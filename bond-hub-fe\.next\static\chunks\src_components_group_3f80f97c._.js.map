{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/src/components/group/CreateGroupDialog.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState, useEffect, use<PERSON><PERSON>back, useMemo } from \"react\";\r\nimport {\r\n  <PERSON><PERSON>,\r\n  <PERSON><PERSON><PERSON>ontent,\r\n  Di<PERSON>Header,\r\n  <PERSON><PERSON><PERSON><PERSON><PERSON>,\r\n  DialogFooter,\r\n} from \"@/components/ui/dialog\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { Input } from \"@/components/ui/input\";\r\nimport { Avatar, AvatarFallback, AvatarImage } from \"@/components/ui/avatar\";\r\nimport { Users, Upload, Search } from \"lucide-react\";\r\nimport { useAuthStore } from \"@/stores/authStore\";\r\nimport { createGroupWithAvatar } from \"@/actions/group.action\";\r\nimport { toast } from \"sonner\";\r\nimport { useFriendStore } from \"@/stores/friendStore\";\r\nimport { useConversationsStore } from \"@/stores/conversationsStore\";\r\nimport { Checkbox } from \"@/components/ui/checkbox\";\r\nimport { ScrollArea } from \"@/components/ui/scroll-area\";\r\nimport { isEmail, isPhoneNumber } from \"@/utils/helpers\";\r\n\r\ninterface CreateGroupDialogProps {\r\n  isOpen: boolean;\r\n  onOpenChange: (open: boolean) => void;\r\n  preSelectedFriendId?: string;\r\n}\r\n\r\nexport default function CreateGroupDialog({\r\n  isOpen,\r\n  onOpenChange,\r\n  preSelectedFriendId,\r\n}: CreateGroupDialogProps) {\r\n  const [groupName, setGroupName] = useState(\"\");\r\n  const [selectedFriends, setSelectedFriends] = useState<string[]>([]);\r\n  const [avatarFile, setAvatarFile] = useState<File | null>(null);\r\n  const [avatarPreview, setAvatarPreview] = useState<string | null>(null);\r\n  const [isLoading, setIsLoading] = useState(false);\r\n\r\n  const { user: currentUser } = useAuthStore();\r\n  const { friends } = useFriendStore();\r\n\r\n  // Memoize callback functions to prevent unnecessary re-renders\r\n  const handleAvatarChange = useCallback(\r\n    (e: React.ChangeEvent<HTMLInputElement>) => {\r\n      const file = e.target.files?.[0];\r\n      if (file) {\r\n        setAvatarFile(file);\r\n        const reader = new FileReader();\r\n        reader.onloadend = () => {\r\n          setAvatarPreview(reader.result as string);\r\n        };\r\n        reader.readAsDataURL(file);\r\n      }\r\n    },\r\n    [],\r\n  );\r\n\r\n  const handleFriendSelection = useCallback((friendId: string) => {\r\n    setSelectedFriends((prev) =>\r\n      prev.includes(friendId)\r\n        ? prev.filter((id) => id !== friendId)\r\n        : [...prev, friendId],\r\n    );\r\n  }, []);\r\n\r\n  const handleCreateGroup = useCallback(async () => {\r\n    if (!groupName.trim()) {\r\n      toast.error(\"Vui lòng nhập tên nhóm\");\r\n      return;\r\n    }\r\n\r\n    if (selectedFriends.length < 2) {\r\n      toast.error(\r\n        \"Vui lòng chọn ít nhất 2 thành viên (nhóm phải có tối thiểu 3 người kể cả bạn)\",\r\n      );\r\n      return;\r\n    }\r\n\r\n    setIsLoading(true);\r\n    try {\r\n      // Chuyển đổi danh sách ID thành viên thành định dạng mới\r\n      const initialMembers = selectedFriends.map((userId) => ({\r\n        userId: userId,\r\n        // addedById sẽ được thêm tự động trong createGroupWithAvatar\r\n      }));\r\n\r\n      // Kiểm tra currentUser có tồn tại không\r\n      if (!currentUser || !currentUser.id) {\r\n        toast.error(\"Bạn cần đăng nhập để tạo nhóm\");\r\n        setIsLoading(false);\r\n        return;\r\n      }\r\n\r\n      // Gọi API tạo nhóm với avatar trong một lần gọi duy nhất\r\n      const result = await createGroupWithAvatar(\r\n        groupName.trim(),\r\n        currentUser.id,\r\n        initialMembers,\r\n        avatarFile || undefined,\r\n      );\r\n\r\n      if (result.success && result.group) {\r\n        // Đóng dialog trước\r\n        onOpenChange(false);\r\n\r\n        // Thông báo thành công\r\n        toast.success(\"Tạo nhóm thành công\");\r\n\r\n        // Reset form\r\n        setGroupName(\"\");\r\n        setSelectedFriends([]);\r\n        setAvatarFile(null);\r\n        setAvatarPreview(null);\r\n\r\n        // Backend sẽ tự động gửi socket events cho tất cả thành viên\r\n        // Chỉ cần reload conversations để cập nhật UI\r\n        if (currentUser?.id) {\r\n          console.log(\"Group created successfully:\", {\r\n            id: result.group.id,\r\n            name: result.group.name,\r\n            type: \"GROUP\",\r\n          });\r\n\r\n          // Reload conversations sau khi tạo nhóm thành công\r\n          setTimeout(() => {\r\n            const conversationsStore = useConversationsStore.getState();\r\n            conversationsStore.loadConversations(currentUser.id);\r\n          }, 500);\r\n        }\r\n      } else {\r\n        toast.error(result.error || \"Không thể tạo nhóm\");\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error creating group:\", error);\r\n      toast.error(\"Đã xảy ra lỗi khi tạo nhóm\");\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  }, [groupName, selectedFriends, avatarFile, currentUser, onOpenChange]);\r\n\r\n  // State for search query\r\n  const [searchQuery, setSearchQuery] = useState(\"\");\r\n\r\n  // Pre-select friend if provided and reset when dialog opens/closes\r\n  useEffect(() => {\r\n    // Reset selected friends when dialog opens/closes\r\n    setSelectedFriends([]);\r\n\r\n    // Only add preSelectedFriendId if dialog is open\r\n    if (\r\n      isOpen &&\r\n      preSelectedFriendId &&\r\n      friends.some((friend) => friend.id === preSelectedFriendId)\r\n    ) {\r\n      setSelectedFriends([preSelectedFriendId]);\r\n    }\r\n  }, [isOpen, preSelectedFriendId, friends]);\r\n\r\n  // Use useMemo for filtered friends to avoid recalculating on every render\r\n  const filteredFriends = useMemo(() => {\r\n    if (!searchQuery.trim()) {\r\n      return friends;\r\n    }\r\n\r\n    // Check if search query is a phone number or email\r\n    const isPhone = isPhoneNumber(searchQuery);\r\n    const isEmailValue = isEmail(searchQuery);\r\n\r\n    // Filter friends based on search query\r\n    return friends.filter((friend) => {\r\n      // Search by phone number\r\n      if (isPhone && friend.phoneNumber) {\r\n        return friend.phoneNumber.includes(searchQuery);\r\n      }\r\n      // Search by email\r\n      if (isEmailValue && friend.email) {\r\n        return friend.email.toLowerCase().includes(searchQuery.toLowerCase());\r\n      }\r\n      // Search by name (default)\r\n      return friend.fullName.toLowerCase().includes(searchQuery.toLowerCase());\r\n    });\r\n  }, [searchQuery, friends]);\r\n\r\n  return (\r\n    <Dialog open={isOpen} onOpenChange={onOpenChange}>\r\n      <DialogContent className=\"sm:max-w-[500px]\">\r\n        <DialogHeader>\r\n          <DialogTitle className=\"text-center text-lg font-semibold\">\r\n            Tạo nhóm\r\n          </DialogTitle>\r\n        </DialogHeader>\r\n\r\n        <div className=\"space-y-4 py-2\">\r\n          {/* Group avatar upload */}\r\n          <div className=\"flex flex-row w-full items-end justify-center\">\r\n            <div className=\"relative\">\r\n              <Avatar className=\"h-12 w-12 cursor-pointer\">\r\n                {avatarPreview ? (\r\n                  <AvatarImage src={avatarPreview} alt=\"Group avatar\" />\r\n                ) : (\r\n                  <>\r\n                    <AvatarFallback className=\"bg-gray-200\">\r\n                      <Users className=\"h-8 w-8 text-gray-400\" />\r\n                    </AvatarFallback>\r\n                  </>\r\n                )}\r\n              </Avatar>\r\n              <label\r\n                htmlFor=\"avatar-upload\"\r\n                className=\"absolute bottom-0 right-0 bg-blue-500 text-white p-1 rounded-full cursor-pointer\"\r\n              >\r\n                <Upload className=\"h-4 w-4\" />\r\n                <input\r\n                  id=\"avatar-upload\"\r\n                  type=\"file\"\r\n                  accept=\"image/*\"\r\n                  className=\"hidden\"\r\n                  onChange={handleAvatarChange}\r\n                />\r\n              </label>\r\n            </div>\r\n            {/* Group name input */}\r\n            <div className=\"ml-2 w-full border-b\">\r\n              <Input\r\n                value={groupName}\r\n                onChange={(e) => setGroupName(e.target.value)}\r\n                placeholder=\"Nhập tên nhóm...\"\r\n                className=\"w-full !border-none focus-visible:ring-0 focus-visible:ring-offset-0\"\r\n              />\r\n            </div>\r\n          </div>\r\n\r\n          {/* Search input */}\r\n          <div className=\"relative\">\r\n            <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\r\n              <Search className=\"h-4 w-4 text-gray-400\" />\r\n            </div>\r\n            <Input\r\n              type=\"text\"\r\n              placeholder=\"Nhập tên, số điện thoại, hoặc danh sách số điện thoại\"\r\n              className=\"pl-10 w-full text-xs\"\r\n              value={searchQuery}\r\n              onChange={(e) => setSearchQuery(e.target.value)}\r\n            />\r\n          </div>\r\n\r\n          {/* Friend selection */}\r\n          <div>\r\n            <div className=\"border rounded-md\">\r\n              <div className=\"p-2 border-b flex items-center justify-between\">\r\n                <span className=\"text-sm font-medium\">Trò chuyện gần đây</span>\r\n                <span className=\"text-sm text-blue-500\">\r\n                  Đã chọn: {selectedFriends.length}{\" \"}\r\n                  <span className=\"text-xs text-gray-500\">(tối thiểu 2)</span>\r\n                </span>\r\n              </div>\r\n\r\n              <ScrollArea className=\"h-[200px]\">\r\n                {filteredFriends.length > 0 ? (\r\n                  <div>\r\n                    {filteredFriends.map((friend) => (\r\n                      <div\r\n                        key={friend.id}\r\n                        className=\"flex items-center py-2 px-3 hover:bg-gray-50 border-b border-gray-100\"\r\n                      >\r\n                        <div className=\"flex items-center w-full\">\r\n                          <Checkbox\r\n                            id={`friend-${friend.id}`}\r\n                            checked={selectedFriends.includes(friend.id)}\r\n                            onCheckedChange={() =>\r\n                              handleFriendSelection(friend.id)\r\n                            }\r\n                            className=\"mr-3\"\r\n                          />\r\n                          <Avatar className=\"h-10 w-10 mr-3\">\r\n                            <AvatarImage\r\n                              src={friend.profilePictureUrl || undefined}\r\n                              alt={friend.fullName || \"\"}\r\n                            />\r\n                            <AvatarFallback>\r\n                              {friend.fullName?.charAt(0) || \"U\"}\r\n                            </AvatarFallback>\r\n                          </Avatar>\r\n                          <span className=\"text-sm font-medium\">\r\n                            {friend.fullName}\r\n                          </span>\r\n                        </div>\r\n                      </div>\r\n                    ))}\r\n                  </div>\r\n                ) : (\r\n                  <div className=\"p-4 text-center text-gray-500\">\r\n                    <p>Không tìm thấy kết quả</p>\r\n                  </div>\r\n                )}\r\n              </ScrollArea>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <DialogFooter>\r\n          <Button\r\n            variant=\"outline\"\r\n            onClick={() => onOpenChange(false)}\r\n            disabled={isLoading}\r\n            className=\"mr-2\"\r\n          >\r\n            Hủy\r\n          </Button>\r\n          <Button\r\n            onClick={handleCreateGroup}\r\n            disabled={\r\n              isLoading || !groupName.trim() || selectedFriends.length < 2\r\n            }\r\n            className=\"bg-blue-500 hover:bg-blue-600\"\r\n          >\r\n            {isLoading ? (\r\n              <>\r\n                <div className=\"h-4 w-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2\"></div>\r\n                Đang tạo...\r\n              </>\r\n            ) : (\r\n              \"Tạo nhóm\"\r\n            )}\r\n          </Button>\r\n        </DialogFooter>\r\n      </DialogContent>\r\n    </Dialog>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAOA;AACA;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AArBA;;;;;;;;;;;;;;;AA6Be,SAAS,kBAAkB,EACxC,MAAM,EACN,YAAY,EACZ,mBAAmB,EACI;;IACvB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACnE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAC1D,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,EAAE,MAAM,WAAW,EAAE,GAAG,CAAA,GAAA,6HAAA,CAAA,eAAY,AAAD;IACzC,MAAM,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,iBAAc,AAAD;IAEjC,+DAA+D;IAC/D,MAAM,qBAAqB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;6DACnC,CAAC;YACC,MAAM,OAAO,EAAE,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE;YAChC,IAAI,MAAM;gBACR,cAAc;gBACd,MAAM,SAAS,IAAI;gBACnB,OAAO,SAAS;yEAAG;wBACjB,iBAAiB,OAAO,MAAM;oBAChC;;gBACA,OAAO,aAAa,CAAC;YACvB;QACF;4DACA,EAAE;IAGJ,MAAM,wBAAwB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;gEAAE,CAAC;YACzC;wEAAmB,CAAC,OAClB,KAAK,QAAQ,CAAC,YACV,KAAK,MAAM;gFAAC,CAAC,KAAO,OAAO;iFAC3B;2BAAI;wBAAM;qBAAS;;QAE3B;+DAAG,EAAE;IAEL,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;4DAAE;YACpC,IAAI,CAAC,UAAU,IAAI,IAAI;gBACrB,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ;YACF;YAEA,IAAI,gBAAgB,MAAM,GAAG,GAAG;gBAC9B,2IAAA,CAAA,QAAK,CAAC,KAAK,CACT;gBAEF;YACF;YAEA,aAAa;YACb,IAAI;gBACF,yDAAyD;gBACzD,MAAM,iBAAiB,gBAAgB,GAAG;uFAAC,CAAC,SAAW,CAAC;4BACtD,QAAQ;wBAEV,CAAC;;gBAED,wCAAwC;gBACxC,IAAI,CAAC,eAAe,CAAC,YAAY,EAAE,EAAE;oBACnC,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;oBACZ,aAAa;oBACb;gBACF;gBAEA,yDAAyD;gBACzD,MAAM,SAAS,MAAM,CAAA,GAAA,oIAAA,CAAA,wBAAqB,AAAD,EACvC,UAAU,IAAI,IACd,YAAY,EAAE,EACd,gBACA,cAAc;gBAGhB,IAAI,OAAO,OAAO,IAAI,OAAO,KAAK,EAAE;oBAClC,oBAAoB;oBACpB,aAAa;oBAEb,uBAAuB;oBACvB,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;oBAEd,aAAa;oBACb,aAAa;oBACb,mBAAmB,EAAE;oBACrB,cAAc;oBACd,iBAAiB;oBAEjB,6DAA6D;oBAC7D,8CAA8C;oBAC9C,IAAI,aAAa,IAAI;wBACnB,QAAQ,GAAG,CAAC,+BAA+B;4BACzC,IAAI,OAAO,KAAK,CAAC,EAAE;4BACnB,MAAM,OAAO,KAAK,CAAC,IAAI;4BACvB,MAAM;wBACR;wBAEA,mDAAmD;wBACnD;gFAAW;gCACT,MAAM,qBAAqB,sIAAA,CAAA,wBAAqB,CAAC,QAAQ;gCACzD,mBAAmB,iBAAiB,CAAC,YAAY,EAAE;4BACrD;+EAAG;oBACL;gBACF,OAAO;oBACL,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,OAAO,KAAK,IAAI;gBAC9B;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,yBAAyB;gBACvC,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd,SAAU;gBACR,aAAa;YACf;QACF;2DAAG;QAAC;QAAW;QAAiB;QAAY;QAAa;KAAa;IAEtE,yBAAyB;IACzB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,mEAAmE;IACnE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR,kDAAkD;YAClD,mBAAmB,EAAE;YAErB,iDAAiD;YACjD,IACE,UACA,uBACA,QAAQ,IAAI;+CAAC,CAAC,SAAW,OAAO,EAAE,KAAK;+CACvC;gBACA,mBAAmB;oBAAC;iBAAoB;YAC1C;QACF;sCAAG;QAAC;QAAQ;QAAqB;KAAQ;IAEzC,0EAA0E;IAC1E,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;sDAAE;YAC9B,IAAI,CAAC,YAAY,IAAI,IAAI;gBACvB,OAAO;YACT;YAEA,mDAAmD;YACnD,MAAM,UAAU,CAAA,GAAA,0HAAA,CAAA,gBAAa,AAAD,EAAE;YAC9B,MAAM,eAAe,CAAA,GAAA,0HAAA,CAAA,UAAO,AAAD,EAAE;YAE7B,uCAAuC;YACvC,OAAO,QAAQ,MAAM;8DAAC,CAAC;oBACrB,yBAAyB;oBACzB,IAAI,WAAW,OAAO,WAAW,EAAE;wBACjC,OAAO,OAAO,WAAW,CAAC,QAAQ,CAAC;oBACrC;oBACA,kBAAkB;oBAClB,IAAI,gBAAgB,OAAO,KAAK,EAAE;wBAChC,OAAO,OAAO,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW;oBACpE;oBACA,2BAA2B;oBAC3B,OAAO,OAAO,QAAQ,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW;gBACvE;;QACF;qDAAG;QAAC;QAAa;KAAQ;IAEzB,qBACE,6LAAC,qIAAA,CAAA,SAAM;QAAC,MAAM;QAAQ,cAAc;kBAClC,cAAA,6LAAC,qIAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,6LAAC,qIAAA,CAAA,eAAY;8BACX,cAAA,6LAAC,qIAAA,CAAA,cAAW;wBAAC,WAAU;kCAAoC;;;;;;;;;;;8BAK7D,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,qIAAA,CAAA,SAAM;4CAAC,WAAU;sDACf,8BACC,6LAAC,qIAAA,CAAA,cAAW;gDAAC,KAAK;gDAAe,KAAI;;;;;qEAErC;0DACE,cAAA,6LAAC,qIAAA,CAAA,iBAAc;oDAAC,WAAU;8DACxB,cAAA,6LAAC,uMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;;;;;;;;;;;;sDAKzB,6LAAC;4CACC,SAAQ;4CACR,WAAU;;8DAEV,6LAAC,yMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,6LAAC;oDACC,IAAG;oDACH,MAAK;oDACL,QAAO;oDACP,WAAU;oDACV,UAAU;;;;;;;;;;;;;;;;;;8CAKhB,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,oIAAA,CAAA,QAAK;wCACJ,OAAO;wCACP,UAAU,CAAC,IAAM,aAAa,EAAE,MAAM,CAAC,KAAK;wCAC5C,aAAY;wCACZ,WAAU;;;;;;;;;;;;;;;;;sCAMhB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,yMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;;;;;;8CAEpB,6LAAC,oIAAA,CAAA,QAAK;oCACJ,MAAK;oCACL,aAAY;oCACZ,WAAU;oCACV,OAAO;oCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;sCAKlD,6LAAC;sCACC,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;0DAAsB;;;;;;0DACtC,6LAAC;gDAAK,WAAU;;oDAAwB;oDAC5B,gBAAgB,MAAM;oDAAE;kEAClC,6LAAC;wDAAK,WAAU;kEAAwB;;;;;;;;;;;;;;;;;;kDAI5C,6LAAC,6IAAA,CAAA,aAAU;wCAAC,WAAU;kDACnB,gBAAgB,MAAM,GAAG,kBACxB,6LAAC;sDACE,gBAAgB,GAAG,CAAC,CAAC,uBACpB,6LAAC;oDAEC,WAAU;8DAEV,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,uIAAA,CAAA,WAAQ;gEACP,IAAI,CAAC,OAAO,EAAE,OAAO,EAAE,EAAE;gEACzB,SAAS,gBAAgB,QAAQ,CAAC,OAAO,EAAE;gEAC3C,iBAAiB,IACf,sBAAsB,OAAO,EAAE;gEAEjC,WAAU;;;;;;0EAEZ,6LAAC,qIAAA,CAAA,SAAM;gEAAC,WAAU;;kFAChB,6LAAC,qIAAA,CAAA,cAAW;wEACV,KAAK,OAAO,iBAAiB,IAAI;wEACjC,KAAK,OAAO,QAAQ,IAAI;;;;;;kFAE1B,6LAAC,qIAAA,CAAA,iBAAc;kFACZ,OAAO,QAAQ,EAAE,OAAO,MAAM;;;;;;;;;;;;0EAGnC,6LAAC;gEAAK,WAAU;0EACb,OAAO,QAAQ;;;;;;;;;;;;mDAtBf,OAAO,EAAE;;;;;;;;;iEA6BpB,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;0DAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQf,6LAAC,qIAAA,CAAA,eAAY;;sCACX,6LAAC,qIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,SAAS,IAAM,aAAa;4BAC5B,UAAU;4BACV,WAAU;sCACX;;;;;;sCAGD,6LAAC,qIAAA,CAAA,SAAM;4BACL,SAAS;4BACT,UACE,aAAa,CAAC,UAAU,IAAI,MAAM,gBAAgB,MAAM,GAAG;4BAE7D,WAAU;sCAET,0BACC;;kDACE,6LAAC;wCAAI,WAAU;;;;;;oCAA0F;;+CAI3G;;;;;;;;;;;;;;;;;;;;;;;AAOd;GA9SwB;;QAWQ,6HAAA,CAAA,eAAY;QACtB,+HAAA,CAAA,iBAAc;;;KAZZ", "debugId": null}}, {"offset": {"line": 562, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/src/components/group/GroupChatHeaderSocketHandler.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useEffect } from \"react\";\nimport { useAuthStore } from \"@/stores/authStore\";\nimport { useChatStore } from \"@/stores/chatStore\";\nimport { useConversationsStore } from \"@/stores/conversationsStore\";\nimport { useGroupSocket } from \"@/hooks/useGroupSocket\";\n\n// Define types for socket events\ninterface MemberAddedEventData {\n  groupId: string;\n  userId: string;\n  addedById: string;\n  timestamp: Date;\n}\n\ninterface MemberRemovedEventData {\n  groupId: string;\n  userId: string;\n  removedById: string;\n  timestamp: Date;\n}\n\ninterface MemberRoleUpdatedEventData {\n  groupId: string;\n  userId: string;\n  updatedById: string;\n  newRole: string;\n  timestamp: Date;\n}\n\ninterface GroupUpdatedEventData {\n  groupId: string;\n  updatedBy: string;\n  timestamp: Date;\n}\n\n/**\n * GroupChatHeaderSocketHandler component\n *\n * This component is responsible for handling socket events specifically for the GroupChatHeader component\n * It listens for group-related events and updates the member count and group info accordingly\n */\nconst GroupChatHeaderSocketHandler = ({\n  groupId,\n  onGroupUpdated,\n}: {\n  groupId: string;\n  onGroupUpdated?: () => void;\n}) => {\n  const currentUser = useAuthStore((state) => state.user);\n  const { socket } = useGroupSocket();\n  const { selectedGroup, refreshSelectedGroup } = useChatStore();\n  const { updateConversation } = useConversationsStore();\n\n  useEffect(() => {\n    if (!socket || !groupId) {\n      console.log(\n        \"[GroupChatHeaderSocketHandler] Socket or groupId not available, skipping event setup\",\n      );\n      return;\n    }\n\n    console.log(\n      `[GroupChatHeaderSocketHandler] Setting up socket event listeners for group ${groupId}`,\n    );\n\n    const handleGroupUpdated = (data: GroupUpdatedEventData) => {\n      if (data.groupId === groupId) {\n        console.log(\n          `[GroupChatHeaderSocketHandler] Group updated for ${groupId}, refreshing header data`,\n        );\n\n        // If this is the currently selected group, refresh it\n        if (selectedGroup && selectedGroup.id === groupId) {\n          refreshSelectedGroup();\n        }\n\n        // Call the onGroupUpdated callback if provided\n        if (onGroupUpdated) {\n          onGroupUpdated();\n        }\n\n        // Force update conversations to ensure header reflects changes\n        setTimeout(() => {\n          useConversationsStore.getState().forceUpdate();\n        }, 100);\n      }\n    };\n\n    const handleMemberAdded = (data: MemberAddedEventData) => {\n      if (data.groupId === groupId) {\n        console.log(\n          `[GroupChatHeaderSocketHandler] Member added to group ${groupId}, updating header`,\n        );\n\n        // If this is the currently selected group, refresh it\n        if (selectedGroup && selectedGroup.id === groupId) {\n          refreshSelectedGroup();\n        }\n\n        // Call the onGroupUpdated callback if provided\n        if (onGroupUpdated) {\n          onGroupUpdated();\n        }\n\n        // Force update conversations to ensure header reflects changes\n        setTimeout(() => {\n          useConversationsStore.getState().forceUpdate();\n        }, 100);\n      }\n    };\n\n    const handleMemberRemoved = (data: MemberRemovedEventData) => {\n      if (data.groupId === groupId) {\n        console.log(\n          `[GroupChatHeaderSocketHandler] Member removed from group ${groupId}, updating header`,\n        );\n\n        // Check if the current user was removed\n        if (data.userId === currentUser?.id) {\n          console.log(\n            `[GroupChatHeaderSocketHandler] Current user was removed from group ${groupId}`,\n          );\n          // The main GroupSocketHandler will handle this case\n          return;\n        }\n\n        // If this is the currently selected group, refresh it\n        if (selectedGroup && selectedGroup.id === groupId) {\n          refreshSelectedGroup();\n        }\n\n        // Call the onGroupUpdated callback if provided\n        if (onGroupUpdated) {\n          onGroupUpdated();\n        }\n\n        // Force update conversations to ensure header reflects changes\n        setTimeout(() => {\n          useConversationsStore.getState().forceUpdate();\n        }, 100);\n      }\n    };\n\n    const handleMemberRoleUpdated = (data: MemberRoleUpdatedEventData) => {\n      if (data.groupId === groupId) {\n        console.log(\n          `[GroupChatHeaderSocketHandler] Member role updated in group ${groupId}, updating header`,\n        );\n\n        // If this is the currently selected group, refresh it\n        if (selectedGroup && selectedGroup.id === groupId) {\n          refreshSelectedGroup();\n        }\n\n        // Call the onGroupUpdated callback if provided\n        if (onGroupUpdated) {\n          onGroupUpdated();\n        }\n\n        // Force update conversations to ensure header reflects changes\n        setTimeout(() => {\n          useConversationsStore.getState().forceUpdate();\n        }, 100);\n      }\n    };\n\n    // Register event listeners\n    socket.on(\"groupUpdated\", handleGroupUpdated);\n    socket.on(\"memberAdded\", handleMemberAdded);\n    socket.on(\"memberRemoved\", handleMemberRemoved);\n    socket.on(\"memberRoleUpdated\", handleMemberRoleUpdated);\n    socket.on(\"roleChanged\", handleMemberRoleUpdated); // Legacy event\n\n    // Cleanup on unmount\n    return () => {\n      console.log(\n        `[GroupChatHeaderSocketHandler] Cleaning up socket event listeners for group ${groupId}`,\n      );\n      socket.off(\"groupUpdated\", handleGroupUpdated);\n      socket.off(\"memberAdded\", handleMemberAdded);\n      socket.off(\"memberRemoved\", handleMemberRemoved);\n      socket.off(\"memberRoleUpdated\", handleMemberRoleUpdated);\n      socket.off(\"roleChanged\", handleMemberRoleUpdated);\n    };\n  }, [\n    socket,\n    groupId,\n    currentUser?.id,\n    selectedGroup,\n    refreshSelectedGroup,\n    updateConversation,\n    onGroupUpdated,\n  ]);\n\n  // This component doesn't render anything\n  return null;\n};\n\nexport default GroupChatHeaderSocketHandler;\n"], "names": [], "mappings": ";;;AAEA;AACA;AACA;AACA;AACA;;AANA;;;;;;AAqCA;;;;;CAKC,GACD,MAAM,+BAA+B,CAAC,EACpC,OAAO,EACP,cAAc,EAIf;;IACC,MAAM,cAAc,CAAA,GAAA,6HAAA,CAAA,eAAY,AAAD;kEAAE,CAAC,QAAU,MAAM,IAAI;;IACtD,MAAM,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,iBAAc,AAAD;IAChC,MAAM,EAAE,aAAa,EAAE,oBAAoB,EAAE,GAAG,CAAA,GAAA,6HAAA,CAAA,eAAY,AAAD;IAC3D,MAAM,EAAE,kBAAkB,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,wBAAqB,AAAD;IAEnD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kDAAE;YACR,IAAI,CAAC,UAAU,CAAC,SAAS;gBACvB,QAAQ,GAAG,CACT;gBAEF;YACF;YAEA,QAAQ,GAAG,CACT,CAAC,2EAA2E,EAAE,SAAS;YAGzF,MAAM;6EAAqB,CAAC;oBAC1B,IAAI,KAAK,OAAO,KAAK,SAAS;wBAC5B,QAAQ,GAAG,CACT,CAAC,iDAAiD,EAAE,QAAQ,wBAAwB,CAAC;wBAGvF,sDAAsD;wBACtD,IAAI,iBAAiB,cAAc,EAAE,KAAK,SAAS;4BACjD;wBACF;wBAEA,+CAA+C;wBAC/C,IAAI,gBAAgB;4BAClB;wBACF;wBAEA,+DAA+D;wBAC/D;yFAAW;gCACT,sIAAA,CAAA,wBAAqB,CAAC,QAAQ,GAAG,WAAW;4BAC9C;wFAAG;oBACL;gBACF;;YAEA,MAAM;4EAAoB,CAAC;oBACzB,IAAI,KAAK,OAAO,KAAK,SAAS;wBAC5B,QAAQ,GAAG,CACT,CAAC,qDAAqD,EAAE,QAAQ,iBAAiB,CAAC;wBAGpF,sDAAsD;wBACtD,IAAI,iBAAiB,cAAc,EAAE,KAAK,SAAS;4BACjD;wBACF;wBAEA,+CAA+C;wBAC/C,IAAI,gBAAgB;4BAClB;wBACF;wBAEA,+DAA+D;wBAC/D;wFAAW;gCACT,sIAAA,CAAA,wBAAqB,CAAC,QAAQ,GAAG,WAAW;4BAC9C;uFAAG;oBACL;gBACF;;YAEA,MAAM;8EAAsB,CAAC;oBAC3B,IAAI,KAAK,OAAO,KAAK,SAAS;wBAC5B,QAAQ,GAAG,CACT,CAAC,yDAAyD,EAAE,QAAQ,iBAAiB,CAAC;wBAGxF,wCAAwC;wBACxC,IAAI,KAAK,MAAM,KAAK,aAAa,IAAI;4BACnC,QAAQ,GAAG,CACT,CAAC,mEAAmE,EAAE,SAAS;4BAEjF,oDAAoD;4BACpD;wBACF;wBAEA,sDAAsD;wBACtD,IAAI,iBAAiB,cAAc,EAAE,KAAK,SAAS;4BACjD;wBACF;wBAEA,+CAA+C;wBAC/C,IAAI,gBAAgB;4BAClB;wBACF;wBAEA,+DAA+D;wBAC/D;0FAAW;gCACT,sIAAA,CAAA,wBAAqB,CAAC,QAAQ,GAAG,WAAW;4BAC9C;yFAAG;oBACL;gBACF;;YAEA,MAAM;kFAA0B,CAAC;oBAC/B,IAAI,KAAK,OAAO,KAAK,SAAS;wBAC5B,QAAQ,GAAG,CACT,CAAC,4DAA4D,EAAE,QAAQ,iBAAiB,CAAC;wBAG3F,sDAAsD;wBACtD,IAAI,iBAAiB,cAAc,EAAE,KAAK,SAAS;4BACjD;wBACF;wBAEA,+CAA+C;wBAC/C,IAAI,gBAAgB;4BAClB;wBACF;wBAEA,+DAA+D;wBAC/D;8FAAW;gCACT,sIAAA,CAAA,wBAAqB,CAAC,QAAQ,GAAG,WAAW;4BAC9C;6FAAG;oBACL;gBACF;;YAEA,2BAA2B;YAC3B,OAAO,EAAE,CAAC,gBAAgB;YAC1B,OAAO,EAAE,CAAC,eAAe;YACzB,OAAO,EAAE,CAAC,iBAAiB;YAC3B,OAAO,EAAE,CAAC,qBAAqB;YAC/B,OAAO,EAAE,CAAC,eAAe,0BAA0B,eAAe;YAElE,qBAAqB;YACrB;0DAAO;oBACL,QAAQ,GAAG,CACT,CAAC,4EAA4E,EAAE,SAAS;oBAE1F,OAAO,GAAG,CAAC,gBAAgB;oBAC3B,OAAO,GAAG,CAAC,eAAe;oBAC1B,OAAO,GAAG,CAAC,iBAAiB;oBAC5B,OAAO,GAAG,CAAC,qBAAqB;oBAChC,OAAO,GAAG,CAAC,eAAe;gBAC5B;;QACF;iDAAG;QACD;QACA;QACA,aAAa;QACb;QACA;QACA;QACA;KACD;IAED,yCAAyC;IACzC,OAAO;AACT;GA3JM;;QAOgB,6HAAA,CAAA,eAAY;QACb,iIAAA,CAAA,iBAAc;QACe,6HAAA,CAAA,eAAY;QAC7B,sIAAA,CAAA,wBAAqB;;;KAVhD;uCA6JS", "debugId": null}}, {"offset": {"line": 738, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/src/components/group/GroupInfoSocketHandler.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useEffect } from \"react\";\nimport { useAuthStore } from \"@/stores/authStore\";\nimport { useChatStore } from \"@/stores/chatStore\";\nimport { useConversationsStore } from \"@/stores/conversationsStore\";\nimport { useGroupSocket } from \"@/hooks/useGroupSocket\";\n\n// Define interfaces for socket event data\ninterface GroupUpdatedEventData {\n  groupId: string;\n  updatedBy?: string;\n  timestamp?: Date;\n  [key: string]: any; // Allow for additional properties\n}\n\ninterface MemberEventData {\n  groupId: string;\n  userId: string;\n  addedById?: string;\n  removedById?: string;\n  timestamp?: Date;\n  [key: string]: any; // Allow for additional properties\n}\n\ninterface RoleChangedEventData {\n  groupId: string;\n  userId: string;\n  newRole?: string;\n  oldRole?: string;\n  updatedBy?: string;\n  timestamp?: Date;\n  [key: string]: any; // Allow for additional properties\n}\n\ninterface AvatarUpdatedEventData {\n  groupId: string;\n  updatedBy?: string;\n  avatarUrl?: string;\n  newAvatarUrl?: string;\n  timestamp?: Date;\n  [key: string]: any; // Allow for additional properties\n}\n\n/**\n * GroupInfoSocketHandler component\n *\n * This component is responsible for handling socket events specifically for the GroupInfo component\n * It listens for group-related events and updates the GroupInfo component accordingly\n */\nconst GroupInfoSocketHandler = ({\n  groupId,\n  onGroupUpdated,\n}: {\n  groupId: string;\n  onGroupUpdated?: () => void;\n}) => {\n  const currentUser = useAuthStore((state) => state.user);\n  const { socket, joinGroupRoom } = useGroupSocket();\n  const { selectedGroup, setSelectedGroup } = useChatStore();\n\n  // Join the group room when the component mounts or groupId changes\n  useEffect(() => {\n    if (groupId && socket) {\n      console.log(`[GroupInfoSocketHandler] Joining group room: ${groupId}`);\n      joinGroupRoom(groupId);\n\n      // We don't need to force update conversations here anymore\n      // The data should already be available in the cache\n    }\n  }, [groupId, socket, joinGroupRoom]);\n\n  // Listen for group-related events\n  useEffect(() => {\n    if (!socket || !groupId) return;\n\n    const handleGroupUpdated = (data: GroupUpdatedEventData) => {\n      if (data.groupId === groupId) {\n        console.log(\n          `[GroupInfoSocketHandler] Group ${groupId} updated, refreshing data`,\n        );\n\n        // Refresh group member cache to ensure user info is up to date\n        const conversationsStore = useConversationsStore.getState();\n        conversationsStore.refreshGroupMemberCache(data.groupId);\n\n        // Thêm throttle để tránh gọi onGroupUpdated quá thường xuyên\n        // Sử dụng biến toàn cục để theo dõi thời gian gọi cuối cùng\n        if (!window._lastGroupInfoUpdateTime) {\n          window._lastGroupInfoUpdateTime = {};\n        }\n\n        const now = Date.now();\n        const lastUpdateTime = window._lastGroupInfoUpdateTime[groupId] || 0;\n        const timeSinceLastUpdate = now - lastUpdateTime;\n\n        // Nếu đã gọi trong vòng 5 giây, bỏ qua để giảm lag\n        if (timeSinceLastUpdate < 5000) {\n          console.log(\n            `[GroupInfoSocketHandler] Skipping update, last update was ${timeSinceLastUpdate}ms ago`,\n          );\n          return;\n        }\n\n        // Cập nhật thời gian gọi cuối cùng\n        window._lastGroupInfoUpdateTime[groupId] = now;\n\n        // Call the onGroupUpdated callback if provided\n        // This will use the cache system to avoid redundant API calls\n        if (onGroupUpdated) {\n          onGroupUpdated();\n        }\n      }\n    };\n\n    const handleMemberAdded = (data: MemberEventData) => {\n      if (data.groupId === groupId) {\n        console.log(\n          `[GroupInfoSocketHandler] Member added to group ${groupId}, refreshing data`,\n        );\n\n        // Thêm throttle để tránh gọi onGroupUpdated quá thường xuyên\n        if (!window._lastGroupInfoUpdateTime) {\n          window._lastGroupInfoUpdateTime = {};\n        }\n\n        const now = Date.now();\n        const lastUpdateTime = window._lastGroupInfoUpdateTime[groupId] || 0;\n        const timeSinceLastUpdate = now - lastUpdateTime;\n\n        // Nếu đã gọi trong vòng 2 giây, bỏ qua\n        if (timeSinceLastUpdate < 2000) {\n          console.log(\n            `[GroupInfoSocketHandler] Skipping update, last update was ${timeSinceLastUpdate}ms ago`,\n          );\n          return;\n        }\n\n        // Cập nhật thời gian gọi cuối cùng\n        window._lastGroupInfoUpdateTime[groupId] = now;\n\n        // Call the onGroupUpdated callback if provided\n        // This will use the cache system to avoid redundant API calls\n        if (onGroupUpdated) {\n          onGroupUpdated();\n        }\n\n        // Force update conversations to ensure all components get updated\n        setTimeout(() => {\n          useConversationsStore.getState().forceUpdate();\n        }, 100);\n      }\n    };\n\n    const handleMemberRemoved = (data: MemberEventData) => {\n      if (data.groupId === groupId) {\n        console.log(\n          `[GroupInfoSocketHandler] Member removed from group ${groupId}, refreshing data`,\n        );\n\n        // Check if the current user was removed\n        if (data.userId === currentUser?.id) {\n          console.log(\n            `[GroupInfoSocketHandler] Current user was removed from group ${groupId}`,\n          );\n\n          // If this is the currently selected group, clear it\n          if (selectedGroup && selectedGroup.id === groupId) {\n            setSelectedGroup(null);\n          }\n\n          // Remove the group from conversations\n          useConversationsStore.getState().removeConversation(groupId);\n\n          // Force update conversations\n          setTimeout(() => {\n            useConversationsStore.getState().forceUpdate();\n          }, 100);\n        } else {\n          // Thêm throttle để tránh gọi onGroupUpdated quá thường xuyên\n          if (!window._lastGroupInfoUpdateTime) {\n            window._lastGroupInfoUpdateTime = {};\n          }\n\n          const now = Date.now();\n          const lastUpdateTime = window._lastGroupInfoUpdateTime[groupId] || 0;\n          const timeSinceLastUpdate = now - lastUpdateTime;\n\n          // Nếu đã gọi trong vòng 2 giây, bỏ qua\n          if (timeSinceLastUpdate < 2000) {\n            console.log(\n              `[GroupInfoSocketHandler] Skipping update, last update was ${timeSinceLastUpdate}ms ago`,\n            );\n            return;\n          }\n\n          // Cập nhật thời gian gọi cuối cùng\n          window._lastGroupInfoUpdateTime[groupId] = now;\n\n          // Call the onGroupUpdated callback if provided\n          // This will use the cache system to avoid redundant API calls\n          if (onGroupUpdated) {\n            onGroupUpdated();\n          }\n\n          // Force update conversations to ensure all components get updated\n          setTimeout(() => {\n            useConversationsStore.getState().forceUpdate();\n          }, 100);\n        }\n      }\n    };\n\n    const handleRoleChanged = (data: RoleChangedEventData) => {\n      if (data.groupId === groupId) {\n        console.log(\n          `[GroupInfoSocketHandler] Role changed in group ${groupId}, refreshing data`,\n        );\n\n        // Refresh group member cache to ensure user info is up to date\n        const conversationsStore = useConversationsStore.getState();\n        conversationsStore.refreshGroupMemberCache(data.groupId);\n\n        // Thêm throttle để tránh gọi onGroupUpdated quá thường xuyên\n        if (!window._lastGroupInfoUpdateTime) {\n          window._lastGroupInfoUpdateTime = {};\n        }\n\n        const now = Date.now();\n        const lastUpdateTime = window._lastGroupInfoUpdateTime[groupId] || 0;\n        const timeSinceLastUpdate = now - lastUpdateTime;\n\n        // Nếu đã gọi trong vòng 2 giây, bỏ qua\n        if (timeSinceLastUpdate < 2000) {\n          console.log(\n            `[GroupInfoSocketHandler] Skipping update, last update was ${timeSinceLastUpdate}ms ago`,\n          );\n          return;\n        }\n\n        // Cập nhật thời gian gọi cuối cùng\n        window._lastGroupInfoUpdateTime[groupId] = now;\n\n        // Call the onGroupUpdated callback if provided\n        // This will use the cache system to avoid redundant API calls\n        if (onGroupUpdated) {\n          onGroupUpdated();\n        }\n\n        // Force update conversations to ensure all components get updated\n        setTimeout(() => {\n          useConversationsStore.getState().forceUpdate();\n        }, 100);\n      }\n    };\n\n    const handleAvatarUpdated = (data: AvatarUpdatedEventData) => {\n      if (data.groupId === groupId) {\n        console.log(\n          `[GroupInfoSocketHandler] Avatar updated for group ${groupId}, refreshing data`,\n        );\n\n        // Refresh group member cache to ensure user info is up to date\n        const conversationsStore = useConversationsStore.getState();\n        conversationsStore.refreshGroupMemberCache(data.groupId);\n\n        // If we have avatarUrl in the data, update it directly in the selected group\n        if (data.avatarUrl && selectedGroup && selectedGroup.id === groupId) {\n          // Update the selected group directly\n          useChatStore.getState().setSelectedGroup({\n            ...selectedGroup,\n            avatarUrl: data.avatarUrl,\n          });\n\n          // Also update the cache\n          const chatStore = useChatStore.getState();\n          const cachedData = chatStore.groupCache\n            ? chatStore.groupCache[groupId]\n            : undefined;\n          if (cachedData) {\n            chatStore.groupCache[groupId] = {\n              ...cachedData,\n              group: {\n                ...cachedData.group,\n                avatarUrl: data.avatarUrl,\n              },\n            };\n          }\n        }\n\n        // Thêm throttle để tránh gọi onGroupUpdated quá thường xuyên\n        if (!window._lastGroupInfoUpdateTime) {\n          window._lastGroupInfoUpdateTime = {};\n        }\n\n        const now = Date.now();\n        const lastUpdateTime = window._lastGroupInfoUpdateTime[groupId] || 0;\n        const timeSinceLastUpdate = now - lastUpdateTime;\n\n        // Nếu đã gọi trong vòng 5 giây, bỏ qua để giảm lag\n        if (timeSinceLastUpdate < 5000) {\n          console.log(\n            `[GroupInfoSocketHandler] Skipping update, last update was ${timeSinceLastUpdate}ms ago`,\n          );\n          return;\n        }\n\n        // Cập nhật thời gian gọi cuối cùng\n        window._lastGroupInfoUpdateTime[groupId] = now;\n\n        // Call the onGroupUpdated callback if provided\n        // This will use the cache system to avoid redundant API calls\n        if (onGroupUpdated) {\n          onGroupUpdated();\n        }\n      }\n    };\n\n    // Register event listeners\n    socket.on(\"groupUpdated\", handleGroupUpdated);\n    socket.on(\"memberAdded\", handleMemberAdded);\n    socket.on(\"memberRemoved\", handleMemberRemoved);\n    socket.on(\"roleChanged\", handleRoleChanged);\n    socket.on(\"memberRoleUpdated\", handleRoleChanged); // Legacy event\n    socket.on(\"avatarUpdated\", handleAvatarUpdated);\n\n    // Cleanup on unmount\n    return () => {\n      socket.off(\"groupUpdated\", handleGroupUpdated);\n      socket.off(\"memberAdded\", handleMemberAdded);\n      socket.off(\"memberRemoved\", handleMemberRemoved);\n      socket.off(\"roleChanged\", handleRoleChanged);\n      socket.off(\"memberRoleUpdated\", handleRoleChanged);\n      socket.off(\"avatarUpdated\", handleAvatarUpdated);\n    };\n  }, [\n    socket,\n    groupId,\n    currentUser?.id,\n    selectedGroup,\n    setSelectedGroup,\n    onGroupUpdated,\n  ]);\n\n  // This component doesn't render anything\n  return null;\n};\n\nexport default GroupInfoSocketHandler;\n"], "names": [], "mappings": ";;;AAEA;AACA;AACA;AACA;AACA;;AANA;;;;;;AA4CA;;;;;CAKC,GACD,MAAM,yBAAyB,CAAC,EAC9B,OAAO,EACP,cAAc,EAIf;;IACC,MAAM,cAAc,CAAA,GAAA,6HAAA,CAAA,eAAY,AAAD;4DAAE,CAAC,QAAU,MAAM,IAAI;;IACtD,MAAM,EAAE,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,iBAAc,AAAD;IAC/C,MAAM,EAAE,aAAa,EAAE,gBAAgB,EAAE,GAAG,CAAA,GAAA,6HAAA,CAAA,eAAY,AAAD;IAEvD,mEAAmE;IACnE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4CAAE;YACR,IAAI,WAAW,QAAQ;gBACrB,QAAQ,GAAG,CAAC,CAAC,6CAA6C,EAAE,SAAS;gBACrE,cAAc;YAEd,2DAA2D;YAC3D,oDAAoD;YACtD;QACF;2CAAG;QAAC;QAAS;QAAQ;KAAc;IAEnC,kCAAkC;IAClC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4CAAE;YACR,IAAI,CAAC,UAAU,CAAC,SAAS;YAEzB,MAAM;uEAAqB,CAAC;oBAC1B,IAAI,KAAK,OAAO,KAAK,SAAS;wBAC5B,QAAQ,GAAG,CACT,CAAC,+BAA+B,EAAE,QAAQ,yBAAyB,CAAC;wBAGtE,+DAA+D;wBAC/D,MAAM,qBAAqB,sIAAA,CAAA,wBAAqB,CAAC,QAAQ;wBACzD,mBAAmB,uBAAuB,CAAC,KAAK,OAAO;wBAEvD,6DAA6D;wBAC7D,4DAA4D;wBAC5D,IAAI,CAAC,OAAO,wBAAwB,EAAE;4BACpC,OAAO,wBAAwB,GAAG,CAAC;wBACrC;wBAEA,MAAM,MAAM,KAAK,GAAG;wBACpB,MAAM,iBAAiB,OAAO,wBAAwB,CAAC,QAAQ,IAAI;wBACnE,MAAM,sBAAsB,MAAM;wBAElC,mDAAmD;wBACnD,IAAI,sBAAsB,MAAM;4BAC9B,QAAQ,GAAG,CACT,CAAC,0DAA0D,EAAE,oBAAoB,MAAM,CAAC;4BAE1F;wBACF;wBAEA,mCAAmC;wBACnC,OAAO,wBAAwB,CAAC,QAAQ,GAAG;wBAE3C,+CAA+C;wBAC/C,8DAA8D;wBAC9D,IAAI,gBAAgB;4BAClB;wBACF;oBACF;gBACF;;YAEA,MAAM;sEAAoB,CAAC;oBACzB,IAAI,KAAK,OAAO,KAAK,SAAS;wBAC5B,QAAQ,GAAG,CACT,CAAC,+CAA+C,EAAE,QAAQ,iBAAiB,CAAC;wBAG9E,6DAA6D;wBAC7D,IAAI,CAAC,OAAO,wBAAwB,EAAE;4BACpC,OAAO,wBAAwB,GAAG,CAAC;wBACrC;wBAEA,MAAM,MAAM,KAAK,GAAG;wBACpB,MAAM,iBAAiB,OAAO,wBAAwB,CAAC,QAAQ,IAAI;wBACnE,MAAM,sBAAsB,MAAM;wBAElC,uCAAuC;wBACvC,IAAI,sBAAsB,MAAM;4BAC9B,QAAQ,GAAG,CACT,CAAC,0DAA0D,EAAE,oBAAoB,MAAM,CAAC;4BAE1F;wBACF;wBAEA,mCAAmC;wBACnC,OAAO,wBAAwB,CAAC,QAAQ,GAAG;wBAE3C,+CAA+C;wBAC/C,8DAA8D;wBAC9D,IAAI,gBAAgB;4BAClB;wBACF;wBAEA,kEAAkE;wBAClE;kFAAW;gCACT,sIAAA,CAAA,wBAAqB,CAAC,QAAQ,GAAG,WAAW;4BAC9C;iFAAG;oBACL;gBACF;;YAEA,MAAM;wEAAsB,CAAC;oBAC3B,IAAI,KAAK,OAAO,KAAK,SAAS;wBAC5B,QAAQ,GAAG,CACT,CAAC,mDAAmD,EAAE,QAAQ,iBAAiB,CAAC;wBAGlF,wCAAwC;wBACxC,IAAI,KAAK,MAAM,KAAK,aAAa,IAAI;4BACnC,QAAQ,GAAG,CACT,CAAC,6DAA6D,EAAE,SAAS;4BAG3E,oDAAoD;4BACpD,IAAI,iBAAiB,cAAc,EAAE,KAAK,SAAS;gCACjD,iBAAiB;4BACnB;4BAEA,sCAAsC;4BACtC,sIAAA,CAAA,wBAAqB,CAAC,QAAQ,GAAG,kBAAkB,CAAC;4BAEpD,6BAA6B;4BAC7B;wFAAW;oCACT,sIAAA,CAAA,wBAAqB,CAAC,QAAQ,GAAG,WAAW;gCAC9C;uFAAG;wBACL,OAAO;4BACL,6DAA6D;4BAC7D,IAAI,CAAC,OAAO,wBAAwB,EAAE;gCACpC,OAAO,wBAAwB,GAAG,CAAC;4BACrC;4BAEA,MAAM,MAAM,KAAK,GAAG;4BACpB,MAAM,iBAAiB,OAAO,wBAAwB,CAAC,QAAQ,IAAI;4BACnE,MAAM,sBAAsB,MAAM;4BAElC,uCAAuC;4BACvC,IAAI,sBAAsB,MAAM;gCAC9B,QAAQ,GAAG,CACT,CAAC,0DAA0D,EAAE,oBAAoB,MAAM,CAAC;gCAE1F;4BACF;4BAEA,mCAAmC;4BACnC,OAAO,wBAAwB,CAAC,QAAQ,GAAG;4BAE3C,+CAA+C;4BAC/C,8DAA8D;4BAC9D,IAAI,gBAAgB;gCAClB;4BACF;4BAEA,kEAAkE;4BAClE;wFAAW;oCACT,sIAAA,CAAA,wBAAqB,CAAC,QAAQ,GAAG,WAAW;gCAC9C;uFAAG;wBACL;oBACF;gBACF;;YAEA,MAAM;sEAAoB,CAAC;oBACzB,IAAI,KAAK,OAAO,KAAK,SAAS;wBAC5B,QAAQ,GAAG,CACT,CAAC,+CAA+C,EAAE,QAAQ,iBAAiB,CAAC;wBAG9E,+DAA+D;wBAC/D,MAAM,qBAAqB,sIAAA,CAAA,wBAAqB,CAAC,QAAQ;wBACzD,mBAAmB,uBAAuB,CAAC,KAAK,OAAO;wBAEvD,6DAA6D;wBAC7D,IAAI,CAAC,OAAO,wBAAwB,EAAE;4BACpC,OAAO,wBAAwB,GAAG,CAAC;wBACrC;wBAEA,MAAM,MAAM,KAAK,GAAG;wBACpB,MAAM,iBAAiB,OAAO,wBAAwB,CAAC,QAAQ,IAAI;wBACnE,MAAM,sBAAsB,MAAM;wBAElC,uCAAuC;wBACvC,IAAI,sBAAsB,MAAM;4BAC9B,QAAQ,GAAG,CACT,CAAC,0DAA0D,EAAE,oBAAoB,MAAM,CAAC;4BAE1F;wBACF;wBAEA,mCAAmC;wBACnC,OAAO,wBAAwB,CAAC,QAAQ,GAAG;wBAE3C,+CAA+C;wBAC/C,8DAA8D;wBAC9D,IAAI,gBAAgB;4BAClB;wBACF;wBAEA,kEAAkE;wBAClE;kFAAW;gCACT,sIAAA,CAAA,wBAAqB,CAAC,QAAQ,GAAG,WAAW;4BAC9C;iFAAG;oBACL;gBACF;;YAEA,MAAM;wEAAsB,CAAC;oBAC3B,IAAI,KAAK,OAAO,KAAK,SAAS;wBAC5B,QAAQ,GAAG,CACT,CAAC,kDAAkD,EAAE,QAAQ,iBAAiB,CAAC;wBAGjF,+DAA+D;wBAC/D,MAAM,qBAAqB,sIAAA,CAAA,wBAAqB,CAAC,QAAQ;wBACzD,mBAAmB,uBAAuB,CAAC,KAAK,OAAO;wBAEvD,6EAA6E;wBAC7E,IAAI,KAAK,SAAS,IAAI,iBAAiB,cAAc,EAAE,KAAK,SAAS;4BACnE,qCAAqC;4BACrC,6HAAA,CAAA,eAAY,CAAC,QAAQ,GAAG,gBAAgB,CAAC;gCACvC,GAAG,aAAa;gCAChB,WAAW,KAAK,SAAS;4BAC3B;4BAEA,wBAAwB;4BACxB,MAAM,YAAY,6HAAA,CAAA,eAAY,CAAC,QAAQ;4BACvC,MAAM,aAAa,UAAU,UAAU,GACnC,UAAU,UAAU,CAAC,QAAQ,GAC7B;4BACJ,IAAI,YAAY;gCACd,UAAU,UAAU,CAAC,QAAQ,GAAG;oCAC9B,GAAG,UAAU;oCACb,OAAO;wCACL,GAAG,WAAW,KAAK;wCACnB,WAAW,KAAK,SAAS;oCAC3B;gCACF;4BACF;wBACF;wBAEA,6DAA6D;wBAC7D,IAAI,CAAC,OAAO,wBAAwB,EAAE;4BACpC,OAAO,wBAAwB,GAAG,CAAC;wBACrC;wBAEA,MAAM,MAAM,KAAK,GAAG;wBACpB,MAAM,iBAAiB,OAAO,wBAAwB,CAAC,QAAQ,IAAI;wBACnE,MAAM,sBAAsB,MAAM;wBAElC,mDAAmD;wBACnD,IAAI,sBAAsB,MAAM;4BAC9B,QAAQ,GAAG,CACT,CAAC,0DAA0D,EAAE,oBAAoB,MAAM,CAAC;4BAE1F;wBACF;wBAEA,mCAAmC;wBACnC,OAAO,wBAAwB,CAAC,QAAQ,GAAG;wBAE3C,+CAA+C;wBAC/C,8DAA8D;wBAC9D,IAAI,gBAAgB;4BAClB;wBACF;oBACF;gBACF;;YAEA,2BAA2B;YAC3B,OAAO,EAAE,CAAC,gBAAgB;YAC1B,OAAO,EAAE,CAAC,eAAe;YACzB,OAAO,EAAE,CAAC,iBAAiB;YAC3B,OAAO,EAAE,CAAC,eAAe;YACzB,OAAO,EAAE,CAAC,qBAAqB,oBAAoB,eAAe;YAClE,OAAO,EAAE,CAAC,iBAAiB;YAE3B,qBAAqB;YACrB;oDAAO;oBACL,OAAO,GAAG,CAAC,gBAAgB;oBAC3B,OAAO,GAAG,CAAC,eAAe;oBAC1B,OAAO,GAAG,CAAC,iBAAiB;oBAC5B,OAAO,GAAG,CAAC,eAAe;oBAC1B,OAAO,GAAG,CAAC,qBAAqB;oBAChC,OAAO,GAAG,CAAC,iBAAiB;gBAC9B;;QACF;2CAAG;QACD;QACA;QACA,aAAa;QACb;QACA;QACA;KACD;IAED,yCAAyC;IACzC,OAAO;AACT;GAxSM;;QAOgB,6HAAA,CAAA,eAAY;QACE,iIAAA,CAAA,iBAAc;QACJ,6HAAA,CAAA,eAAY;;;KATpD;uCA0SS", "debugId": null}}, {"offset": {"line": 1029, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/src/components/group/AddMemberDialog.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState, useEffect } from \"react\";\r\nimport {\r\n  <PERSON><PERSON>,\r\n  <PERSON><PERSON><PERSON>ontent,\r\n  <PERSON><PERSON><PERSON>eader,\r\n  DialogTitle,\r\n} from \"@/components/ui/dialog\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { Input } from \"@/components/ui/input\";\r\nimport { Avatar, AvatarFallback, AvatarImage } from \"@/components/ui/avatar\";\r\nimport { Search, Check, UserCheck } from \"lucide-react\";\r\nimport { Checkbox } from \"@/components/ui/checkbox\";\r\nimport { ScrollArea } from \"@/components/ui/scroll-area\";\r\nimport { useFriendStore } from \"@/stores/friendStore\";\r\nimport { addGroupMember, getGroupById } from \"@/actions/group.action\";\r\nimport { toast } from \"sonner\";\r\nimport { useAuthStore } from \"@/stores/authStore\";\r\nimport { GroupRole } from \"@/types/base\";\r\n\r\ninterface AddMemberDialogProps {\r\n  groupId: string;\r\n  isOpen: boolean;\r\n  onOpenChange: (open: boolean) => void;\r\n}\r\n\r\nexport default function AddGroupMemberDialog({\r\n  groupId,\r\n  isOpen,\r\n  onOpenChange,\r\n}: AddMemberDialogProps) {\r\n  const [searchQuery, setSearchQuery] = useState(\"\");\r\n  const [selectedFriends, setSelectedFriends] = useState<string[]>([]);\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const [existingMembers, setExistingMembers] = useState<string[]>([]);\r\n\r\n  const { friends, fetchFriends } = useFriendStore();\r\n  const currentUser = useAuthStore((state) => state.user);\r\n\r\n  // Load friends and group members when dialog opens\r\n  useEffect(() => {\r\n    if (isOpen && groupId) {\r\n      // Fetch friends\r\n      fetchFriends();\r\n      setSelectedFriends([]);\r\n      setSearchQuery(\"\");\r\n\r\n      // Fetch group to get existing members\r\n      const fetchGroupMembers = async () => {\r\n        try {\r\n          const result = await getGroupById(groupId);\r\n          if (result.success && result.group && result.group.members) {\r\n            // Extract member IDs\r\n            const memberIds = result.group.members.map(\r\n              (member: { userId: string }) => member.userId,\r\n            );\r\n            // const memberIds = result.group.members.map(member => member.userId);\r\n            setExistingMembers(memberIds);\r\n          }\r\n        } catch (error) {\r\n          console.error(\"Error fetching group members:\", error);\r\n        }\r\n      };\r\n\r\n      fetchGroupMembers();\r\n    }\r\n  }, [isOpen, groupId, fetchFriends]);\r\n\r\n  // Filter friends based on search query and active tab\r\n  const filteredFriends = friends.filter((friend) => {\r\n    const matchesSearch =\r\n      !searchQuery ||\r\n      (friend.fullName &&\r\n        friend.fullName.toLowerCase().includes(searchQuery.toLowerCase())) ||\r\n      (friend.email &&\r\n        friend.email.toLowerCase().includes(searchQuery.toLowerCase())) ||\r\n      (friend.phoneNumber && friend.phoneNumber.includes(searchQuery));\r\n    return matchesSearch;\r\n  });\r\n\r\n  // Toggle friend selection\r\n  const handleFriendSelection = (friendId: string) => {\r\n    setSelectedFriends((prev) =>\r\n      prev.includes(friendId)\r\n        ? prev.filter((id) => id !== friendId)\r\n        : [...prev, friendId],\r\n    );\r\n  };\r\n\r\n  // Add selected members to group\r\n  const handleAddMembers = async () => {\r\n    if (selectedFriends.length === 0) {\r\n      toast.error(\"Vui lòng chọn ít nhất một thành viên\");\r\n      return;\r\n    }\r\n\r\n    setIsLoading(true);\r\n\r\n    try {\r\n      // Add each selected friend to the group\r\n      for (const friendId of selectedFriends) {\r\n        const result = await addGroupMember(\r\n          groupId,\r\n          friendId,\r\n          currentUser?.id || \"\",\r\n          GroupRole.MEMBER,\r\n        );\r\n        if (!result.success) {\r\n          toast.error(`Không thể thêm thành viên: ${result.error}`);\r\n        }\r\n      }\r\n\r\n      toast.success(\"Đã thêm thành viên vào nhóm\");\r\n      onOpenChange(false);\r\n    } catch (error) {\r\n      console.error(\"Error adding members to group:\", error);\r\n      toast.error(\"Đã xảy ra lỗi khi thêm thành viên\");\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <Dialog open={isOpen} onOpenChange={onOpenChange}>\r\n      <DialogContent className=\"sm:max-w-[500px] p-0\">\r\n        <DialogHeader className=\"px-4 py-2 border-b flex flex-row items-center justify-between\">\r\n          <DialogTitle className=\"text-base font-semibold\">\r\n            Thêm thành viên\r\n          </DialogTitle>\r\n          <Button\r\n            variant=\"ghost\"\r\n            size=\"icon\"\r\n            className=\"h-8 w-8\"\r\n            onClick={() => onOpenChange(false)}\r\n          ></Button>\r\n        </DialogHeader>\r\n\r\n        <div className=\"p-4\">\r\n          <div className=\"relative mb-4\">\r\n            <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\r\n              <Search className=\"h-4 w-4 text-gray-400\" />\r\n            </div>\r\n            <Input\r\n              type=\"text\"\r\n              placeholder=\"Nhập tên, số điện thoại, hoặc danh sách số điện thoại\"\r\n              className=\"pl-10 w-full text-xs\"\r\n              value={searchQuery}\r\n              onChange={(e) => setSearchQuery(e.target.value)}\r\n            />\r\n          </div>\r\n\r\n          <ScrollArea className=\"h-[300px] pr-4\">\r\n            {filteredFriends.length > 0 ? (\r\n              <div className=\"space-y-1\">\r\n                {filteredFriends.map((friend) => (\r\n                  <div\r\n                    key={friend.id}\r\n                    className=\"flex items-center py-2 hover:bg-gray-50 rounded-md\"\r\n                  >\r\n                    {existingMembers.includes(friend.id) ? (\r\n                      <div className=\"ml-2 mr-3 h-4 w-4 flex items-center justify-center text-green-500\">\r\n                        <UserCheck className=\"h-4 w-4\" />\r\n                      </div>\r\n                    ) : (\r\n                      <Checkbox\r\n                        id={`friend-${friend.id}`}\r\n                        checked={selectedFriends.includes(friend.id)}\r\n                        onCheckedChange={() => handleFriendSelection(friend.id)}\r\n                        className=\"ml-2 mr-3\"\r\n                      />\r\n                    )}\r\n                    <Avatar className=\"h-10 w-10 mr-3\">\r\n                      <AvatarImage\r\n                        src={friend.profilePictureUrl || undefined}\r\n                        alt={friend.fullName || \"\"}\r\n                      />\r\n                      <AvatarFallback>\r\n                        {friend.fullName ? friend.fullName.charAt(0) : \"U\"}\r\n                      </AvatarFallback>\r\n                    </Avatar>\r\n                    <div className=\"flex-1\">\r\n                      <p className=\"text-sm font-medium\">{friend.fullName}</p>\r\n                      {existingMembers.includes(friend.id) && (\r\n                        <p className=\"text-xs text-green-500\">\r\n                          Đã là thành viên\r\n                        </p>\r\n                      )}\r\n                    </div>\r\n                  </div>\r\n                ))}\r\n              </div>\r\n            ) : (\r\n              <div className=\"text-center py-8 text-gray-500\">\r\n                <p>Không tìm thấy kết quả</p>\r\n              </div>\r\n            )}\r\n          </ScrollArea>\r\n\r\n          <div className=\"flex justify-end gap-2 mt-4 pt-4 border-t\">\r\n            <Button\r\n              variant=\"outline\"\r\n              onClick={() => onOpenChange(false)}\r\n              disabled={isLoading}\r\n            >\r\n              Hủy\r\n            </Button>\r\n            <Button\r\n              onClick={handleAddMembers}\r\n              disabled={selectedFriends.length === 0 || isLoading}\r\n              className=\"bg-blue-500 hover:bg-blue-600 text-white\"\r\n            >\r\n              {isLoading ? (\r\n                <>\r\n                  <div className=\"h-4 w-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2\"></div>\r\n                  Đang xử lý...\r\n                </>\r\n              ) : (\r\n                <>\r\n                  <Check className=\"h-4 w-4 mr-2\" />\r\n                  Xác nhận\r\n                </>\r\n              )}\r\n            </Button>\r\n          </div>\r\n        </div>\r\n      </DialogContent>\r\n    </Dialog>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAMA;AACA;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAnBA;;;;;;;;;;;;;;AA2Be,SAAS,qBAAqB,EAC3C,OAAO,EACP,MAAM,EACN,YAAY,EACS;;IACrB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACnE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAEnE,MAAM,EAAE,OAAO,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,iBAAc,AAAD;IAC/C,MAAM,cAAc,CAAA,GAAA,6HAAA,CAAA,eAAY,AAAD;0DAAE,CAAC,QAAU,MAAM,IAAI;;IAEtD,mDAAmD;IACnD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0CAAE;YACR,IAAI,UAAU,SAAS;gBACrB,gBAAgB;gBAChB;gBACA,mBAAmB,EAAE;gBACrB,eAAe;gBAEf,sCAAsC;gBACtC,MAAM;wEAAoB;wBACxB,IAAI;4BACF,MAAM,SAAS,MAAM,CAAA,GAAA,oIAAA,CAAA,eAAY,AAAD,EAAE;4BAClC,IAAI,OAAO,OAAO,IAAI,OAAO,KAAK,IAAI,OAAO,KAAK,CAAC,OAAO,EAAE;gCAC1D,qBAAqB;gCACrB,MAAM,YAAY,OAAO,KAAK,CAAC,OAAO,CAAC,GAAG;kGACxC,CAAC,SAA+B,OAAO,MAAM;;gCAE/C,uEAAuE;gCACvE,mBAAmB;4BACrB;wBACF,EAAE,OAAO,OAAO;4BACd,QAAQ,KAAK,CAAC,iCAAiC;wBACjD;oBACF;;gBAEA;YACF;QACF;yCAAG;QAAC;QAAQ;QAAS;KAAa;IAElC,sDAAsD;IACtD,MAAM,kBAAkB,QAAQ,MAAM,CAAC,CAAC;QACtC,MAAM,gBACJ,CAAC,eACA,OAAO,QAAQ,IACd,OAAO,QAAQ,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW,OAC/D,OAAO,KAAK,IACX,OAAO,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW,OAC5D,OAAO,WAAW,IAAI,OAAO,WAAW,CAAC,QAAQ,CAAC;QACrD,OAAO;IACT;IAEA,0BAA0B;IAC1B,MAAM,wBAAwB,CAAC;QAC7B,mBAAmB,CAAC,OAClB,KAAK,QAAQ,CAAC,YACV,KAAK,MAAM,CAAC,CAAC,KAAO,OAAO,YAC3B;mBAAI;gBAAM;aAAS;IAE3B;IAEA,gCAAgC;IAChC,MAAM,mBAAmB;QACvB,IAAI,gBAAgB,MAAM,KAAK,GAAG;YAChC,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,aAAa;QAEb,IAAI;YACF,wCAAwC;YACxC,KAAK,MAAM,YAAY,gBAAiB;gBACtC,MAAM,SAAS,MAAM,CAAA,GAAA,oIAAA,CAAA,iBAAc,AAAD,EAChC,SACA,UACA,aAAa,MAAM,IACnB,uHAAA,CAAA,YAAS,CAAC,MAAM;gBAElB,IAAI,CAAC,OAAO,OAAO,EAAE;oBACnB,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,2BAA2B,EAAE,OAAO,KAAK,EAAE;gBAC1D;YACF;YAEA,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YACd,aAAa;QACf,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;YAChD,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,aAAa;QACf;IACF;IAEA,qBACE,6LAAC,qIAAA,CAAA,SAAM;QAAC,MAAM;QAAQ,cAAc;kBAClC,cAAA,6LAAC,qIAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,6LAAC,qIAAA,CAAA,eAAY;oBAAC,WAAU;;sCACtB,6LAAC,qIAAA,CAAA,cAAW;4BAAC,WAAU;sCAA0B;;;;;;sCAGjD,6LAAC,qIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,WAAU;4BACV,SAAS,IAAM,aAAa;;;;;;;;;;;;8BAIhC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,yMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;;;;;;8CAEpB,6LAAC,oIAAA,CAAA,QAAK;oCACJ,MAAK;oCACL,aAAY;oCACZ,WAAU;oCACV,OAAO;oCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;sCAIlD,6LAAC,6IAAA,CAAA,aAAU;4BAAC,WAAU;sCACnB,gBAAgB,MAAM,GAAG,kBACxB,6LAAC;gCAAI,WAAU;0CACZ,gBAAgB,GAAG,CAAC,CAAC,uBACpB,6LAAC;wCAEC,WAAU;;4CAET,gBAAgB,QAAQ,CAAC,OAAO,EAAE,kBACjC,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,mNAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;;;;;qEAGvB,6LAAC,uIAAA,CAAA,WAAQ;gDACP,IAAI,CAAC,OAAO,EAAE,OAAO,EAAE,EAAE;gDACzB,SAAS,gBAAgB,QAAQ,CAAC,OAAO,EAAE;gDAC3C,iBAAiB,IAAM,sBAAsB,OAAO,EAAE;gDACtD,WAAU;;;;;;0DAGd,6LAAC,qIAAA,CAAA,SAAM;gDAAC,WAAU;;kEAChB,6LAAC,qIAAA,CAAA,cAAW;wDACV,KAAK,OAAO,iBAAiB,IAAI;wDACjC,KAAK,OAAO,QAAQ,IAAI;;;;;;kEAE1B,6LAAC,qIAAA,CAAA,iBAAc;kEACZ,OAAO,QAAQ,GAAG,OAAO,QAAQ,CAAC,MAAM,CAAC,KAAK;;;;;;;;;;;;0DAGnD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEAAuB,OAAO,QAAQ;;;;;;oDAClD,gBAAgB,QAAQ,CAAC,OAAO,EAAE,mBACjC,6LAAC;wDAAE,WAAU;kEAAyB;;;;;;;;;;;;;uCA3BrC,OAAO,EAAE;;;;;;;;;qDAoCpB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;8CAAE;;;;;;;;;;;;;;;;sCAKT,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,SAAS,IAAM,aAAa;oCAC5B,UAAU;8CACX;;;;;;8CAGD,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAS;oCACT,UAAU,gBAAgB,MAAM,KAAK,KAAK;oCAC1C,WAAU;8CAET,0BACC;;0DACE,6LAAC;gDAAI,WAAU;;;;;;4CAA0F;;qEAI3G;;0DACE,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUpD;GA1MwB;;QAUY,+HAAA,CAAA,iBAAc;QAC5B,6HAAA,CAAA,eAAY;;;KAXV", "debugId": null}}, {"offset": {"line": 1409, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/src/components/group/GroupMemberList.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState, useEffect } from \"react\";\r\nimport { Avatar, AvatarFallback, AvatarImage } from \"@/components/ui/avatar\";\r\nimport { But<PERSON> } from \"@/components/ui/button\";\r\nimport { Group, User, GroupRole } from \"@/types/base\";\r\nimport {\r\n  ArrowLeft,\r\n  MoreHorizontal,\r\n  Shield,\r\n  UserMinus,\r\n  Ban,\r\n  UserPlus,\r\n  Link as LinkIcon,\r\n} from \"lucide-react\";\r\nimport AddMemberDialog from \"./AddMemberDialog\";\r\nimport {\r\n  AlertDialog,\r\n  AlertDialogAction,\r\n  AlertDialogCancel,\r\n  AlertDialogContent,\r\n  AlertDialogDescription,\r\n  AlertDialogFooter,\r\n  AlertDialogHeader,\r\n  AlertDialogTitle,\r\n} from \"@/components/ui/alert-dialog\";\r\nimport { ScrollArea } from \"@/components/ui/scroll-area\";\r\nimport ProfileDialog from \"@/components/profile/ProfileDialog\";\r\nimport { batchGetUserData } from \"@/actions/user.action\";\r\nimport { useAuthStore } from \"@/stores/authStore\";\r\nimport { batchGetRelationships } from \"@/actions/friend.action\";\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuItem,\r\n  DropdownMenuSeparator,\r\n  DropdownMenuTrigger,\r\n} from \"@/components/ui/dropdown-menu\";\r\nimport {\r\n  Dialog,\r\n  DialogContent,\r\n  DialogHeader,\r\n  DialogTitle,\r\n} from \"@/components/ui/dialog\";\r\nimport { updateMemberRole, removeGroupMember } from \"@/actions/group.action\";\r\nimport { toast } from \"sonner\";\r\n\r\ninterface GroupMemberListProps {\r\n  group: Group | null;\r\n  isOpen: boolean;\r\n  onOpenChange: (open: boolean) => void;\r\n  onBack: () => void;\r\n}\r\n\r\nexport default function GroupMemberList({\r\n  group,\r\n  isOpen,\r\n  onOpenChange,\r\n  onBack,\r\n}: GroupMemberListProps) {\r\n  const [selectedMember, setSelectedMember] = useState<User | null>(null);\r\n  const [showProfileDialog, setShowProfileDialog] = useState(false);\r\n  const [showFriendRequestForm, setShowFriendRequestForm] = useState(false);\r\n  const [showAddMemberDialog, setShowAddMemberDialog] = useState(false);\r\n  const [showKickDialog, setShowKickDialog] = useState(false);\r\n  const [memberToKick, setMemberToKick] = useState<string | null>(null);\r\n  const [memberDetails, setMemberDetails] = useState<{ [key: string]: User }>(\r\n    {},\r\n  );\r\n  const [isProcessing, setIsProcessing] = useState(false);\r\n  const [adderDetails, setAdderDetails] = useState<{ [key: string]: User }>({});\r\n  const [relationships, setRelationships] = useState<{ [key: string]: string }>(\r\n    {},\r\n  );\r\n  const [isSendingRequest] = useState<{\r\n    [key: string]: boolean;\r\n  }>({});\r\n\r\n  const currentUser = useAuthStore((state) => state.user);\r\n\r\n  // Determine current user's role in the group\r\n  const currentUserRole =\r\n    group?.members?.find((member) => member.userId === currentUser?.id)?.role ||\r\n    \"MEMBER\";\r\n\r\n  // Fetch member details when group changes\r\n  useEffect(() => {\r\n    if (group?.id && group.members) {\r\n      const fetchMemberDetails = async () => {\r\n        const newMemberDetails: { [key: string]: User } = {};\r\n        const newAdderDetails: { [key: string]: User } = {};\r\n        const newRelationships: { [key: string]: string } = {};\r\n\r\n        try {\r\n          // Collect all user IDs that need to be fetched\r\n          const memberIds: string[] = [];\r\n          const adderIds: string[] = [];\r\n          const relationshipIds: string[] = [];\r\n\r\n          // Prepare lists of IDs to fetch\r\n          for (const member of group.members) {\r\n            // Check if we need to fetch user data\r\n            if (!member.user?.userInfo) {\r\n              memberIds.push(member.userId);\r\n            } else {\r\n              // If we already have the data, store it\r\n              newMemberDetails[member.userId] = member.user;\r\n            }\r\n\r\n            // Check if we need to fetch adder data\r\n            if (\r\n              member.addedBy &&\r\n              typeof member.addedBy === \"object\" &&\r\n              \"id\" in member.addedBy &&\r\n              \"fullName\" in member.addedBy\r\n            ) {\r\n              // Create a simple User object with the addedBy information\r\n              const adderInfo = member.addedBy as unknown as {\r\n                id: string;\r\n                fullName: string;\r\n              };\r\n              newAdderDetails[member.userId] = {\r\n                id: adderInfo.id,\r\n                userInfo: {\r\n                  id: adderInfo.id,\r\n                  fullName: adderInfo.fullName,\r\n                  blockStrangers: false,\r\n                  createdAt: new Date(),\r\n                  updatedAt: new Date(),\r\n                  userAuth: { id: adderInfo.id } as User,\r\n                },\r\n              } as unknown as User;\r\n            } else if (\r\n              member.addedById &&\r\n              member.addedById !== currentUser?.id &&\r\n              !member.addedBy\r\n            ) {\r\n              adderIds.push(member.addedById);\r\n            } else if (member.addedBy && \"userInfo\" in member.addedBy) {\r\n              newAdderDetails[member.userId] = member.addedBy as User;\r\n            }\r\n\r\n            // Check if we need to fetch relationship data\r\n            if (member.userId !== currentUser?.id) {\r\n              relationshipIds.push(member.userId);\r\n            }\r\n          }\r\n\r\n          // Batch fetch user data\r\n          if (memberIds.length > 0) {\r\n            console.log(`Batch fetching ${memberIds.length} member details`);\r\n            const userResult = await batchGetUserData(memberIds);\r\n            if (userResult.success && userResult.users) {\r\n              userResult.users.forEach((user) => {\r\n                newMemberDetails[user.id] = user;\r\n              });\r\n            }\r\n          }\r\n\r\n          // Batch fetch adder data\r\n          if (adderIds.length > 0) {\r\n            console.log(`Batch fetching ${adderIds.length} adder details`);\r\n            const adderResult = await batchGetUserData(adderIds);\r\n            if (adderResult.success && adderResult.users) {\r\n              // Match adders to members\r\n              for (const member of group.members) {\r\n                if (member.addedById) {\r\n                  const adder = adderResult.users.find(\r\n                    (u) => u.id === member.addedById,\r\n                  );\r\n                  if (adder) {\r\n                    newAdderDetails[member.userId] = adder;\r\n                  }\r\n                }\r\n              }\r\n            }\r\n          }\r\n\r\n          // Batch fetch relationship data\r\n          if (relationshipIds.length > 0) {\r\n            console.log(\r\n              `Batch fetching ${relationshipIds.length} relationships`,\r\n            );\r\n            const accessToken =\r\n              useAuthStore.getState().accessToken || undefined;\r\n            const relationshipResult = await batchGetRelationships(\r\n              relationshipIds,\r\n              accessToken,\r\n            );\r\n\r\n            if (\r\n              relationshipResult.success &&\r\n              relationshipResult.relationships\r\n            ) {\r\n              // Process relationships\r\n              Object.entries(relationshipResult.relationships).forEach(\r\n                ([userId, data]) => {\r\n                  // Normalize relationship status\r\n                  const status = data.status || \"NONE\";\r\n\r\n                  // Standardize relationship values\r\n                  if (status === \"ACCEPTED\" || status === \"FRIEND\") {\r\n                    newRelationships[userId] = \"ACCEPTED\";\r\n                  } else if (status === \"PENDING_SENT\") {\r\n                    newRelationships[userId] = \"PENDING_SENT\";\r\n                  } else if (status === \"PENDING_RECEIVED\") {\r\n                    newRelationships[userId] = \"PENDING_RECEIVED\";\r\n                  } else {\r\n                    newRelationships[userId] = status;\r\n                  }\r\n\r\n                  console.log(\r\n                    `Normalized relationship with ${userId}:`,\r\n                    newRelationships[userId],\r\n                  );\r\n                },\r\n              );\r\n            }\r\n          }\r\n\r\n          // Set default relationship status for any members without data\r\n          for (const member of group.members) {\r\n            if (\r\n              member.userId !== currentUser?.id &&\r\n              !newRelationships[member.userId]\r\n            ) {\r\n              newRelationships[member.userId] = \"NONE\";\r\n            }\r\n          }\r\n        } catch (error) {\r\n          console.error(\"Error fetching member details:\", error);\r\n        }\r\n\r\n        // Update state with all the data we collected\r\n        setMemberDetails(newMemberDetails);\r\n        setAdderDetails(newAdderDetails);\r\n        setRelationships(newRelationships);\r\n      };\r\n\r\n      fetchMemberDetails();\r\n    }\r\n  }, [group?.id, group?.members, currentUser?.id]);\r\n\r\n  // Handle member click to show profile\r\n  const handleMemberClick = (memberId: string) => {\r\n    const memberData = memberDetails[memberId];\r\n    if (memberData) {\r\n      setSelectedMember(memberData);\r\n      setShowFriendRequestForm(false);\r\n      setShowProfileDialog(true);\r\n    }\r\n  };\r\n\r\n  // Handle send friend request\r\n  const handleSendFriendRequest = (userId: string) => {\r\n    const memberData = memberDetails[userId];\r\n    if (memberData) {\r\n      setSelectedMember(memberData);\r\n      setShowFriendRequestForm(true);\r\n      setShowProfileDialog(true);\r\n    }\r\n  };\r\n\r\n  // Handle promote member to co-leader\r\n  const handlePromoteMember = async (memberId: string) => {\r\n    if (!group?.id) return;\r\n    setIsProcessing(true);\r\n    try {\r\n      const result = await updateMemberRole(\r\n        group.id,\r\n        memberId,\r\n        GroupRole.CO_LEADER,\r\n      );\r\n      if (result.success) {\r\n        toast.success(\"Đã thăng cấp thành viên thành phó nhóm\");\r\n      } else {\r\n        toast.error(`Lỗi: ${result.error}`);\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error promoting member:\", error);\r\n      toast.error(\"Đã xảy ra lỗi khi thăng cấp thành viên\");\r\n    } finally {\r\n      setIsProcessing(false);\r\n    }\r\n  };\r\n\r\n  // Handle demote co-leader to member\r\n  const handleDemoteMember = async (memberId: string) => {\r\n    if (!group?.id) return;\r\n    setIsProcessing(true);\r\n    try {\r\n      const result = await updateMemberRole(\r\n        group.id,\r\n        memberId,\r\n        GroupRole.MEMBER,\r\n      );\r\n      if (result.success) {\r\n        toast.success(\"Đã hạ cấp phó nhóm xuống thành viên thường\");\r\n      } else {\r\n        toast.error(`Lỗi: ${result.error}`);\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error demoting member:\", error);\r\n      toast.error(\"Đã xảy ra lỗi khi hạ cấp thành viên\");\r\n    } finally {\r\n      setIsProcessing(false);\r\n    }\r\n  };\r\n\r\n  // Open kick member dialog\r\n  const openKickMemberDialog = (memberId: string) => {\r\n    setMemberToKick(memberId);\r\n    setShowKickDialog(true);\r\n  };\r\n\r\n  // Handle remove member from group\r\n  const handleKickMember = async () => {\r\n    if (!group?.id || !memberToKick) return;\r\n\r\n    setIsProcessing(true);\r\n    try {\r\n      const result = await removeGroupMember(group.id, memberToKick);\r\n      if (result.success) {\r\n        toast.success(\"Đã xóa thành viên khỏi nhóm\");\r\n        setShowKickDialog(false);\r\n      } else {\r\n        toast.error(`Lỗi: ${result.error}`);\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error removing member:\", error);\r\n      toast.error(\"Đã xảy ra lỗi khi xóa thành viên\");\r\n    } finally {\r\n      setIsProcessing(false);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <>\r\n      <Dialog\r\n        open={isOpen}\r\n        onOpenChange={(open) => {\r\n          // Only close the dialog if no other dialogs are open\r\n          if (!showKickDialog && !showProfileDialog && !showAddMemberDialog) {\r\n            onOpenChange(open);\r\n          }\r\n        }}\r\n      >\r\n        <DialogContent className=\"sm:max-w-[425px] h-auto !p-0 mt-0 mb-16 max-h-[90vh] overflow-y-auto no-scrollbar\">\r\n          <DialogHeader className=\"px-4 py-2 flex flex-row items-center border-b\">\r\n            <Button\r\n              variant=\"ghost\"\r\n              size=\"icon\"\r\n              className=\"mr-2 h-8 w-8\"\r\n              onClick={onBack}\r\n            >\r\n              <ArrowLeft className=\"h-4 w-4\" />\r\n            </Button>\r\n            <DialogTitle className=\"text-base font-semibold\">\r\n              Danh sách thành viên\r\n            </DialogTitle>\r\n            <Button\r\n              variant=\"ghost\"\r\n              size=\"icon\"\r\n              className=\"ml-auto h-8 w-8\"\r\n              onClick={() => onOpenChange(false)}\r\n            ></Button>\r\n          </DialogHeader>\r\n\r\n          <div className=\"px-4 pb-4 border-b\">\r\n            <Button\r\n              className=\"w-full flex items-center justify-center bg-gray-100 hover:bg-gray-200 text-black\"\r\n              onClick={() => setShowAddMemberDialog(true)}\r\n            >\r\n              <UserPlus className=\"h-4 w-4\" />\r\n              <span>Thêm thành viên</span>\r\n            </Button>\r\n          </div>\r\n\r\n          <div className=\"px-4 flex justify-between items-center\">\r\n            <span className=\"text-sm\">\r\n              Danh sách thành viên ({group?.members?.length || 0})\r\n            </span>\r\n          </div>\r\n\r\n          <ScrollArea className=\"flex-1\">\r\n            {group?.members?.map((member) => {\r\n              const memberData = memberDetails[member.userId];\r\n              const initials = memberData?.userInfo?.fullName\r\n                ? memberData.userInfo.fullName.slice(0, 2).toUpperCase()\r\n                : \"??\";\r\n\r\n              return (\r\n                <div\r\n                  key={member.userId}\r\n                  className=\"flex items-center px-4 py-2 hover:bg-gray-100 justify-between\"\r\n                >\r\n                  <div\r\n                    className=\"flex items-center cursor-pointer\"\r\n                    onClick={() => handleMemberClick(member.userId)}\r\n                  >\r\n                    <Avatar className=\"h-10 w-10 mr-3\">\r\n                      <AvatarImage\r\n                        src={\r\n                          memberData?.userInfo?.profilePictureUrl || undefined\r\n                        }\r\n                        className=\"object-cover\"\r\n                      />\r\n                      <AvatarFallback className=\"bg-gray-200 text-gray-600\">\r\n                        {initials}\r\n                      </AvatarFallback>\r\n                    </Avatar>\r\n                    <div>\r\n                      <p className=\"font-medium\">\r\n                        {memberData?.userInfo?.fullName || \"Thành viên\"}\r\n                      </p>\r\n                      <p className=\"text-xs text-gray-500\">\r\n                        {member.role === \"LEADER\"\r\n                          ? \"Trưởng nhóm\"\r\n                          : member.role === \"CO_LEADER\"\r\n                            ? \"Phó nhóm\"\r\n                            : \"\"}\r\n                      </p>\r\n                      {member.userId !== currentUser?.id && (\r\n                        <p className=\"text-xs text-gray-500\">\r\n                          Thêm bởi{\" \"}\r\n                          {member.addedBy && \"fullName\" in member.addedBy\r\n                            ? (\r\n                                member.addedBy as unknown as {\r\n                                  fullName: string;\r\n                                }\r\n                              ).fullName\r\n                            : adderDetails[member.userId]?.userInfo?.fullName ||\r\n                              \"Người dùng\"}\r\n                        </p>\r\n                      )}\r\n                      {member.userId !== currentUser?.id &&\r\n                        member.userId !== group?.creatorId && (\r\n                          <p className=\"text-xs text-gray-500\">\r\n                            {member.joinedAt &&\r\n                              `Tham gia ${new Date(member.joinedAt).toLocaleDateString()}`}\r\n                          </p>\r\n                        )}\r\n                    </div>\r\n                  </div>\r\n\r\n                  <div className=\"flex items-center\">\r\n                    {/* Show pending status */}\r\n                    {member.userId !== currentUser?.id &&\r\n                      relationships[member.userId] === \"PENDING_SENT\" && (\r\n                        <Button\r\n                          variant=\"ghost\"\r\n                          size=\"icon\"\r\n                          disabled\r\n                          title=\"Đã gửi lời mời kết bạn\"\r\n                        >\r\n                          <LinkIcon className=\"h-4 w-4 text-gray-400\" />\r\n                        </Button>\r\n                      )}\r\n\r\n                    {/* Show dropdown menu for all members except current user */}\r\n                    {member.userId !== currentUser?.id && (\r\n                      <DropdownMenu>\r\n                        <DropdownMenuTrigger asChild>\r\n                          <Button variant=\"ghost\" size=\"icon\">\r\n                            <MoreHorizontal className=\"h-5 w-5\" />\r\n                          </Button>\r\n                        </DropdownMenuTrigger>\r\n                        <DropdownMenuContent align=\"end\">\r\n                          {/* Add friend option if not already friends */}\r\n                          {relationships[member.userId] === \"NONE\" && (\r\n                            <DropdownMenuItem\r\n                              onClick={() =>\r\n                                handleSendFriendRequest(member.userId)\r\n                              }\r\n                              disabled={isSendingRequest[member.userId]}\r\n                            >\r\n                              {isSendingRequest[member.userId] ? (\r\n                                <>\r\n                                  <div className=\"h-4 w-4 mr-2 rounded-full border-2 border-gray-600 border-t-transparent animate-spin\"></div>\r\n                                  Đang gửi...\r\n                                </>\r\n                              ) : (\r\n                                <>\r\n                                  <UserPlus className=\"h-4 w-4 mr-2 text-blue-500\" />\r\n                                  Kết bạn\r\n                                </>\r\n                              )}\r\n                            </DropdownMenuItem>\r\n                          )}\r\n\r\n                          {/* Leader/Co-leader management options */}\r\n                          {(currentUserRole === \"LEADER\" ||\r\n                            (currentUserRole === \"CO_LEADER\" &&\r\n                              member.role === \"MEMBER\")) && (\r\n                            <>\r\n                              {currentUserRole === \"LEADER\" &&\r\n                                member.role === \"MEMBER\" && (\r\n                                  <DropdownMenuItem\r\n                                    onClick={() =>\r\n                                      handlePromoteMember(member.userId)\r\n                                    }\r\n                                  >\r\n                                    <Shield className=\"h-4 w-4 mr-2\" />\r\n                                    Thăng phó nhóm\r\n                                  </DropdownMenuItem>\r\n                                )}\r\n                              {currentUserRole === \"LEADER\" &&\r\n                                member.role === \"CO_LEADER\" && (\r\n                                  <DropdownMenuItem\r\n                                    onClick={() =>\r\n                                      handleDemoteMember(member.userId)\r\n                                    }\r\n                                  >\r\n                                    <UserMinus className=\"h-4 w-4 mr-2\" />\r\n                                    Hạ xuống thành viên\r\n                                  </DropdownMenuItem>\r\n                                )}\r\n                              <DropdownMenuSeparator />\r\n                              <DropdownMenuItem\r\n                                onClick={() =>\r\n                                  openKickMemberDialog(member.userId)\r\n                                }\r\n                                className=\"text-red-500 focus:text-red-500\"\r\n                              >\r\n                                <Ban className=\"h-4 w-4 mr-2\" />\r\n                                Xóa khỏi nhóm\r\n                              </DropdownMenuItem>\r\n                            </>\r\n                          )}\r\n                        </DropdownMenuContent>\r\n                      </DropdownMenu>\r\n                    )}\r\n                  </div>\r\n                </div>\r\n              );\r\n            })}\r\n          </ScrollArea>\r\n        </DialogContent>\r\n      </Dialog>\r\n\r\n      {/* Profile Dialog for members */}\r\n      {showProfileDialog && selectedMember && (\r\n        <ProfileDialog\r\n          user={selectedMember}\r\n          isOpen={showProfileDialog}\r\n          onOpenChange={(open) => {\r\n            setShowProfileDialog(open);\r\n            if (!open) {\r\n              setSelectedMember(null);\r\n              setShowFriendRequestForm(false);\r\n            }\r\n          }}\r\n          isOwnProfile={selectedMember.id === currentUser?.id}\r\n          initialShowFriendRequestForm={showFriendRequestForm}\r\n        />\r\n      )}\r\n\r\n      {/* Add Member Dialog */}\r\n      {group?.id && (\r\n        <AddMemberDialog\r\n          groupId={group.id}\r\n          isOpen={showAddMemberDialog}\r\n          onOpenChange={(open) => {\r\n            setShowAddMemberDialog(open);\r\n            // If the add member dialog is closed and the member list should still be open\r\n            if (!open && isOpen) {\r\n              // Force the member list to stay open\r\n              setTimeout(() => onOpenChange(true), 0);\r\n            }\r\n          }}\r\n        />\r\n      )}\r\n\r\n      {/* Kick Member Confirmation Dialog */}\r\n      <AlertDialog\r\n        open={showKickDialog}\r\n        onOpenChange={(open) => {\r\n          setShowKickDialog(open);\r\n          // If the kick dialog is closed and the member list should still be open\r\n          if (!open && isOpen) {\r\n            // Force the member list to stay open\r\n            setTimeout(() => onOpenChange(true), 0);\r\n          }\r\n        }}\r\n      >\r\n        <AlertDialogContent>\r\n          <AlertDialogHeader>\r\n            <AlertDialogTitle>Xóa thành viên</AlertDialogTitle>\r\n            <AlertDialogDescription>\r\n              Bạn có chắc chắn muốn xóa thành viên này khỏi nhóm? Họ sẽ không\r\n              thể xem tin nhắn trong nhóm này nữa.\r\n            </AlertDialogDescription>\r\n          </AlertDialogHeader>\r\n          <AlertDialogFooter>\r\n            <AlertDialogCancel disabled={isProcessing}>Hủy</AlertDialogCancel>\r\n            <AlertDialogAction\r\n              onClick={handleKickMember}\r\n              disabled={isProcessing}\r\n              className=\"bg-red-500 hover:bg-red-600\"\r\n            >\r\n              {isProcessing ? (\r\n                <>\r\n                  <div className=\"animate-spin h-4 w-4 border-2 border-white rounded-full border-t-transparent mr-2\"></div>\r\n                  Đang xử lý...\r\n                </>\r\n              ) : (\r\n                \"Xóa thành viên\"\r\n              )}\r\n            </AlertDialogAction>\r\n          </AlertDialogFooter>\r\n        </AlertDialogContent>\r\n      </AlertDialog>\r\n    </>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AASA;AACA;AAUA;AACA;AACA;AACA;AACA;AACA;AAOA;AAMA;AACA;;;AA7CA;;;;;;;;;;;;;;;;;AAsDe,SAAS,gBAAgB,EACtC,KAAK,EACL,MAAM,EACN,YAAY,EACZ,MAAM,EACe;;IACrB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAClE,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,uBAAuB,yBAAyB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnE,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAChE,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAC/C,CAAC;IAEH,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA2B,CAAC;IAC3E,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAC/C,CAAC;IAEH,MAAM,CAAC,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAE/B,CAAC;IAEJ,MAAM,cAAc,CAAA,GAAA,6HAAA,CAAA,eAAY,AAAD;qDAAE,CAAC,QAAU,MAAM,IAAI;;IAEtD,6CAA6C;IAC7C,MAAM,kBACJ,OAAO,SAAS,KAAK,CAAC,SAAW,OAAO,MAAM,KAAK,aAAa,KAAK,QACrE;IAEF,0CAA0C;IAC1C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR,IAAI,OAAO,MAAM,MAAM,OAAO,EAAE;gBAC9B,MAAM;oEAAqB;wBACzB,MAAM,mBAA4C,CAAC;wBACnD,MAAM,kBAA2C,CAAC;wBAClD,MAAM,mBAA8C,CAAC;wBAErD,IAAI;4BACF,+CAA+C;4BAC/C,MAAM,YAAsB,EAAE;4BAC9B,MAAM,WAAqB,EAAE;4BAC7B,MAAM,kBAA4B,EAAE;4BAEpC,gCAAgC;4BAChC,KAAK,MAAM,UAAU,MAAM,OAAO,CAAE;gCAClC,sCAAsC;gCACtC,IAAI,CAAC,OAAO,IAAI,EAAE,UAAU;oCAC1B,UAAU,IAAI,CAAC,OAAO,MAAM;gCAC9B,OAAO;oCACL,wCAAwC;oCACxC,gBAAgB,CAAC,OAAO,MAAM,CAAC,GAAG,OAAO,IAAI;gCAC/C;gCAEA,uCAAuC;gCACvC,IACE,OAAO,OAAO,IACd,OAAO,OAAO,OAAO,KAAK,YAC1B,QAAQ,OAAO,OAAO,IACtB,cAAc,OAAO,OAAO,EAC5B;oCACA,2DAA2D;oCAC3D,MAAM,YAAY,OAAO,OAAO;oCAIhC,eAAe,CAAC,OAAO,MAAM,CAAC,GAAG;wCAC/B,IAAI,UAAU,EAAE;wCAChB,UAAU;4CACR,IAAI,UAAU,EAAE;4CAChB,UAAU,UAAU,QAAQ;4CAC5B,gBAAgB;4CAChB,WAAW,IAAI;4CACf,WAAW,IAAI;4CACf,UAAU;gDAAE,IAAI,UAAU,EAAE;4CAAC;wCAC/B;oCACF;gCACF,OAAO,IACL,OAAO,SAAS,IAChB,OAAO,SAAS,KAAK,aAAa,MAClC,CAAC,OAAO,OAAO,EACf;oCACA,SAAS,IAAI,CAAC,OAAO,SAAS;gCAChC,OAAO,IAAI,OAAO,OAAO,IAAI,cAAc,OAAO,OAAO,EAAE;oCACzD,eAAe,CAAC,OAAO,MAAM,CAAC,GAAG,OAAO,OAAO;gCACjD;gCAEA,8CAA8C;gCAC9C,IAAI,OAAO,MAAM,KAAK,aAAa,IAAI;oCACrC,gBAAgB,IAAI,CAAC,OAAO,MAAM;gCACpC;4BACF;4BAEA,wBAAwB;4BACxB,IAAI,UAAU,MAAM,GAAG,GAAG;gCACxB,QAAQ,GAAG,CAAC,CAAC,eAAe,EAAE,UAAU,MAAM,CAAC,eAAe,CAAC;gCAC/D,MAAM,aAAa,MAAM,CAAA,GAAA,mIAAA,CAAA,mBAAgB,AAAD,EAAE;gCAC1C,IAAI,WAAW,OAAO,IAAI,WAAW,KAAK,EAAE;oCAC1C,WAAW,KAAK,CAAC,OAAO;wFAAC,CAAC;4CACxB,gBAAgB,CAAC,KAAK,EAAE,CAAC,GAAG;wCAC9B;;gCACF;4BACF;4BAEA,yBAAyB;4BACzB,IAAI,SAAS,MAAM,GAAG,GAAG;gCACvB,QAAQ,GAAG,CAAC,CAAC,eAAe,EAAE,SAAS,MAAM,CAAC,cAAc,CAAC;gCAC7D,MAAM,cAAc,MAAM,CAAA,GAAA,mIAAA,CAAA,mBAAgB,AAAD,EAAE;gCAC3C,IAAI,YAAY,OAAO,IAAI,YAAY,KAAK,EAAE;oCAC5C,0BAA0B;oCAC1B,KAAK,MAAM,UAAU,MAAM,OAAO,CAAE;wCAClC,IAAI,OAAO,SAAS,EAAE;4CACpB,MAAM,QAAQ,YAAY,KAAK,CAAC,IAAI;sGAClC,CAAC,IAAM,EAAE,EAAE,KAAK,OAAO,SAAS;;4CAElC,IAAI,OAAO;gDACT,eAAe,CAAC,OAAO,MAAM,CAAC,GAAG;4CACnC;wCACF;oCACF;gCACF;4BACF;4BAEA,gCAAgC;4BAChC,IAAI,gBAAgB,MAAM,GAAG,GAAG;gCAC9B,QAAQ,GAAG,CACT,CAAC,eAAe,EAAE,gBAAgB,MAAM,CAAC,cAAc,CAAC;gCAE1D,MAAM,cACJ,6HAAA,CAAA,eAAY,CAAC,QAAQ,GAAG,WAAW,IAAI;gCACzC,MAAM,qBAAqB,MAAM,CAAA,GAAA,yJAAA,CAAA,wBAAqB,AAAD,EACnD,iBACA;gCAGF,IACE,mBAAmB,OAAO,IAC1B,mBAAmB,aAAa,EAChC;oCACA,wBAAwB;oCACxB,OAAO,OAAO,CAAC,mBAAmB,aAAa,EAAE,OAAO;wFACtD,CAAC,CAAC,QAAQ,KAAK;4CACb,gCAAgC;4CAChC,MAAM,SAAS,KAAK,MAAM,IAAI;4CAE9B,kCAAkC;4CAClC,IAAI,WAAW,cAAc,WAAW,UAAU;gDAChD,gBAAgB,CAAC,OAAO,GAAG;4CAC7B,OAAO,IAAI,WAAW,gBAAgB;gDACpC,gBAAgB,CAAC,OAAO,GAAG;4CAC7B,OAAO,IAAI,WAAW,oBAAoB;gDACxC,gBAAgB,CAAC,OAAO,GAAG;4CAC7B,OAAO;gDACL,gBAAgB,CAAC,OAAO,GAAG;4CAC7B;4CAEA,QAAQ,GAAG,CACT,CAAC,6BAA6B,EAAE,OAAO,CAAC,CAAC,EACzC,gBAAgB,CAAC,OAAO;wCAE5B;;gCAEJ;4BACF;4BAEA,+DAA+D;4BAC/D,KAAK,MAAM,UAAU,MAAM,OAAO,CAAE;gCAClC,IACE,OAAO,MAAM,KAAK,aAAa,MAC/B,CAAC,gBAAgB,CAAC,OAAO,MAAM,CAAC,EAChC;oCACA,gBAAgB,CAAC,OAAO,MAAM,CAAC,GAAG;gCACpC;4BACF;wBACF,EAAE,OAAO,OAAO;4BACd,QAAQ,KAAK,CAAC,kCAAkC;wBAClD;wBAEA,8CAA8C;wBAC9C,iBAAiB;wBACjB,gBAAgB;wBAChB,iBAAiB;oBACnB;;gBAEA;YACF;QACF;oCAAG;QAAC,OAAO;QAAI,OAAO;QAAS,aAAa;KAAG;IAE/C,sCAAsC;IACtC,MAAM,oBAAoB,CAAC;QACzB,MAAM,aAAa,aAAa,CAAC,SAAS;QAC1C,IAAI,YAAY;YACd,kBAAkB;YAClB,yBAAyB;YACzB,qBAAqB;QACvB;IACF;IAEA,6BAA6B;IAC7B,MAAM,0BAA0B,CAAC;QAC/B,MAAM,aAAa,aAAa,CAAC,OAAO;QACxC,IAAI,YAAY;YACd,kBAAkB;YAClB,yBAAyB;YACzB,qBAAqB;QACvB;IACF;IAEA,qCAAqC;IACrC,MAAM,sBAAsB,OAAO;QACjC,IAAI,CAAC,OAAO,IAAI;QAChB,gBAAgB;QAChB,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,oIAAA,CAAA,mBAAgB,AAAD,EAClC,MAAM,EAAE,EACR,UACA,uHAAA,CAAA,YAAS,CAAC,SAAS;YAErB,IAAI,OAAO,OAAO,EAAE;gBAClB,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB,OAAO;gBACL,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,OAAO,KAAK,EAAE;YACpC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,oCAAoC;IACpC,MAAM,qBAAqB,OAAO;QAChC,IAAI,CAAC,OAAO,IAAI;QAChB,gBAAgB;QAChB,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,oIAAA,CAAA,mBAAgB,AAAD,EAClC,MAAM,EAAE,EACR,UACA,uHAAA,CAAA,YAAS,CAAC,MAAM;YAElB,IAAI,OAAO,OAAO,EAAE;gBAClB,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB,OAAO;gBACL,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,OAAO,KAAK,EAAE;YACpC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,0BAA0B;IAC1B,MAAM,uBAAuB,CAAC;QAC5B,gBAAgB;QAChB,kBAAkB;IACpB;IAEA,kCAAkC;IAClC,MAAM,mBAAmB;QACvB,IAAI,CAAC,OAAO,MAAM,CAAC,cAAc;QAEjC,gBAAgB;QAChB,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,oIAAA,CAAA,oBAAiB,AAAD,EAAE,MAAM,EAAE,EAAE;YACjD,IAAI,OAAO,OAAO,EAAE;gBAClB,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBACd,kBAAkB;YACpB,OAAO;gBACL,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,OAAO,KAAK,EAAE;YACpC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,qBACE;;0BACE,6LAAC,qIAAA,CAAA,SAAM;gBACL,MAAM;gBACN,cAAc,CAAC;oBACb,qDAAqD;oBACrD,IAAI,CAAC,kBAAkB,CAAC,qBAAqB,CAAC,qBAAqB;wBACjE,aAAa;oBACf;gBACF;0BAEA,cAAA,6LAAC,qIAAA,CAAA,gBAAa;oBAAC,WAAU;;sCACvB,6LAAC,qIAAA,CAAA,eAAY;4BAAC,WAAU;;8CACtB,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAU;oCACV,SAAS;8CAET,cAAA,6LAAC,mNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;;;;;;8CAEvB,6LAAC,qIAAA,CAAA,cAAW;oCAAC,WAAU;8CAA0B;;;;;;8CAGjD,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAU;oCACV,SAAS,IAAM,aAAa;;;;;;;;;;;;sCAIhC,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;gCACL,WAAU;gCACV,SAAS,IAAM,uBAAuB;;kDAEtC,6LAAC,iNAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,6LAAC;kDAAK;;;;;;;;;;;;;;;;;sCAIV,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAK,WAAU;;oCAAU;oCACD,OAAO,SAAS,UAAU;oCAAE;;;;;;;;;;;;sCAIvD,6LAAC,6IAAA,CAAA,aAAU;4BAAC,WAAU;sCACnB,OAAO,SAAS,IAAI,CAAC;gCACpB,MAAM,aAAa,aAAa,CAAC,OAAO,MAAM,CAAC;gCAC/C,MAAM,WAAW,YAAY,UAAU,WACnC,WAAW,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,GAAG,WAAW,KACpD;gCAEJ,qBACE,6LAAC;oCAEC,WAAU;;sDAEV,6LAAC;4CACC,WAAU;4CACV,SAAS,IAAM,kBAAkB,OAAO,MAAM;;8DAE9C,6LAAC,qIAAA,CAAA,SAAM;oDAAC,WAAU;;sEAChB,6LAAC,qIAAA,CAAA,cAAW;4DACV,KACE,YAAY,UAAU,qBAAqB;4DAE7C,WAAU;;;;;;sEAEZ,6LAAC,qIAAA,CAAA,iBAAc;4DAAC,WAAU;sEACvB;;;;;;;;;;;;8DAGL,6LAAC;;sEACC,6LAAC;4DAAE,WAAU;sEACV,YAAY,UAAU,YAAY;;;;;;sEAErC,6LAAC;4DAAE,WAAU;sEACV,OAAO,IAAI,KAAK,WACb,gBACA,OAAO,IAAI,KAAK,cACd,aACA;;;;;;wDAEP,OAAO,MAAM,KAAK,aAAa,oBAC9B,6LAAC;4DAAE,WAAU;;gEAAwB;gEAC1B;gEACR,OAAO,OAAO,IAAI,cAAc,OAAO,OAAO,GAC3C,AACE,OAAO,OAAO,CAGd,QAAQ,GACV,YAAY,CAAC,OAAO,MAAM,CAAC,EAAE,UAAU,YACvC;;;;;;;wDAGP,OAAO,MAAM,KAAK,aAAa,MAC9B,OAAO,MAAM,KAAK,OAAO,2BACvB,6LAAC;4DAAE,WAAU;sEACV,OAAO,QAAQ,IACd,CAAC,SAAS,EAAE,IAAI,KAAK,OAAO,QAAQ,EAAE,kBAAkB,IAAI;;;;;;;;;;;;;;;;;;sDAMxE,6LAAC;4CAAI,WAAU;;gDAEZ,OAAO,MAAM,KAAK,aAAa,MAC9B,aAAa,CAAC,OAAO,MAAM,CAAC,KAAK,gCAC/B,6LAAC,qIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,QAAQ;oDACR,OAAM;8DAEN,cAAA,6LAAC,qMAAA,CAAA,OAAQ;wDAAC,WAAU;;;;;;;;;;;gDAKzB,OAAO,MAAM,KAAK,aAAa,oBAC9B,6LAAC,+IAAA,CAAA,eAAY;;sEACX,6LAAC,+IAAA,CAAA,sBAAmB;4DAAC,OAAO;sEAC1B,cAAA,6LAAC,qIAAA,CAAA,SAAM;gEAAC,SAAQ;gEAAQ,MAAK;0EAC3B,cAAA,6LAAC,mNAAA,CAAA,iBAAc;oEAAC,WAAU;;;;;;;;;;;;;;;;sEAG9B,6LAAC,+IAAA,CAAA,sBAAmB;4DAAC,OAAM;;gEAExB,aAAa,CAAC,OAAO,MAAM,CAAC,KAAK,wBAChC,6LAAC,+IAAA,CAAA,mBAAgB;oEACf,SAAS,IACP,wBAAwB,OAAO,MAAM;oEAEvC,UAAU,gBAAgB,CAAC,OAAO,MAAM,CAAC;8EAExC,gBAAgB,CAAC,OAAO,MAAM,CAAC,iBAC9B;;0FACE,6LAAC;gFAAI,WAAU;;;;;;4EAA6F;;qGAI9G;;0FACE,6LAAC,iNAAA,CAAA,WAAQ;gFAAC,WAAU;;;;;;4EAA+B;;;;;;;;gEAQ1D,CAAC,oBAAoB,YACnB,oBAAoB,eACnB,OAAO,IAAI,KAAK,QAAS,mBAC3B;;wEACG,oBAAoB,YACnB,OAAO,IAAI,KAAK,0BACd,6LAAC,+IAAA,CAAA,mBAAgB;4EACf,SAAS,IACP,oBAAoB,OAAO,MAAM;;8FAGnC,6LAAC,yMAAA,CAAA,SAAM;oFAAC,WAAU;;;;;;gFAAiB;;;;;;;wEAIxC,oBAAoB,YACnB,OAAO,IAAI,KAAK,6BACd,6LAAC,+IAAA,CAAA,mBAAgB;4EACf,SAAS,IACP,mBAAmB,OAAO,MAAM;;8FAGlC,6LAAC,mNAAA,CAAA,YAAS;oFAAC,WAAU;;;;;;gFAAiB;;;;;;;sFAI5C,6LAAC,+IAAA,CAAA,wBAAqB;;;;;sFACtB,6LAAC,+IAAA,CAAA,mBAAgB;4EACf,SAAS,IACP,qBAAqB,OAAO,MAAM;4EAEpC,WAAU;;8FAEV,6LAAC,mMAAA,CAAA,MAAG;oFAAC,WAAU;;;;;;gFAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;mCAnIzC,OAAO,MAAM;;;;;4BA8IxB;;;;;;;;;;;;;;;;;YAML,qBAAqB,gCACpB,6LAAC,iJAAA,CAAA,UAAa;gBACZ,MAAM;gBACN,QAAQ;gBACR,cAAc,CAAC;oBACb,qBAAqB;oBACrB,IAAI,CAAC,MAAM;wBACT,kBAAkB;wBAClB,yBAAyB;oBAC3B;gBACF;gBACA,cAAc,eAAe,EAAE,KAAK,aAAa;gBACjD,8BAA8B;;;;;;YAKjC,OAAO,oBACN,6LAAC,iJAAA,CAAA,UAAe;gBACd,SAAS,MAAM,EAAE;gBACjB,QAAQ;gBACR,cAAc,CAAC;oBACb,uBAAuB;oBACvB,8EAA8E;oBAC9E,IAAI,CAAC,QAAQ,QAAQ;wBACnB,qCAAqC;wBACrC,WAAW,IAAM,aAAa,OAAO;oBACvC;gBACF;;;;;;0BAKJ,6LAAC,8IAAA,CAAA,cAAW;gBACV,MAAM;gBACN,cAAc,CAAC;oBACb,kBAAkB;oBAClB,wEAAwE;oBACxE,IAAI,CAAC,QAAQ,QAAQ;wBACnB,qCAAqC;wBACrC,WAAW,IAAM,aAAa,OAAO;oBACvC;gBACF;0BAEA,cAAA,6LAAC,8IAAA,CAAA,qBAAkB;;sCACjB,6LAAC,8IAAA,CAAA,oBAAiB;;8CAChB,6LAAC,8IAAA,CAAA,mBAAgB;8CAAC;;;;;;8CAClB,6LAAC,8IAAA,CAAA,yBAAsB;8CAAC;;;;;;;;;;;;sCAK1B,6LAAC,8IAAA,CAAA,oBAAiB;;8CAChB,6LAAC,8IAAA,CAAA,oBAAiB;oCAAC,UAAU;8CAAc;;;;;;8CAC3C,6LAAC,8IAAA,CAAA,oBAAiB;oCAChB,SAAS;oCACT,UAAU;oCACV,WAAU;8CAET,6BACC;;0DACE,6LAAC;gDAAI,WAAU;;;;;;4CAA0F;;uDAI3G;;;;;;;;;;;;;;;;;;;;;;;;;AAQhB;GAhjBwB;;QAwBF,6HAAA,CAAA,eAAY;;;KAxBV", "debugId": null}}, {"offset": {"line": 2188, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/src/components/group/EditGroupNameDialog.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState } from \"react\";\r\nimport {\r\n  <PERSON><PERSON>,\r\n  <PERSON><PERSON><PERSON><PERSON><PERSON>,\r\n  <PERSON><PERSON><PERSON><PERSON><PERSON>,\r\n  <PERSON><PERSON><PERSON><PERSON><PERSON>,\r\n  <PERSON><PERSON>Footer,\r\n} from \"@/components/ui/dialog\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { Input } from \"@/components/ui/input\";\r\nimport { Avatar, AvatarFallback, AvatarImage } from \"@/components/ui/avatar\";\r\nimport { updateGroup } from \"@/actions/group.action\";\r\nimport { toast } from \"sonner\";\r\nimport { Group } from \"@/types/base\";\r\n\r\ninterface EditGroupNameDialogProps {\r\n  group: Group | null;\r\n  isOpen: boolean;\r\n  onOpenChange: (open: boolean) => void;\r\n  onBack: () => void;\r\n  onSuccess?: (updatedGroup: Group) => void;\r\n}\r\n\r\nexport default function EditGroupNameDialog({\r\n  group,\r\n  isOpen,\r\n  onOpenChange,\r\n  onBack,\r\n  onSuccess,\r\n}: EditGroupNameDialogProps) {\r\n  const [newGroupName, setNewGroupName] = useState(group?.name || \"\");\r\n  const [isProcessing, setIsProcessing] = useState(false);\r\n\r\n  // Reset the input when the dialog opens\r\n  const handleOpenChange = (open: boolean) => {\r\n    if (open) {\r\n      setNewGroupName(group?.name || \"\");\r\n    }\r\n    onOpenChange(open);\r\n  };\r\n\r\n  const handleSubmit = async () => {\r\n    if (!group?.id) return;\r\n\r\n    // Validate input\r\n    if (!newGroupName.trim()) {\r\n      toast.error(\"Tên nhóm không được để trống\");\r\n      return;\r\n    }\r\n\r\n    setIsProcessing(true);\r\n    try {\r\n      const result = await updateGroup(group.id, { name: newGroupName.trim() });\r\n\r\n      if (result.success && result.group) {\r\n        toast.success(\"Đổi tên nhóm thành công\");\r\n        onOpenChange(false);\r\n\r\n        // Call the success callback if provided\r\n        if (onSuccess) {\r\n          onSuccess(result.group);\r\n        }\r\n      } else {\r\n        toast.error(result.error || \"Không thể đổi tên nhóm\");\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error updating group name:\", error);\r\n      toast.error(\"Đã xảy ra lỗi khi đổi tên nhóm\");\r\n    } finally {\r\n      setIsProcessing(false);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <Dialog open={isOpen} onOpenChange={handleOpenChange}>\r\n      <DialogContent className=\"sm:max-w-[425px]\">\r\n        <DialogHeader className=\"flex flex-row items-center\">\r\n          <DialogTitle>Đổi tên nhóm</DialogTitle>\r\n        </DialogHeader>\r\n\r\n        <div className=\"flex flex-col items-center space-y-4 py-4\">\r\n          <Avatar className=\"h-20 w-20\">\r\n            <AvatarImage\r\n              src={group?.avatarUrl || undefined}\r\n              className=\"object-cover\"\r\n            />\r\n            <AvatarFallback className=\"text-xl\">\r\n              {group?.name?.slice(0, 2).toUpperCase() || \"GR\"}\r\n            </AvatarFallback>\r\n          </Avatar>\r\n\r\n          <div className=\"w-full\">\r\n            <Input\r\n              value={newGroupName}\r\n              onChange={(e) => setNewGroupName(e.target.value)}\r\n              placeholder=\"Nhập tên nhóm mới...\"\r\n              className=\"w-full\"\r\n              autoFocus\r\n            />\r\n          </div>\r\n\r\n          <p className=\"text-sm text-gray-500 text-center\">\r\n            Bạn có chắc chắn muốn đổi tên nhóm, khi xác nhận tên nhóm mới sẽ\r\n            hiển thị với tất cả thành viên.\r\n          </p>\r\n        </div>\r\n\r\n        <DialogFooter className=\"flex justify-between\">\r\n          <Button variant=\"outline\" onClick={onBack} disabled={isProcessing}>\r\n            Hủy\r\n          </Button>\r\n          <Button\r\n            onClick={handleSubmit}\r\n            disabled={\r\n              isProcessing ||\r\n              !newGroupName.trim() ||\r\n              newGroupName === group?.name\r\n            }\r\n          >\r\n            {isProcessing ? (\r\n              <>\r\n                <div className=\"animate-spin h-4 w-4 border-2 border-white rounded-full border-t-transparent mr-2\"></div>\r\n                Đang xử lý...\r\n              </>\r\n            ) : (\r\n              \"Xác nhận\"\r\n            )}\r\n          </Button>\r\n        </DialogFooter>\r\n      </DialogContent>\r\n    </Dialog>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAOA;AACA;AACA;AACA;AACA;;;AAdA;;;;;;;;AAyBe,SAAS,oBAAoB,EAC1C,KAAK,EACL,MAAM,EACN,YAAY,EACZ,MAAM,EACN,SAAS,EACgB;;IACzB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,OAAO,QAAQ;IAChE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,wCAAwC;IACxC,MAAM,mBAAmB,CAAC;QACxB,IAAI,MAAM;YACR,gBAAgB,OAAO,QAAQ;QACjC;QACA,aAAa;IACf;IAEA,MAAM,eAAe;QACnB,IAAI,CAAC,OAAO,IAAI;QAEhB,iBAAiB;QACjB,IAAI,CAAC,aAAa,IAAI,IAAI;YACxB,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,gBAAgB;QAChB,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,oIAAA,CAAA,cAAW,AAAD,EAAE,MAAM,EAAE,EAAE;gBAAE,MAAM,aAAa,IAAI;YAAG;YAEvE,IAAI,OAAO,OAAO,IAAI,OAAO,KAAK,EAAE;gBAClC,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBACd,aAAa;gBAEb,wCAAwC;gBACxC,IAAI,WAAW;oBACb,UAAU,OAAO,KAAK;gBACxB;YACF,OAAO;gBACL,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,OAAO,KAAK,IAAI;YAC9B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,qBACE,6LAAC,qIAAA,CAAA,SAAM;QAAC,MAAM;QAAQ,cAAc;kBAClC,cAAA,6LAAC,qIAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,6LAAC,qIAAA,CAAA,eAAY;oBAAC,WAAU;8BACtB,cAAA,6LAAC,qIAAA,CAAA,cAAW;kCAAC;;;;;;;;;;;8BAGf,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,qIAAA,CAAA,SAAM;4BAAC,WAAU;;8CAChB,6LAAC,qIAAA,CAAA,cAAW;oCACV,KAAK,OAAO,aAAa;oCACzB,WAAU;;;;;;8CAEZ,6LAAC,qIAAA,CAAA,iBAAc;oCAAC,WAAU;8CACvB,OAAO,MAAM,MAAM,GAAG,GAAG,iBAAiB;;;;;;;;;;;;sCAI/C,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,oIAAA,CAAA,QAAK;gCACJ,OAAO;gCACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;gCAC/C,aAAY;gCACZ,WAAU;gCACV,SAAS;;;;;;;;;;;sCAIb,6LAAC;4BAAE,WAAU;sCAAoC;;;;;;;;;;;;8BAMnD,6LAAC,qIAAA,CAAA,eAAY;oBAAC,WAAU;;sCACtB,6LAAC,qIAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAU,SAAS;4BAAQ,UAAU;sCAAc;;;;;;sCAGnE,6LAAC,qIAAA,CAAA,SAAM;4BACL,SAAS;4BACT,UACE,gBACA,CAAC,aAAa,IAAI,MAClB,iBAAiB,OAAO;sCAGzB,6BACC;;kDACE,6LAAC;wCAAI,WAAU;;;;;;oCAA0F;;+CAI3G;;;;;;;;;;;;;;;;;;;;;;;AAOd;GA7GwB;KAAA", "debugId": null}}, {"offset": {"line": 2393, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/src/components/group/GroupDialog.tsx"], "sourcesContent": ["\"use client\";\r\nimport {\r\n  <PERSON><PERSON>,\r\n  <PERSON><PERSON><PERSON>ontent,\r\n  <PERSON><PERSON><PERSON>eader,\r\n  DialogTitle,\r\n  DialogDescription,\r\n} from \"@/components/ui/dialog\";\r\nimport {\r\n  AlertDialog,\r\n  AlertDialogAction,\r\n  AlertDialogCancel,\r\n  AlertDialogContent,\r\n  AlertDialogDescription,\r\n  AlertDialogFooter,\r\n  AlertDialogHeader,\r\n  AlertDialogTitle,\r\n} from \"@/components/ui/alert-dialog\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport {\r\n  Camera,\r\n  Copy,\r\n  ExternalLink,\r\n  LogOut,\r\n  Settings,\r\n  Share2,\r\n  Video,\r\n  Trash,\r\n  Pencil,\r\n} from \"lucide-react\";\r\nimport { Group, Media, User, GroupRole } from \"@/types/base\";\r\nimport { Avatar, AvatarFallback, AvatarImage } from \"@/components/ui/avatar\";\r\nimport { useState, useEffect } from \"react\";\r\nimport { useChatStore } from \"@/stores/chatStore\";\r\nimport { useAuthStore } from \"@/stores/authStore\";\r\nimport { toast } from \"sonner\";\r\nimport { useRouter } from \"next/navigation\";\r\nimport {\r\n  deleteGroup,\r\n  leaveGroup,\r\n  updateGroupAvatar,\r\n  updateMemberRole,\r\n} from \"@/actions/group.action\";\r\nimport ProfileDialog from \"@/components/profile/ProfileDialog\";\r\nimport GroupMemberList from \"./GroupMemberList\";\r\nimport AddMemberDialog from \"./AddMemberDialog\";\r\nimport EditGroupNameDialog from \"./EditGroupNameDialog\";\r\nimport { batchGetUserData } from \"@/actions/user.action\";\r\n\r\ninterface GroupDialogProps {\r\n  group: Group | null;\r\n  isOpen: boolean;\r\n  onOpenChange: (open: boolean) => void;\r\n  mediaFiles?: Media[];\r\n}\r\n\r\nexport default function GroupDialog({\r\n  group,\r\n  isOpen,\r\n  onOpenChange,\r\n  mediaFiles = [],\r\n}: GroupDialogProps) {\r\n  const [showLeaveDialog, setShowLeaveDialog] = useState(false);\r\n  const [showTransferLeadershipDialog, setShowTransferLeadershipDialog] =\r\n    useState(false);\r\n  const [showConfirmTransferDialog, setShowConfirmTransferDialog] =\r\n    useState(false);\r\n  const [newLeaderId, setNewLeaderId] = useState<string | null>(null);\r\n  const [showDeleteDialog, setShowDeleteDialog] = useState(false);\r\n  const [isProcessing, setIsProcessing] = useState(false);\r\n  const [selectedMember, setSelectedMember] = useState<User | null>(null);\r\n  const [showProfileDialog, setShowProfileDialog] = useState(false);\r\n  const [showMemberList, setShowMemberList] = useState(false);\r\n  const [showAddMemberDialog, setShowAddMemberDialog] = useState(false);\r\n  const [showEditNameDialog, setShowEditNameDialog] = useState(false);\r\n  const [memberDetails, setMemberDetails] = useState<{ [key: string]: User }>(\r\n    {},\r\n  );\r\n  const [isUploadingAvatar, setIsUploadingAvatar] = useState(false);\r\n  const router = useRouter();\r\n\r\n  // Get current user and chat store functions\r\n  const currentUser = useAuthStore((state) => state.user);\r\n  const { setSelectedGroup } = useChatStore();\r\n\r\n  // Determine current user's role in the group\r\n  const currentUserRole =\r\n    group?.members?.find((member) => member.userId === currentUser?.id)?.role ||\r\n    \"MEMBER\";\r\n\r\n  // Fetch member details when group changes\r\n  useEffect(() => {\r\n    if (group?.id && group.members) {\r\n      const fetchMemberDetails = async () => {\r\n        const newMemberDetails: { [key: string]: User } = {};\r\n\r\n        try {\r\n          // Collect all user IDs that need to be fetched\r\n          const memberIds: string[] = [];\r\n\r\n          // First, use any existing user data\r\n          for (const member of group.members) {\r\n            if (member.user?.userInfo) {\r\n              newMemberDetails[member.userId] = member.user;\r\n            } else {\r\n              memberIds.push(member.userId);\r\n            }\r\n          }\r\n\r\n          // Batch fetch any missing user data\r\n          if (memberIds.length > 0) {\r\n            console.log(`Batch fetching ${memberIds.length} member details`);\r\n            const userResult = await batchGetUserData(memberIds);\r\n            if (userResult.success && userResult.users) {\r\n              userResult.users.forEach((user) => {\r\n                newMemberDetails[user.id] = user;\r\n              });\r\n            }\r\n          }\r\n        } catch (error) {\r\n          console.error(\"Error fetching member details:\", error);\r\n        }\r\n\r\n        setMemberDetails(newMemberDetails);\r\n      };\r\n\r\n      fetchMemberDetails();\r\n    }\r\n  }, [group?.id, group?.members]);\r\n\r\n  // Handle avatar change\r\n  const handleAvatarChange = async (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    if (!e.target.files || e.target.files.length === 0 || !group?.id) return;\r\n\r\n    const file = e.target.files[0];\r\n    setIsUploadingAvatar(true);\r\n\r\n    try {\r\n      const formData = new FormData();\r\n      formData.append(\"file\", file);\r\n\r\n      const result = await updateGroupAvatar(group.id, formData);\r\n\r\n      if (result.success) {\r\n        toast.success(\"Cập nhật ảnh đại diện nhóm thành công\");\r\n        // Refresh the group data or update the UI\r\n        // This could be done by refreshing the page or updating the group in the store\r\n        setTimeout(() => {\r\n          window.location.reload();\r\n        }, 500);\r\n      } else {\r\n        toast.error(result.error || \"Không thể cập nhật ảnh đại diện nhóm\");\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error updating group avatar:\", error);\r\n      toast.error(\"Đã xảy ra lỗi khi cập nhật ảnh đại diện nhóm\");\r\n    } finally {\r\n      setIsUploadingAvatar(false);\r\n    }\r\n  };\r\n\r\n  // Handle copy group link\r\n  const handleCopyGroupLink = () => {\r\n    if (!group?.id) return;\r\n\r\n    const groupLink = `https://zalo.me/g/${group.id}`;\r\n    navigator.clipboard.writeText(groupLink);\r\n    toast.success(\"Đã sao chép liên kết nhóm\");\r\n  };\r\n\r\n  // Hàm xử lý khi chọn thành viên để chuyển quyền trưởng nhóm\r\n  const handleSelectNewLeader = (memberId: string) => {\r\n    setNewLeaderId(memberId);\r\n    setShowConfirmTransferDialog(true);\r\n  };\r\n\r\n  // Hàm xử lý chuyển quyền trưởng nhóm\r\n  const executeTransferLeadership = async () => {\r\n    if (!group?.id || !newLeaderId) return;\r\n    setIsProcessing(true);\r\n    try {\r\n      // Chuyển quyền trưởng nhóm cho thành viên được chọn\r\n      const result = await updateMemberRole(\r\n        group.id,\r\n        newLeaderId,\r\n        GroupRole.LEADER,\r\n      );\r\n\r\n      if (result.success) {\r\n        // Đóng các dialog\r\n        setShowConfirmTransferDialog(false);\r\n        setShowTransferLeadershipDialog(false);\r\n\r\n        // Thông báo cho người dùng\r\n        toast.success(\"Đã chuyển quyền trưởng nhóm thành công\");\r\n\r\n        // Tiếp tục rời nhóm\r\n        setShowLeaveDialog(true);\r\n      } else {\r\n        toast.error(`Lỗi: ${result.error}`);\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error transferring leadership:\", error);\r\n      toast.error(\"Đã xảy ra lỗi khi chuyển quyền trưởng nhóm\");\r\n    } finally {\r\n      setIsProcessing(false);\r\n    }\r\n  };\r\n\r\n  // Handle leave group\r\n  const handleLeaveGroup = async () => {\r\n    if (!group?.id) return;\r\n    setIsProcessing(true);\r\n    try {\r\n      const result = await leaveGroup(group.id);\r\n      if (result.success) {\r\n        // Close confirmation dialog\r\n        setShowLeaveDialog(false);\r\n\r\n        // Get chat store and clear cache\r\n        const chatStore = useChatStore.getState();\r\n        chatStore.clearChatCache(\"GROUP\", group.id);\r\n        chatStore.setSelectedGroup(null);\r\n\r\n        // Close group dialog\r\n        onOpenChange(false);\r\n\r\n        // Notify user\r\n        toast.success(\"Đã rời nhóm thành công\");\r\n      } else {\r\n        toast.error(`Lỗi: ${result.error}`);\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error leaving group:\", error);\r\n      toast.error(\"Đã xảy ra lỗi khi rời nhóm\");\r\n    } finally {\r\n      setIsProcessing(false);\r\n    }\r\n  };\r\n\r\n  // Handle delete group\r\n  const handleDeleteGroup = async () => {\r\n    if (!group?.id) return;\r\n    setIsProcessing(true);\r\n    try {\r\n      const result = await deleteGroup(group.id);\r\n      if (result.success) {\r\n        // Close confirmation dialog\r\n        setShowDeleteDialog(false);\r\n\r\n        // Get chat store and clear cache\r\n        const chatStore = useChatStore.getState();\r\n        chatStore.clearChatCache(\"GROUP\", group.id);\r\n        chatStore.setSelectedGroup(null);\r\n\r\n        // Close group dialog\r\n        onOpenChange(false);\r\n\r\n        // Notify user\r\n        toast.success(\"Đã xóa nhóm thành công\");\r\n      } else {\r\n        toast.error(`Lỗi: ${result.error}`);\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error deleting group:\", error);\r\n      toast.error(\"Đã xảy ra lỗi khi xóa nhóm\");\r\n    } finally {\r\n      setIsProcessing(false);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <>\r\n      <Dialog open={isOpen} onOpenChange={onOpenChange}>\r\n        <DialogContent className=\"sm:max-w-[425px] h-auto !p-0 mt-0 mb-16 max-h-[90vh] overflow-y-auto no-scrollbar\">\r\n          <DialogHeader className=\"px-4 py-2 flex flex-row items-center border-b\">\r\n            <DialogTitle className=\"text-base font-semibold\">\r\n              Thông tin nhóm\r\n            </DialogTitle>\r\n            <DialogDescription className=\"sr-only\">\r\n              Xem và quản lý thông tin nhóm\r\n            </DialogDescription>\r\n            <Button\r\n              variant=\"ghost\"\r\n              size=\"icon\"\r\n              className=\"ml-auto h-8 w-8\"\r\n              onClick={() => onOpenChange(false)}\r\n            ></Button>\r\n          </DialogHeader>\r\n\r\n          <div className=\"flex flex-col gap-2 overflow-auto no-scrollbar bg-[#e5e7eb]\">\r\n            {/* Group Avatar and Name */}\r\n            <div className=\"flex flex-col gap-2 items-center text-center px-4 py-2 bg-white\">\r\n              <div className=\"flex flex-row items-center justify-start w-full gap-4\">\r\n                <div className=\"relative\">\r\n                  <Avatar className=\"h-16 w-16 border-2\">\r\n                    <AvatarImage\r\n                      src={group?.avatarUrl || undefined}\r\n                      className=\"object-cover\"\r\n                    />\r\n                    <AvatarFallback className=\"text-xl\">\r\n                      {group?.name?.slice(0, 2).toUpperCase() || \"GR\"}\r\n                    </AvatarFallback>\r\n                  </Avatar>\r\n                  {currentUserRole === \"LEADER\" && (\r\n                    <label\r\n                      htmlFor=\"group-avatar-upload\"\r\n                      className=\"absolute bottom-0 right-0 bg-blue-500 rounded-full p-1 cursor-pointer\"\r\n                    >\r\n                      {isUploadingAvatar ? (\r\n                        <div className=\"h-4 w-4 border-2 border-white border-t-transparent rounded-full animate-spin\"></div>\r\n                      ) : (\r\n                        <Camera className=\"h-4 w-4 text-white\" />\r\n                      )}\r\n                      <input\r\n                        id=\"group-avatar-upload\"\r\n                        type=\"file\"\r\n                        accept=\"image/*\"\r\n                        className=\"hidden\"\r\n                        onChange={handleAvatarChange}\r\n                        disabled={isUploadingAvatar}\r\n                      />\r\n                    </label>\r\n                  )}\r\n                </div>\r\n                <div className=\"flex items-center gap-2\">\r\n                  <h2 className=\"text-base font-semibold\">{group?.name}</h2>\r\n                  {currentUserRole === \"LEADER\" && (\r\n                    <button\r\n                      className=\"text-gray-500 hover:text-blue-500 transition-colors\"\r\n                      onClick={() => setShowEditNameDialog(true)}\r\n                    >\r\n                      <Pencil className=\"h-4 w-4\" />\r\n                    </button>\r\n                  )}\r\n                </div>\r\n              </div>\r\n              {/* Message Button */}\r\n              <div\r\n                className=\"w-full bg-[#e5e7eb] py-2 px-4 text-center cursor-pointer hover:bg-gray-200\"\r\n                onClick={() => {\r\n                  if (group?.id) {\r\n                    // Close the dialog\r\n                    onOpenChange(false);\r\n\r\n                    // Open the chat with this group\r\n                    setSelectedGroup(group);\r\n\r\n                    // Navigate to chat page if not already there\r\n                    router.push(\"/dashboard/chat\");\r\n                  }\r\n                }}\r\n              >\r\n                <span className=\"font-semibold\">Nhắn tin</span>\r\n              </div>\r\n            </div>\r\n\r\n            {/* Members Section */}\r\n            <div className=\"p-4 bg-white border-b border-gray-200\">\r\n              <div className=\"flex items-center justify-between mb-3\">\r\n                <h3 className=\"font-semibold\">\r\n                  Thành viên ({group?.members?.length || 0})\r\n                </h3>\r\n              </div>\r\n              <div className=\"flex justify-center gap-2\">\r\n                {group?.members?.slice(0, 4).map((member) => {\r\n                  const memberData = memberDetails[member.userId];\r\n                  const initials = memberData?.userInfo?.fullName\r\n                    ? memberData.userInfo.fullName.slice(0, 2).toUpperCase()\r\n                    : \"U\";\r\n                  const displayName =\r\n                    memberData?.userInfo?.fullName || \"Thành viên\";\r\n                  const isLeader = member.role === \"LEADER\";\r\n                  const isCoLeader = member.role === \"CO_LEADER\";\r\n\r\n                  return (\r\n                    <div\r\n                      key={member.userId}\r\n                      className=\"flex flex-col items-center\"\r\n                    >\r\n                      <Avatar\r\n                        className=\"h-12 w-12 mb-1 cursor-pointer\"\r\n                        onClick={() => {\r\n                          if (memberData) {\r\n                            setSelectedMember(memberData);\r\n                            setShowProfileDialog(true);\r\n                          }\r\n                        }}\r\n                      >\r\n                        <AvatarImage\r\n                          src={\r\n                            memberData?.userInfo?.profilePictureUrl || undefined\r\n                          }\r\n                          className=\"object-cover\"\r\n                        />\r\n                        <AvatarFallback>{initials}</AvatarFallback>\r\n                      </Avatar>\r\n                      <span className=\"text-xs font-medium truncate w-16 text-center\">\r\n                        {displayName}\r\n                      </span>\r\n                      {(isLeader || isCoLeader) && (\r\n                        <span className=\"text-xs text-gray-500\">\r\n                          {isLeader ? \"Trưởng nhóm\" : \"Phó nhóm\"}\r\n                        </span>\r\n                      )}\r\n                    </div>\r\n                  );\r\n                })}\r\n                {(group?.members?.length || 0) > 4 && (\r\n                  <div className=\"flex flex-col items-center\">\r\n                    <div\r\n                      className=\"h-12 w-12 rounded-full bg-gray-200 flex items-center justify-center mb-1 cursor-pointer\"\r\n                      onClick={() => setShowMemberList(true)}\r\n                    >\r\n                      <span className=\"text-sm font-medium\">...</span>\r\n                    </div>\r\n                    <span className=\"text-xs font-medium\">Xem thêm</span>\r\n                  </div>\r\n                )}\r\n              </div>\r\n            </div>\r\n\r\n            {/* Media Section */}\r\n            <div className=\"p-4 bg-white border-b border-gray-200\">\r\n              <div className=\"mb-3\">\r\n                <h3 className=\"font-semibold\">Ảnh/Video</h3>\r\n              </div>\r\n              {mediaFiles.length > 0 ? (\r\n                <div className=\"grid grid-cols-4 gap-1\">\r\n                  {mediaFiles.slice(0, 4).map((media, index) => (\r\n                    <div\r\n                      key={index}\r\n                      className=\"aspect-square relative overflow-hidden border border-gray-200 rounded-md cursor-pointer\"\r\n                    >\r\n                      <div\r\n                        className=\"w-full h-full bg-cover bg-center\"\r\n                        style={{ backgroundImage: `url(${media.url})` }}\r\n                      ></div>\r\n                      {media.metadata?.extension?.match(/mp4|webm|mov/i) && (\r\n                        <div className=\"absolute inset-0 flex items-center justify-center bg-black/30\">\r\n                          <Video className=\"h-6 w-6 text-white\" />\r\n                        </div>\r\n                      )}\r\n                    </div>\r\n                  ))}\r\n                </div>\r\n              ) : (\r\n                <div className=\"text-center text-gray-500 py-2\">\r\n                  <p>Chưa có ảnh/video nào</p>\r\n                </div>\r\n              )}\r\n            </div>\r\n\r\n            {/* Link Section */}\r\n            <div className=\"p-4 bg-white border-b border-gray-200\">\r\n              <div className=\"flex items-center mb-3\">\r\n                <h3 className=\"font-semibold\">Link tham gia nhóm</h3>\r\n              </div>\r\n              <div className=\"flex items-center\">\r\n                <div className=\"flex items-center\">\r\n                  <div className=\"mr-2\">\r\n                    <ExternalLink className=\"h-5 w-5 text-gray-500\" />\r\n                  </div>\r\n                  <div className=\"text-sm text-blue-500\">\r\n                    https://zalo.me/g/{group?.id || \"lqgvcn149\"}\r\n                  </div>\r\n                </div>\r\n                <div className=\"ml-auto flex\">\r\n                  <Button\r\n                    variant=\"ghost\"\r\n                    size=\"icon\"\r\n                    className=\"h-8 w-8 rounded-full bg-gray-200\"\r\n                    onClick={handleCopyGroupLink}\r\n                  >\r\n                    <Copy className=\"h-4 w-4\" />\r\n                  </Button>\r\n                  <Button\r\n                    variant=\"ghost\"\r\n                    size=\"icon\"\r\n                    className=\"h-8 w-8 rounded-full bg-gray-200 ml-2\"\r\n                    onClick={() => {\r\n                      if (navigator.share && group?.id) {\r\n                        navigator\r\n                          .share({\r\n                            title: `Nhóm ${group.name || \"chat\"}`,\r\n                            text: `Tham gia nhóm ${group.name || \"chat\"} trên Zalo`,\r\n                            url: `https://zalo.me/g/${group.id}`,\r\n                          })\r\n                          .catch((err) => {\r\n                            console.error(\"Error sharing:\", err);\r\n                          });\r\n                      } else {\r\n                        handleCopyGroupLink();\r\n                        toast.info(\r\n                          \"Đã sao chép liên kết. Thiết bị của bạn không hỗ trợ chia sẻ trực tiếp.\",\r\n                        );\r\n                      }\r\n                    }}\r\n                  >\r\n                    <Share2 className=\"h-4 w-4\" />\r\n                  </Button>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            {/* Group Management Buttons */}\r\n            <div className=\"p-4 bg-white space-y-2\">\r\n              {/* Manage group button */}\r\n              <div\r\n                className=\"flex items-center p-2 cursor-pointer opacity-60\"\r\n                onClick={() => {\r\n                  toast.info(\"Tính năng này chưa được hỗ trợ\");\r\n                }}\r\n              >\r\n                <Settings className=\"h-5 w-5 mr-3 text-gray-500\" />\r\n                <span className=\"text-sm\">Quản lý nhóm</span>\r\n              </div>\r\n\r\n              {/* Leave group option - for everyone */}\r\n              {/* Delete group option - only for leader */}\r\n              {currentUserRole === \"LEADER\" && (\r\n                <div\r\n                  className=\"flex items-center p-2 cursor-pointer text-red-500\"\r\n                  onClick={() => setShowDeleteDialog(true)}\r\n                >\r\n                  <Trash className=\"h-5 w-5 mr-3\" />\r\n                  <span className=\"text-sm\">Xóa nhóm</span>\r\n                </div>\r\n              )}\r\n\r\n              {/* Nút rời nhóm hiển thị cho tất cả thành viên, trừ khi trưởng nhóm là thành viên duy nhất */}\r\n              {!(\r\n                currentUserRole === \"LEADER\" && group?.members?.length === 1\r\n              ) && (\r\n                <div\r\n                  className=\"flex items-center p-2 cursor-pointer text-red-500\"\r\n                  onClick={() => {\r\n                    // Nếu là trưởng nhóm, hiển thị dialog chuyển quyền trưởng nhóm\r\n                    if (currentUserRole === \"LEADER\") {\r\n                      setShowTransferLeadershipDialog(true);\r\n                    } else {\r\n                      setShowLeaveDialog(true);\r\n                    }\r\n                  }}\r\n                >\r\n                  <LogOut className=\"h-5 w-5 mr-3\" />\r\n                  <span className=\"text-sm\">Rời nhóm</span>\r\n                </div>\r\n              )}\r\n            </div>\r\n          </div>\r\n        </DialogContent>\r\n      </Dialog>\r\n\r\n      {/* Profile Dialog for members */}\r\n      {showProfileDialog && selectedMember && (\r\n        <ProfileDialog\r\n          user={selectedMember}\r\n          isOpen={showProfileDialog}\r\n          onOpenChange={setShowProfileDialog}\r\n          isOwnProfile={selectedMember.id === currentUser?.id}\r\n        />\r\n      )}\r\n\r\n      {/* Group Member List Dialog */}\r\n      <GroupMemberList\r\n        group={group}\r\n        isOpen={showMemberList}\r\n        onOpenChange={setShowMemberList}\r\n        onBack={() => {\r\n          setShowMemberList(false);\r\n        }}\r\n      />\r\n\r\n      {/* Add Member Dialog */}\r\n      {group?.id && (\r\n        <AddMemberDialog\r\n          groupId={group.id}\r\n          isOpen={showAddMemberDialog}\r\n          onOpenChange={setShowAddMemberDialog}\r\n        />\r\n      )}\r\n\r\n      {/* Leave Group Confirmation Dialog */}\r\n      <AlertDialog open={showLeaveDialog} onOpenChange={setShowLeaveDialog}>\r\n        <AlertDialogContent>\r\n          <AlertDialogHeader>\r\n            <AlertDialogTitle>Rời nhóm</AlertDialogTitle>\r\n            <AlertDialogDescription>\r\n              Bạn có chắc chắn muốn rời khỏi nhóm &quot;{group?.name}&quot;? Bạn\r\n              sẽ không thể xem tin nhắn trong nhóm này nữa.\r\n            </AlertDialogDescription>\r\n          </AlertDialogHeader>\r\n          <AlertDialogFooter>\r\n            <AlertDialogCancel disabled={isProcessing}>Hủy</AlertDialogCancel>\r\n            <AlertDialogAction\r\n              onClick={handleLeaveGroup}\r\n              disabled={isProcessing}\r\n              className=\"bg-red-500 hover:bg-red-600\"\r\n            >\r\n              {isProcessing ? (\r\n                <>\r\n                  <div className=\"animate-spin h-4 w-4 border-2 border-white rounded-full border-t-transparent mr-2\"></div>\r\n                  Đang xử lý...\r\n                </>\r\n              ) : (\r\n                \"Rời nhóm\"\r\n              )}\r\n            </AlertDialogAction>\r\n          </AlertDialogFooter>\r\n        </AlertDialogContent>\r\n      </AlertDialog>\r\n\r\n      {/* Dialog chuyển quyền trưởng nhóm */}\r\n      <AlertDialog\r\n        open={showTransferLeadershipDialog}\r\n        onOpenChange={setShowTransferLeadershipDialog}\r\n      >\r\n        <AlertDialogContent>\r\n          <AlertDialogHeader>\r\n            <AlertDialogTitle>Chuyển quyền trưởng nhóm</AlertDialogTitle>\r\n            <AlertDialogDescription>\r\n              Bạn cần chuyển quyền trưởng nhóm cho một thành viên khác trước khi\r\n              rời nhóm. Vui lòng chọn một thành viên để trở thành trưởng nhóm\r\n              mới.\r\n            </AlertDialogDescription>\r\n          </AlertDialogHeader>\r\n          <div className=\"max-h-[200px] overflow-y-auto my-4 border rounded-md\">\r\n            {group?.members\r\n              ?.filter((member) => member.userId !== currentUser?.id) // Lọc ra các thành viên khác\r\n              .map((member) => {\r\n                const memberData = memberDetails[member.userId];\r\n                const initials = memberData?.userInfo?.fullName\r\n                  ? memberData.userInfo.fullName.slice(0, 2).toUpperCase()\r\n                  : \"??\";\r\n\r\n                return (\r\n                  <div\r\n                    key={member.userId}\r\n                    className=\"flex items-center p-3 hover:bg-gray-100 cursor-pointer\"\r\n                    onClick={() => handleSelectNewLeader(member.userId)}\r\n                  >\r\n                    <Avatar className=\"h-8 w-8 mr-3\">\r\n                      <AvatarImage\r\n                        src={\r\n                          memberData?.userInfo?.profilePictureUrl || undefined\r\n                        }\r\n                        className=\"object-cover\"\r\n                      />\r\n                      <AvatarFallback className=\"bg-gray-200 text-gray-600\">\r\n                        {initials}\r\n                      </AvatarFallback>\r\n                    </Avatar>\r\n                    <div>\r\n                      <p className=\"font-medium\">\r\n                        {memberData?.userInfo?.fullName || \"Thành viên\"}\r\n                      </p>\r\n                      <p className=\"text-xs text-gray-500\">\r\n                        {member.role === \"CO_LEADER\"\r\n                          ? \"Phó nhóm\"\r\n                          : \"Thành viên\"}\r\n                      </p>\r\n                    </div>\r\n                  </div>\r\n                );\r\n              })}\r\n          </div>\r\n          <AlertDialogFooter>\r\n            <AlertDialogCancel disabled={isProcessing}>Hủy</AlertDialogCancel>\r\n          </AlertDialogFooter>\r\n        </AlertDialogContent>\r\n      </AlertDialog>\r\n\r\n      {/* Dialog xác nhận chuyển quyền trưởng nhóm */}\r\n      <AlertDialog\r\n        open={showConfirmTransferDialog}\r\n        onOpenChange={setShowConfirmTransferDialog}\r\n      >\r\n        <AlertDialogContent>\r\n          <AlertDialogHeader>\r\n            <AlertDialogTitle>\r\n              Xác nhận chuyển quyền trưởng nhóm\r\n            </AlertDialogTitle>\r\n            <AlertDialogDescription>\r\n              {newLeaderId && memberDetails[newLeaderId] ? (\r\n                <>\r\n                  Bạn có chắc chắn muốn chuyển quyền trưởng nhóm cho{\" \"}\r\n                  <strong>\r\n                    {memberDetails[newLeaderId]?.userInfo?.fullName ||\r\n                      \"Thành viên này\"}\r\n                  </strong>\r\n                  ?\r\n                  <br />\r\n                  Sau khi chuyển quyền, bạn sẽ trở thành thành viên thường trong\r\n                  nhóm.\r\n                </>\r\n              ) : (\r\n                \"Bạn có chắc chắn muốn chuyển quyền trưởng nhóm cho thành viên này?\"\r\n              )}\r\n            </AlertDialogDescription>\r\n          </AlertDialogHeader>\r\n          <AlertDialogFooter>\r\n            <AlertDialogCancel\r\n              disabled={isProcessing}\r\n              onClick={() => {\r\n                setShowConfirmTransferDialog(false);\r\n                setNewLeaderId(null);\r\n              }}\r\n            >\r\n              Hủy\r\n            </AlertDialogCancel>\r\n            <AlertDialogAction\r\n              onClick={executeTransferLeadership}\r\n              disabled={isProcessing}\r\n              className=\"bg-blue-500 hover:bg-blue-600\"\r\n            >\r\n              {isProcessing ? (\r\n                <>\r\n                  <div className=\"animate-spin h-4 w-4 border-2 border-white rounded-full border-t-transparent mr-2\"></div>\r\n                  Đang xử lý...\r\n                </>\r\n              ) : (\r\n                \"Xác nhận\"\r\n              )}\r\n            </AlertDialogAction>\r\n          </AlertDialogFooter>\r\n        </AlertDialogContent>\r\n      </AlertDialog>\r\n\r\n      {/* Edit Group Name Dialog */}\r\n      {group && (\r\n        <EditGroupNameDialog\r\n          group={group}\r\n          isOpen={showEditNameDialog}\r\n          onOpenChange={setShowEditNameDialog}\r\n          onBack={() => setShowEditNameDialog(false)}\r\n          onSuccess={(updatedGroup) => {\r\n            // Update the group in the store\r\n            const chatStore = useChatStore.getState();\r\n            if (chatStore.selectedGroup?.id === updatedGroup.id) {\r\n              chatStore.setSelectedGroup(updatedGroup);\r\n            }\r\n\r\n            // Refresh the page after a short delay to ensure all components are updated\r\n            setTimeout(() => {\r\n              window.location.reload();\r\n            }, 500);\r\n          }}\r\n        />\r\n      )}\r\n\r\n      {/* Delete Group Confirmation Dialog */}\r\n      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>\r\n        <AlertDialogContent>\r\n          <AlertDialogHeader>\r\n            <AlertDialogTitle>Xóa nhóm</AlertDialogTitle>\r\n            <AlertDialogDescription>\r\n              Bạn có chắc chắn muốn xóa nhóm &quot;{group?.name}&quot;? Hành\r\n              động này không thể hoàn tác và tất cả tin nhắn trong nhóm sẽ bị\r\n              xóa.\r\n            </AlertDialogDescription>\r\n          </AlertDialogHeader>\r\n          <AlertDialogFooter>\r\n            <AlertDialogCancel disabled={isProcessing}>Hủy</AlertDialogCancel>\r\n            <AlertDialogAction\r\n              onClick={handleDeleteGroup}\r\n              disabled={isProcessing}\r\n              className=\"bg-red-500 hover:bg-red-600\"\r\n            >\r\n              {isProcessing ? (\r\n                <>\r\n                  <div className=\"animate-spin h-4 w-4 border-2 border-white rounded-full border-t-transparent mr-2\"></div>\r\n                  Đang xử lý...\r\n                </>\r\n              ) : (\r\n                \"Xóa nhóm\"\r\n              )}\r\n            </AlertDialogAction>\r\n          </AlertDialogFooter>\r\n        </AlertDialogContent>\r\n      </AlertDialog>\r\n    </>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AACA;AAOA;AAUA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAMA;AACA;AACA;AACA;AACA;;;AA/CA;;;;;;;;;;;;;;;;;;AAwDe,SAAS,YAAY,EAClC,KAAK,EACL,MAAM,EACN,YAAY,EACZ,aAAa,EAAE,EACE;;IACjB,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,8BAA8B,gCAAgC,GACnE,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACX,MAAM,CAAC,2BAA2B,6BAA6B,GAC7D,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACX,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAC9D,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAClE,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAC/C,CAAC;IAEH,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,4CAA4C;IAC5C,MAAM,cAAc,CAAA,GAAA,6HAAA,CAAA,eAAY,AAAD;iDAAE,CAAC,QAAU,MAAM,IAAI;;IACtD,MAAM,EAAE,gBAAgB,EAAE,GAAG,CAAA,GAAA,6HAAA,CAAA,eAAY,AAAD;IAExC,6CAA6C;IAC7C,MAAM,kBACJ,OAAO,SAAS,KAAK,CAAC,SAAW,OAAO,MAAM,KAAK,aAAa,KAAK,QACrE;IAEF,0CAA0C;IAC1C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,IAAI,OAAO,MAAM,MAAM,OAAO,EAAE;gBAC9B,MAAM;gEAAqB;wBACzB,MAAM,mBAA4C,CAAC;wBAEnD,IAAI;4BACF,+CAA+C;4BAC/C,MAAM,YAAsB,EAAE;4BAE9B,oCAAoC;4BACpC,KAAK,MAAM,UAAU,MAAM,OAAO,CAAE;gCAClC,IAAI,OAAO,IAAI,EAAE,UAAU;oCACzB,gBAAgB,CAAC,OAAO,MAAM,CAAC,GAAG,OAAO,IAAI;gCAC/C,OAAO;oCACL,UAAU,IAAI,CAAC,OAAO,MAAM;gCAC9B;4BACF;4BAEA,oCAAoC;4BACpC,IAAI,UAAU,MAAM,GAAG,GAAG;gCACxB,QAAQ,GAAG,CAAC,CAAC,eAAe,EAAE,UAAU,MAAM,CAAC,eAAe,CAAC;gCAC/D,MAAM,aAAa,MAAM,CAAA,GAAA,mIAAA,CAAA,mBAAgB,AAAD,EAAE;gCAC1C,IAAI,WAAW,OAAO,IAAI,WAAW,KAAK,EAAE;oCAC1C,WAAW,KAAK,CAAC,OAAO;oFAAC,CAAC;4CACxB,gBAAgB,CAAC,KAAK,EAAE,CAAC,GAAG;wCAC9B;;gCACF;4BACF;wBACF,EAAE,OAAO,OAAO;4BACd,QAAQ,KAAK,CAAC,kCAAkC;wBAClD;wBAEA,iBAAiB;oBACnB;;gBAEA;YACF;QACF;gCAAG;QAAC,OAAO;QAAI,OAAO;KAAQ;IAE9B,uBAAuB;IACvB,MAAM,qBAAqB,OAAO;QAChC,IAAI,CAAC,EAAE,MAAM,CAAC,KAAK,IAAI,EAAE,MAAM,CAAC,KAAK,CAAC,MAAM,KAAK,KAAK,CAAC,OAAO,IAAI;QAElE,MAAM,OAAO,EAAE,MAAM,CAAC,KAAK,CAAC,EAAE;QAC9B,qBAAqB;QAErB,IAAI;YACF,MAAM,WAAW,IAAI;YACrB,SAAS,MAAM,CAAC,QAAQ;YAExB,MAAM,SAAS,MAAM,CAAA,GAAA,oIAAA,CAAA,oBAAiB,AAAD,EAAE,MAAM,EAAE,EAAE;YAEjD,IAAI,OAAO,OAAO,EAAE;gBAClB,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBACd,0CAA0C;gBAC1C,+EAA+E;gBAC/E,WAAW;oBACT,OAAO,QAAQ,CAAC,MAAM;gBACxB,GAAG;YACL,OAAO;gBACL,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,OAAO,KAAK,IAAI;YAC9B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,qBAAqB;QACvB;IACF;IAEA,yBAAyB;IACzB,MAAM,sBAAsB;QAC1B,IAAI,CAAC,OAAO,IAAI;QAEhB,MAAM,YAAY,CAAC,kBAAkB,EAAE,MAAM,EAAE,EAAE;QACjD,UAAU,SAAS,CAAC,SAAS,CAAC;QAC9B,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;IAChB;IAEA,4DAA4D;IAC5D,MAAM,wBAAwB,CAAC;QAC7B,eAAe;QACf,6BAA6B;IAC/B;IAEA,qCAAqC;IACrC,MAAM,4BAA4B;QAChC,IAAI,CAAC,OAAO,MAAM,CAAC,aAAa;QAChC,gBAAgB;QAChB,IAAI;YACF,oDAAoD;YACpD,MAAM,SAAS,MAAM,CAAA,GAAA,oIAAA,CAAA,mBAAgB,AAAD,EAClC,MAAM,EAAE,EACR,aACA,uHAAA,CAAA,YAAS,CAAC,MAAM;YAGlB,IAAI,OAAO,OAAO,EAAE;gBAClB,kBAAkB;gBAClB,6BAA6B;gBAC7B,gCAAgC;gBAEhC,2BAA2B;gBAC3B,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBAEd,oBAAoB;gBACpB,mBAAmB;YACrB,OAAO;gBACL,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,OAAO,KAAK,EAAE;YACpC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;YAChD,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,qBAAqB;IACrB,MAAM,mBAAmB;QACvB,IAAI,CAAC,OAAO,IAAI;QAChB,gBAAgB;QAChB,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,oIAAA,CAAA,aAAU,AAAD,EAAE,MAAM,EAAE;YACxC,IAAI,OAAO,OAAO,EAAE;gBAClB,4BAA4B;gBAC5B,mBAAmB;gBAEnB,iCAAiC;gBACjC,MAAM,YAAY,6HAAA,CAAA,eAAY,CAAC,QAAQ;gBACvC,UAAU,cAAc,CAAC,SAAS,MAAM,EAAE;gBAC1C,UAAU,gBAAgB,CAAC;gBAE3B,qBAAqB;gBACrB,aAAa;gBAEb,cAAc;gBACd,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB,OAAO;gBACL,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,OAAO,KAAK,EAAE;YACpC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;YACtC,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,sBAAsB;IACtB,MAAM,oBAAoB;QACxB,IAAI,CAAC,OAAO,IAAI;QAChB,gBAAgB;QAChB,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,oIAAA,CAAA,cAAW,AAAD,EAAE,MAAM,EAAE;YACzC,IAAI,OAAO,OAAO,EAAE;gBAClB,4BAA4B;gBAC5B,oBAAoB;gBAEpB,iCAAiC;gBACjC,MAAM,YAAY,6HAAA,CAAA,eAAY,CAAC,QAAQ;gBACvC,UAAU,cAAc,CAAC,SAAS,MAAM,EAAE;gBAC1C,UAAU,gBAAgB,CAAC;gBAE3B,qBAAqB;gBACrB,aAAa;gBAEb,cAAc;gBACd,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB,OAAO;gBACL,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,OAAO,KAAK,EAAE;YACpC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,qBACE;;0BACE,6LAAC,qIAAA,CAAA,SAAM;gBAAC,MAAM;gBAAQ,cAAc;0BAClC,cAAA,6LAAC,qIAAA,CAAA,gBAAa;oBAAC,WAAU;;sCACvB,6LAAC,qIAAA,CAAA,eAAY;4BAAC,WAAU;;8CACtB,6LAAC,qIAAA,CAAA,cAAW;oCAAC,WAAU;8CAA0B;;;;;;8CAGjD,6LAAC,qIAAA,CAAA,oBAAiB;oCAAC,WAAU;8CAAU;;;;;;8CAGvC,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAU;oCACV,SAAS,IAAM,aAAa;;;;;;;;;;;;sCAIhC,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,qIAAA,CAAA,SAAM;4DAAC,WAAU;;8EAChB,6LAAC,qIAAA,CAAA,cAAW;oEACV,KAAK,OAAO,aAAa;oEACzB,WAAU;;;;;;8EAEZ,6LAAC,qIAAA,CAAA,iBAAc;oEAAC,WAAU;8EACvB,OAAO,MAAM,MAAM,GAAG,GAAG,iBAAiB;;;;;;;;;;;;wDAG9C,oBAAoB,0BACnB,6LAAC;4DACC,SAAQ;4DACR,WAAU;;gEAET,kCACC,6LAAC;oEAAI,WAAU;;;;;yFAEf,6LAAC,yMAAA,CAAA,SAAM;oEAAC,WAAU;;;;;;8EAEpB,6LAAC;oEACC,IAAG;oEACH,MAAK;oEACL,QAAO;oEACP,WAAU;oEACV,UAAU;oEACV,UAAU;;;;;;;;;;;;;;;;;;8DAKlB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAG,WAAU;sEAA2B,OAAO;;;;;;wDAC/C,oBAAoB,0BACnB,6LAAC;4DACC,WAAU;4DACV,SAAS,IAAM,sBAAsB;sEAErC,cAAA,6LAAC,yMAAA,CAAA,SAAM;gEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;sDAM1B,6LAAC;4CACC,WAAU;4CACV,SAAS;gDACP,IAAI,OAAO,IAAI;oDACb,mBAAmB;oDACnB,aAAa;oDAEb,gCAAgC;oDAChC,iBAAiB;oDAEjB,6CAA6C;oDAC7C,OAAO,IAAI,CAAC;gDACd;4CACF;sDAEA,cAAA,6LAAC;gDAAK,WAAU;0DAAgB;;;;;;;;;;;;;;;;;8CAKpC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAG,WAAU;;oDAAgB;oDACf,OAAO,SAAS,UAAU;oDAAE;;;;;;;;;;;;sDAG7C,6LAAC;4CAAI,WAAU;;gDACZ,OAAO,SAAS,MAAM,GAAG,GAAG,IAAI,CAAC;oDAChC,MAAM,aAAa,aAAa,CAAC,OAAO,MAAM,CAAC;oDAC/C,MAAM,WAAW,YAAY,UAAU,WACnC,WAAW,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,GAAG,WAAW,KACpD;oDACJ,MAAM,cACJ,YAAY,UAAU,YAAY;oDACpC,MAAM,WAAW,OAAO,IAAI,KAAK;oDACjC,MAAM,aAAa,OAAO,IAAI,KAAK;oDAEnC,qBACE,6LAAC;wDAEC,WAAU;;0EAEV,6LAAC,qIAAA,CAAA,SAAM;gEACL,WAAU;gEACV,SAAS;oEACP,IAAI,YAAY;wEACd,kBAAkB;wEAClB,qBAAqB;oEACvB;gEACF;;kFAEA,6LAAC,qIAAA,CAAA,cAAW;wEACV,KACE,YAAY,UAAU,qBAAqB;wEAE7C,WAAU;;;;;;kFAEZ,6LAAC,qIAAA,CAAA,iBAAc;kFAAE;;;;;;;;;;;;0EAEnB,6LAAC;gEAAK,WAAU;0EACb;;;;;;4DAEF,CAAC,YAAY,UAAU,mBACtB,6LAAC;gEAAK,WAAU;0EACb,WAAW,gBAAgB;;;;;;;uDAzB3B,OAAO,MAAM;;;;;gDA8BxB;gDACC,CAAC,OAAO,SAAS,UAAU,CAAC,IAAI,mBAC/B,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DACC,WAAU;4DACV,SAAS,IAAM,kBAAkB;sEAEjC,cAAA,6LAAC;gEAAK,WAAU;0EAAsB;;;;;;;;;;;sEAExC,6LAAC;4DAAK,WAAU;sEAAsB;;;;;;;;;;;;;;;;;;;;;;;;8CAO9C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAG,WAAU;0DAAgB;;;;;;;;;;;wCAE/B,WAAW,MAAM,GAAG,kBACnB,6LAAC;4CAAI,WAAU;sDACZ,WAAW,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,OAAO,sBAClC,6LAAC;oDAEC,WAAU;;sEAEV,6LAAC;4DACC,WAAU;4DACV,OAAO;gEAAE,iBAAiB,CAAC,IAAI,EAAE,MAAM,GAAG,CAAC,CAAC,CAAC;4DAAC;;;;;;wDAE/C,MAAM,QAAQ,EAAE,WAAW,MAAM,kCAChC,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;;;;;;;mDAThB;;;;;;;;;iEAgBX,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;0DAAE;;;;;;;;;;;;;;;;;8CAMT,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAG,WAAU;0DAAgB;;;;;;;;;;;sDAEhC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC,yNAAA,CAAA,eAAY;gEAAC,WAAU;;;;;;;;;;;sEAE1B,6LAAC;4DAAI,WAAU;;gEAAwB;gEAClB,OAAO,MAAM;;;;;;;;;;;;;8DAGpC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,qIAAA,CAAA,SAAM;4DACL,SAAQ;4DACR,MAAK;4DACL,WAAU;4DACV,SAAS;sEAET,cAAA,6LAAC,qMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;;;;;;sEAElB,6LAAC,qIAAA,CAAA,SAAM;4DACL,SAAQ;4DACR,MAAK;4DACL,WAAU;4DACV,SAAS;gEACP,IAAI,UAAU,KAAK,IAAI,OAAO,IAAI;oEAChC,UACG,KAAK,CAAC;wEACL,OAAO,CAAC,KAAK,EAAE,MAAM,IAAI,IAAI,QAAQ;wEACrC,MAAM,CAAC,cAAc,EAAE,MAAM,IAAI,IAAI,OAAO,UAAU,CAAC;wEACvD,KAAK,CAAC,kBAAkB,EAAE,MAAM,EAAE,EAAE;oEACtC,GACC,KAAK,CAAC,CAAC;wEACN,QAAQ,KAAK,CAAC,kBAAkB;oEAClC;gEACJ,OAAO;oEACL;oEACA,2IAAA,CAAA,QAAK,CAAC,IAAI,CACR;gEAEJ;4DACF;sEAEA,cAAA,6LAAC,6MAAA,CAAA,SAAM;gEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAO1B,6LAAC;oCAAI,WAAU;;sDAEb,6LAAC;4CACC,WAAU;4CACV,SAAS;gDACP,2IAAA,CAAA,QAAK,CAAC,IAAI,CAAC;4CACb;;8DAEA,6LAAC,6MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;8DACpB,6LAAC;oDAAK,WAAU;8DAAU;;;;;;;;;;;;wCAK3B,oBAAoB,0BACnB,6LAAC;4CACC,WAAU;4CACV,SAAS,IAAM,oBAAoB;;8DAEnC,6LAAC,uMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;8DACjB,6LAAC;oDAAK,WAAU;8DAAU;;;;;;;;;;;;wCAK7B,CAAC,CACA,oBAAoB,YAAY,OAAO,SAAS,WAAW,CAC7D,mBACE,6LAAC;4CACC,WAAU;4CACV,SAAS;gDACP,+DAA+D;gDAC/D,IAAI,oBAAoB,UAAU;oDAChC,gCAAgC;gDAClC,OAAO;oDACL,mBAAmB;gDACrB;4CACF;;8DAEA,6LAAC,6MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,6LAAC;oDAAK,WAAU;8DAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YASrC,qBAAqB,gCACpB,6LAAC,iJAAA,CAAA,UAAa;gBACZ,MAAM;gBACN,QAAQ;gBACR,cAAc;gBACd,cAAc,eAAe,EAAE,KAAK,aAAa;;;;;;0BAKrD,6LAAC,iJAAA,CAAA,UAAe;gBACd,OAAO;gBACP,QAAQ;gBACR,cAAc;gBACd,QAAQ;oBACN,kBAAkB;gBACpB;;;;;;YAID,OAAO,oBACN,6LAAC,iJAAA,CAAA,UAAe;gBACd,SAAS,MAAM,EAAE;gBACjB,QAAQ;gBACR,cAAc;;;;;;0BAKlB,6LAAC,8IAAA,CAAA,cAAW;gBAAC,MAAM;gBAAiB,cAAc;0BAChD,cAAA,6LAAC,8IAAA,CAAA,qBAAkB;;sCACjB,6LAAC,8IAAA,CAAA,oBAAiB;;8CAChB,6LAAC,8IAAA,CAAA,mBAAgB;8CAAC;;;;;;8CAClB,6LAAC,8IAAA,CAAA,yBAAsB;;wCAAC;wCACqB,OAAO;wCAAK;;;;;;;;;;;;;sCAI3D,6LAAC,8IAAA,CAAA,oBAAiB;;8CAChB,6LAAC,8IAAA,CAAA,oBAAiB;oCAAC,UAAU;8CAAc;;;;;;8CAC3C,6LAAC,8IAAA,CAAA,oBAAiB;oCAChB,SAAS;oCACT,UAAU;oCACV,WAAU;8CAET,6BACC;;0DACE,6LAAC;gDAAI,WAAU;;;;;;4CAA0F;;uDAI3G;;;;;;;;;;;;;;;;;;;;;;;0BAQV,6LAAC,8IAAA,CAAA,cAAW;gBACV,MAAM;gBACN,cAAc;0BAEd,cAAA,6LAAC,8IAAA,CAAA,qBAAkB;;sCACjB,6LAAC,8IAAA,CAAA,oBAAiB;;8CAChB,6LAAC,8IAAA,CAAA,mBAAgB;8CAAC;;;;;;8CAClB,6LAAC,8IAAA,CAAA,yBAAsB;8CAAC;;;;;;;;;;;;sCAM1B,6LAAC;4BAAI,WAAU;sCACZ,OAAO,SACJ,OAAO,CAAC,SAAW,OAAO,MAAM,KAAK,aAAa,IAAI,6BAA6B;6BACpF,IAAI,CAAC;gCACJ,MAAM,aAAa,aAAa,CAAC,OAAO,MAAM,CAAC;gCAC/C,MAAM,WAAW,YAAY,UAAU,WACnC,WAAW,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,GAAG,WAAW,KACpD;gCAEJ,qBACE,6LAAC;oCAEC,WAAU;oCACV,SAAS,IAAM,sBAAsB,OAAO,MAAM;;sDAElD,6LAAC,qIAAA,CAAA,SAAM;4CAAC,WAAU;;8DAChB,6LAAC,qIAAA,CAAA,cAAW;oDACV,KACE,YAAY,UAAU,qBAAqB;oDAE7C,WAAU;;;;;;8DAEZ,6LAAC,qIAAA,CAAA,iBAAc;oDAAC,WAAU;8DACvB;;;;;;;;;;;;sDAGL,6LAAC;;8DACC,6LAAC;oDAAE,WAAU;8DACV,YAAY,UAAU,YAAY;;;;;;8DAErC,6LAAC;oDAAE,WAAU;8DACV,OAAO,IAAI,KAAK,cACb,aACA;;;;;;;;;;;;;mCAtBH,OAAO,MAAM;;;;;4BA2BxB;;;;;;sCAEJ,6LAAC,8IAAA,CAAA,oBAAiB;sCAChB,cAAA,6LAAC,8IAAA,CAAA,oBAAiB;gCAAC,UAAU;0CAAc;;;;;;;;;;;;;;;;;;;;;;0BAMjD,6LAAC,8IAAA,CAAA,cAAW;gBACV,MAAM;gBACN,cAAc;0BAEd,cAAA,6LAAC,8IAAA,CAAA,qBAAkB;;sCACjB,6LAAC,8IAAA,CAAA,oBAAiB;;8CAChB,6LAAC,8IAAA,CAAA,mBAAgB;8CAAC;;;;;;8CAGlB,6LAAC,8IAAA,CAAA,yBAAsB;8CACpB,eAAe,aAAa,CAAC,YAAY,iBACxC;;4CAAE;4CACmD;0DACnD,6LAAC;0DACE,aAAa,CAAC,YAAY,EAAE,UAAU,YACrC;;;;;;4CACK;0DAET,6LAAC;;;;;4CAAK;;uDAKR;;;;;;;;;;;;sCAIN,6LAAC,8IAAA,CAAA,oBAAiB;;8CAChB,6LAAC,8IAAA,CAAA,oBAAiB;oCAChB,UAAU;oCACV,SAAS;wCACP,6BAA6B;wCAC7B,eAAe;oCACjB;8CACD;;;;;;8CAGD,6LAAC,8IAAA,CAAA,oBAAiB;oCAChB,SAAS;oCACT,UAAU;oCACV,WAAU;8CAET,6BACC;;0DACE,6LAAC;gDAAI,WAAU;;;;;;4CAA0F;;uDAI3G;;;;;;;;;;;;;;;;;;;;;;;YAQT,uBACC,6LAAC,qJAAA,CAAA,UAAmB;gBAClB,OAAO;gBACP,QAAQ;gBACR,cAAc;gBACd,QAAQ,IAAM,sBAAsB;gBACpC,WAAW,CAAC;oBACV,gCAAgC;oBAChC,MAAM,YAAY,6HAAA,CAAA,eAAY,CAAC,QAAQ;oBACvC,IAAI,UAAU,aAAa,EAAE,OAAO,aAAa,EAAE,EAAE;wBACnD,UAAU,gBAAgB,CAAC;oBAC7B;oBAEA,4EAA4E;oBAC5E,WAAW;wBACT,OAAO,QAAQ,CAAC,MAAM;oBACxB,GAAG;gBACL;;;;;;0BAKJ,6LAAC,8IAAA,CAAA,cAAW;gBAAC,MAAM;gBAAkB,cAAc;0BACjD,cAAA,6LAAC,8IAAA,CAAA,qBAAkB;;sCACjB,6LAAC,8IAAA,CAAA,oBAAiB;;8CAChB,6LAAC,8IAAA,CAAA,mBAAgB;8CAAC;;;;;;8CAClB,6LAAC,8IAAA,CAAA,yBAAsB;;wCAAC;wCACgB,OAAO;wCAAK;;;;;;;;;;;;;sCAKtD,6LAAC,8IAAA,CAAA,oBAAiB;;8CAChB,6LAAC,8IAAA,CAAA,oBAAiB;oCAAC,UAAU;8CAAc;;;;;;8CAC3C,6LAAC,8IAAA,CAAA,oBAAiB;oCAChB,SAAS;oCACT,UAAU;oCACV,WAAU;8CAET,6BACC;;0DACE,6LAAC;gDAAI,WAAU;;;;;;4CAA0F;;uDAI3G;;;;;;;;;;;;;;;;;;;;;;;;;AAQhB;GAvtBwB;;QAuBP,qIAAA,CAAA,YAAS;QAGJ,6HAAA,CAAA,eAAY;QACH,6HAAA,CAAA,eAAY;;;KA3BnB", "debugId": null}}]}