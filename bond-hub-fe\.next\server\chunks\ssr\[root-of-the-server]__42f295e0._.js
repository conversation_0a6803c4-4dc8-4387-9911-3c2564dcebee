module.exports = {

"[externals]/util [external] (util, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("util", () => require("util"));

module.exports = mod;
}}),
"[externals]/stream [external] (stream, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("stream", () => require("stream"));

module.exports = mod;
}}),
"[externals]/http [external] (http, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("http", () => require("http"));

module.exports = mod;
}}),
"[externals]/https [external] (https, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("https", () => require("https"));

module.exports = mod;
}}),
"[externals]/fs [external] (fs, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("fs", () => require("fs"));

module.exports = mod;
}}),
"[externals]/crypto [external] (crypto, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("crypto", () => require("crypto"));

module.exports = mod;
}}),
"[externals]/assert [external] (assert, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("assert", () => require("assert"));

module.exports = mod;
}}),
"[externals]/tty [external] (tty, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("tty", () => require("tty"));

module.exports = mod;
}}),
"[externals]/os [external] (os, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("os", () => require("os"));

module.exports = mod;
}}),
"[externals]/zlib [external] (zlib, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("zlib", () => require("zlib"));

module.exports = mod;
}}),
"[externals]/events [external] (events, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("events", () => require("events"));

module.exports = mod;
}}),
"[project]/src/utils/userCache.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "cacheUserData": (()=>cacheUserData),
    "cacheUserInfo": (()=>cacheUserInfo),
    "cacheUserInfoFromGroupMembers": (()=>cacheUserInfoFromGroupMembers),
    "clearUserCache": (()=>clearUserCache),
    "getCachedUserData": (()=>getCachedUserData),
    "getCachedUserInfo": (()=>getCachedUserInfo),
    "invalidateMultipleUserCache": (()=>invalidateMultipleUserCache),
    "invalidateUserCache": (()=>invalidateUserCache),
    "isCacheValid": (()=>isCacheValid),
    "isUserInfoCacheValid": (()=>isUserInfoCacheValid),
    "removeCachedUserData": (()=>removeCachedUserData)
});
// Create a cache for user data to avoid redundant API calls
const userDataCache = {};
const userInfoCache = {};
// Cache expiration time in milliseconds (10 minutes for better performance)
const CACHE_EXPIRATION = 10 * 60 * 1000;
function isCacheValid(userId) {
    if (!userDataCache[userId]) return false;
    const now = Date.now();
    const cacheTime = userDataCache[userId].timestamp;
    return now - cacheTime < CACHE_EXPIRATION;
}
function isUserInfoCacheValid(userId) {
    if (!userInfoCache[userId]) return false;
    const now = Date.now();
    const cacheTime = userInfoCache[userId].timestamp;
    return now - cacheTime < CACHE_EXPIRATION;
}
function getCachedUserData(userId, allowExpired = false) {
    // If we allow expired data, just check if it exists
    if (allowExpired && userDataCache[userId]) {
        console.log(`[USER_CACHE] Returning expired cache data for user ${userId}`);
        return userDataCache[userId].user;
    }
    // Otherwise check if it's valid
    if (isCacheValid(userId)) {
        console.log(`[USER_CACHE] Returning valid cache data for user ${userId}`);
        return userDataCache[userId].user;
    }
    return null;
}
function getCachedUserInfo(userId, allowExpired = true) {
    if (allowExpired && userInfoCache[userId]) {
        return userInfoCache[userId].userInfo;
    }
    if (isUserInfoCacheValid(userId)) {
        return userInfoCache[userId].userInfo;
    }
    return null;
}
function cacheUserData(userId, user) {
    userDataCache[userId] = {
        user,
        timestamp: Date.now()
    };
    // Also cache userInfo if available
    if (user.userInfo) {
        cacheUserInfo(userId, user.userInfo);
    }
}
function cacheUserInfo(userId, userInfo) {
    userInfoCache[userId] = {
        userInfo,
        timestamp: Date.now()
    };
}
function removeCachedUserData(userId) {
    if (userDataCache[userId]) {
        delete userDataCache[userId];
    }
    if (userInfoCache[userId]) {
        delete userInfoCache[userId];
    }
}
function cacheUserInfoFromGroupMembers(memberUsers) {
    memberUsers.forEach((member)=>{
        if (member.id && member.fullName) {
            cacheUserInfo(member.id, {
                id: member.id,
                fullName: member.fullName,
                profilePictureUrl: member.profilePictureUrl || null,
                statusMessage: "",
                blockStrangers: false,
                createdAt: new Date(),
                updatedAt: new Date(),
                userAuth: {
                    id: member.id
                }
            });
        }
    });
}
function clearUserCache() {
    Object.keys(userDataCache).forEach((key)=>{
        delete userDataCache[key];
    });
}
function invalidateUserCache(userId) {
    if (userInfoCache[userId]) {
        delete userInfoCache[userId];
    }
    if (userDataCache[userId]) {
        delete userDataCache[userId];
    }
}
function invalidateMultipleUserCache(userIds) {
    userIds.forEach((userId)=>{
        invalidateUserCache(userId);
    });
}
}}),
"[project]/src/actions/user.action.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
//"use server";
__turbopack_context__.s({
    "batchGetUserData": (()=>batchGetUserData),
    "deleteUser": (()=>deleteUser),
    "getAllUsers": (()=>getAllUsers),
    "getUserBasicInfo": (()=>getUserBasicInfo),
    "getUserDataById": (()=>getUserDataById),
    "searchUser": (()=>searchUser),
    "searchUserByPhoneNumber": (()=>searchUserByPhoneNumber),
    "updateCoverImage": (()=>updateCoverImage),
    "updateProfilePicture": (()=>updateProfilePicture),
    "updateUser": (()=>updateUser),
    "updateUserBasicInfo": (()=>updateUserBasicInfo)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$axios$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/axios.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$authStore$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/stores/authStore.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$userCache$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/utils/userCache.ts [app-rsc] (ecmascript)");
;
;
;
async function getAllUsers() {
    try {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$axios$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"].get("/users");
        const users = response.data;
        return {
            success: true,
            users
        };
    } catch (error) {
        console.error("Get all users failed:", error);
        return {
            success: false,
            error: error instanceof Error ? error.message : "Unknown error"
        };
    }
}
async function getUserDataById(id, token) {
    try {
        // Kiểm tra id hợp lệ
        if (!id || typeof id !== "string" || id.trim() === "") {
            console.error("[USER_ACTION] Invalid user ID provided:", id);
            return {
                success: false,
                error: "Invalid user ID"
            };
        }
        // Check if user data is in cache and still valid
        const cachedUser = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$userCache$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getCachedUserData"])(id);
        if (cachedUser) {
            console.log(`[USER_ACTION] Using cached user data for ID: ${id}`);
            return {
                success: true,
                user: cachedUser
            };
        }
        console.log(`[USER_ACTION] Fetching user data for ID: ${id}`);
        // Add timeout to the request to prevent hanging
        const controller = new AbortController();
        const timeoutId = setTimeout(()=>controller.abort(), 5000); // 5 second timeout
        try {
            // Set up request config
            const config = {
                signal: controller.signal
            };
            // If token is provided, use it in the request
            if (token) {
                config.headers = {
                    ...config.headers,
                    Authorization: `Bearer ${token}`
                };
                console.log(`[USER_ACTION] Using provided token for user data request`);
            }
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$axios$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"].get(`/users/${id}`, config);
            clearTimeout(timeoutId);
            const user = response.data;
            console.log(`[USER_ACTION] Successfully fetched user data for ID: ${id}`, user);
            // Store user data in cache
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$userCache$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["cacheUserData"])(id, user);
            return {
                success: true,
                user
            };
        } catch (requestError) {
            clearTimeout(timeoutId);
            throw requestError; // Re-throw to be caught by the outer catch
        }
    } catch (error) {
        console.error(`[USER_ACTION] Get user by ID failed for ID ${id}:`, error);
        // Check if this is a timeout or abort error
        if (error && typeof error === "object" && "name" in error && (error.name === "AbortError" || error.name === "TimeoutError")) {
            console.warn(`[USER_ACTION] Request timed out for user ID: ${id}`);
            // Try to get from cache even if expired as fallback
            const expiredCachedUser = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$userCache$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getCachedUserData"])(id, true);
            if (expiredCachedUser) {
                console.log(`[USER_ACTION] Using expired cached user data for ID: ${id} after timeout`);
                return {
                    success: true,
                    user: expiredCachedUser
                };
            }
        }
        // Tạo user giả nếu không tìm thấy (chỉ cho mục đích hiển thị UI)
        const axiosError = error;
        if (axiosError.response && axiosError.response.status === 404) {
            console.log(`[USER_ACTION] Creating placeholder user for ID ${id}`);
            const placeholderUser = {
                id: id,
                email: null,
                phoneNumber: null,
                passwordHash: "",
                createdAt: new Date(),
                updatedAt: new Date(),
                userInfo: {
                    id: id,
                    fullName: `Người dùng ${id.slice(-4)}`,
                    profilePictureUrl: null,
                    statusMessage: "",
                    blockStrangers: false,
                    createdAt: new Date(),
                    updatedAt: new Date(),
                    userAuth: null
                },
                refreshTokens: [],
                qrCodes: [],
                posts: [],
                stories: [],
                groupMembers: [],
                cloudFiles: [],
                pinnedItems: [],
                sentFriends: [],
                receivedFriends: [],
                contacts: [],
                contactOf: [],
                settings: [],
                postReactions: [],
                hiddenPosts: [],
                addedBy: [],
                notifications: [],
                sentMessages: [],
                receivedMessages: [],
                comments: []
            };
            // Cache the placeholder user too
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$userCache$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["cacheUserData"])(id, placeholderUser);
            return {
                success: true,
                user: placeholderUser
            };
        }
        // For any other error, create a generic placeholder user
        console.log(`[USER_ACTION] Creating generic placeholder user for ID ${id} due to error`);
        const genericUser = {
            id: id,
            email: null,
            phoneNumber: null,
            passwordHash: "",
            createdAt: new Date(),
            updatedAt: new Date(),
            userInfo: {
                id: id,
                fullName: `Người dùng ${id.slice(-4)}`,
                profilePictureUrl: null,
                statusMessage: "",
                blockStrangers: false,
                createdAt: new Date(),
                updatedAt: new Date(),
                userAuth: null
            },
            refreshTokens: [],
            qrCodes: [],
            posts: [],
            stories: [],
            groupMembers: [],
            cloudFiles: [],
            pinnedItems: [],
            sentFriends: [],
            receivedFriends: [],
            contacts: [],
            contactOf: [],
            settings: [],
            postReactions: [],
            hiddenPosts: [],
            addedBy: [],
            notifications: [],
            sentMessages: [],
            receivedMessages: [],
            comments: []
        };
        // Cache the generic user too
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$userCache$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["cacheUserData"])(id, genericUser);
        return {
            success: true,
            user: genericUser
        };
    }
}
async function batchGetUserData(userIds) {
    // Filter out duplicate IDs
    const uniqueIds = [
        ...new Set(userIds)
    ];
    // Check which users are already in cache
    const cachedUsers = [];
    const idsToFetch = [];
    uniqueIds.forEach((id)=>{
        const cachedUser = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$userCache$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getCachedUserData"])(id);
        if (cachedUser) {
            cachedUsers.push(cachedUser);
        } else {
            idsToFetch.push(id);
        }
    });
    // If all users are in cache, return immediately
    if (idsToFetch.length === 0) {
        console.log(`All ${uniqueIds.length} users found in cache`);
        return {
            success: true,
            users: cachedUsers
        };
    }
    // Otherwise, fetch the remaining users
    try {
        console.log(`Batch fetching ${idsToFetch.length} users`);
        // Fetch each user individually (could be optimized with a batch API endpoint)
        const fetchPromises = idsToFetch.map((id)=>getUserDataById(id));
        const results = await Promise.all(fetchPromises);
        // Combine cached and newly fetched users
        const fetchedUsers = results.filter((result)=>result.success && result.user).map((result)=>result.user);
        const allUsers = [
            ...cachedUsers,
            ...fetchedUsers
        ];
        return {
            success: true,
            users: allUsers
        };
    } catch (error) {
        console.error("Batch get users failed:", error);
        return {
            success: false,
            error: error instanceof Error ? error.message : "Unknown error",
            users: cachedUsers
        };
    }
}
async function getUserBasicInfo(id) {
    try {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$axios$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"].get(`/users/${id}/basic-info`);
        const userInfo = response.data; // Type tùy thuộc vào định nghĩa của bạn
        return {
            success: true,
            userInfo
        };
    } catch (error) {
        console.error("Get user basic info failed:", error);
        return {
            success: false,
            error: error instanceof Error ? error.message : "Unknown error"
        };
    }
}
async function updateUser(id, userData) {
    try {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$axios$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"].patch(`/users/${id}`, userData);
        const updatedUser = response.data;
        // Cập nhật lại thông tin trong store nếu user hiện tại đang được chỉnh sửa
        const currentUser = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$authStore$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["useAuthStore"].getState().user;
        if (currentUser && currentUser.id === id) {
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$authStore$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["useAuthStore"].getState().updateUser(updatedUser);
        }
        // Update the cache with the new user data
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$userCache$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["cacheUserData"])(id, updatedUser);
        return {
            success: true,
            user: updatedUser
        };
    } catch (error) {
        console.error("Update user failed:", error);
        return {
            success: false,
            error: error instanceof Error ? error.message : "Unknown error"
        };
    }
}
async function deleteUser(id) {
    try {
        await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$axios$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"].delete(`/users/${id}`);
        // Nếu user hiện tại bị xóa, thực hiện logout
        const currentUser = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$authStore$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["useAuthStore"].getState().user;
        if (currentUser && currentUser.id === id) {
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$authStore$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["useAuthStore"].getState().logout();
        }
        // Remove from cache if exists
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$userCache$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["removeCachedUserData"])(id);
        return {
            success: true
        };
    } catch (error) {
        console.error("Delete user failed:", error);
        return {
            success: false,
            error: error instanceof Error ? error.message : "Unknown error"
        };
    }
}
async function updateProfilePicture(file) {
    try {
        // Make sure file is not undefined
        if (!file) {
            throw new Error("No file selected");
        }
        const formData = new FormData();
        formData.append("file", file);
        // Change the content type to multipart/form-data
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$axios$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"].put("/auth/update-profile-picture", formData, {
            headers: {
                "Content-Type": "multipart/form-data"
            }
        });
        const { message, url } = response.data;
        // Thêm timestamp vào URL để tránh cache
        const urlWithTimestamp = `${url}?t=${new Date().getTime()}`;
        // Lấy dữ liệu user hiện tại từ store
        const currentUser = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$authStore$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["useAuthStore"].getState().user;
        if (currentUser && currentUser.id) {
            // Cập nhật ngay lập tức trong store với URL mới có timestamp
            if (currentUser.userInfo) {
                const updatedUser = {
                    ...currentUser,
                    userInfo: {
                        ...currentUser.userInfo,
                        profilePictureUrl: urlWithTimestamp
                    }
                };
                // Cập nhật store
                __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$authStore$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["useAuthStore"].getState().updateUser(updatedUser);
                // Cập nhật cache
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$userCache$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["cacheUserData"])(currentUser.id, updatedUser);
                console.log("Profile picture immediately updated in store with timestamp");
            }
            // Sau đó, thử lấy dữ liệu đầy đủ từ server (không chờ đợi)
            getUserDataById(currentUser.id).then((userResponse)=>{
                if (userResponse.success && userResponse.user) {
                    // Đảm bảo URL hình ảnh mới có timestamp
                    const serverUser = userResponse.user;
                    if (serverUser.userInfo && serverUser.userInfo.profilePictureUrl) {
                        serverUser.userInfo.profilePictureUrl = `${serverUser.userInfo.profilePictureUrl}?t=${new Date().getTime()}`;
                    }
                    // Cập nhật store với dữ liệu đầy đủ từ server
                    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$authStore$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["useAuthStore"].getState().updateUser(serverUser);
                    // Cập nhật cache
                    (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$userCache$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["cacheUserData"])(currentUser.id, serverUser);
                    console.log("User data updated from database after profile picture change");
                }
            }).catch((fetchError)=>{
                console.error("Error fetching updated user data:", fetchError);
            // Không cần làm gì vì đã cập nhật store trước đó
            });
        }
        // Trả về URL có timestamp để client có thể sử dụng ngay
        return {
            success: true,
            message,
            url: urlWithTimestamp
        };
    } catch (error) {
        console.error("Update profile picture failed:", error);
        return {
            success: false,
            error: error instanceof Error ? error.message : "Unknown error"
        };
    }
}
async function updateCoverImage(file) {
    try {
        // Make sure file is not undefined
        if (!file) {
            throw new Error("No file selected");
        }
        const formData = new FormData();
        formData.append("file", file);
        // Change the content type to multipart/form-data
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$axios$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"].put("/auth/update-cover-image", formData, {
            headers: {
                "Content-Type": "multipart/form-data"
            }
        });
        const { message, url } = response.data;
        // Thêm timestamp vào URL để tránh cache
        const urlWithTimestamp = `${url}?t=${new Date().getTime()}`;
        // Lấy dữ liệu user hiện tại từ store
        const currentUser = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$authStore$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["useAuthStore"].getState().user;
        if (currentUser && currentUser.id) {
            // Cập nhật ngay lập tức trong store với URL mới có timestamp
            if (currentUser.userInfo) {
                const updatedUser = {
                    ...currentUser,
                    userInfo: {
                        ...currentUser.userInfo,
                        coverImgUrl: urlWithTimestamp
                    }
                };
                // Cập nhật store
                __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$authStore$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["useAuthStore"].getState().updateUser(updatedUser);
                // Cập nhật cache
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$userCache$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["cacheUserData"])(currentUser.id, updatedUser);
                console.log("Cover image immediately updated in store with timestamp");
            }
            // Sau đó, thử lấy dữ liệu đầy đủ từ server (không chờ đợi)
            getUserDataById(currentUser.id).then((userResponse)=>{
                if (userResponse.success && userResponse.user) {
                    // Đảm bảo URL hình ảnh mới có timestamp
                    const serverUser = userResponse.user;
                    if (serverUser.userInfo && serverUser.userInfo.coverImgUrl) {
                        serverUser.userInfo.coverImgUrl = `${serverUser.userInfo.coverImgUrl}?t=${new Date().getTime()}`;
                    }
                    // Cập nhật store với dữ liệu đầy đủ từ server
                    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$authStore$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["useAuthStore"].getState().updateUser(serverUser);
                    // Cập nhật cache
                    (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$userCache$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["cacheUserData"])(currentUser.id, serverUser);
                    console.log("User data updated from database after cover image change");
                }
            }).catch((fetchError)=>{
                console.error("Error fetching updated user data:", fetchError);
            // Không cần làm gì vì đã cập nhật store trước đó
            });
        }
        // Trả về URL có timestamp để client có thể sử dụng ngay
        return {
            success: true,
            message,
            url: urlWithTimestamp
        };
    } catch (error) {
        console.error("Update cover image failed:", error);
        return {
            success: false,
            error: error instanceof Error ? error.message : "Unknown error"
        };
    }
}
async function updateUserBasicInfo(userData) {
    try {
        const currentUser = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$authStore$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["useAuthStore"].getState().user;
        if (!currentUser || !currentUser.id) {
            throw new Error("User not authenticated");
        }
        // Cập nhật ngay lập tức trong store trước khi gọi API
        // Điều này giúp UI cập nhật ngay lập tức
        if (currentUser.userInfo) {
            // Xử lý gender để đảm bảo đúng kiểu Gender
            let updatedGender = currentUser.userInfo.gender;
            if (userData.gender) {
                // Chuyển đổi string thành Gender enum
                if (userData.gender === "MALE" || userData.gender === "FEMALE" || userData.gender === "OTHER") {
                    updatedGender = userData.gender; // Ép kiểu an toàn vì đã kiểm tra giá trị
                }
            }
            const immediateUpdatedUserInfo = {
                ...currentUser.userInfo,
                fullName: userData.fullName || currentUser.userInfo.fullName,
                gender: updatedGender,
                dateOfBirth: userData.dateOfBirth || currentUser.userInfo.dateOfBirth,
                bio: userData.bio || currentUser.userInfo.bio
            };
            const immediateUserToUpdate = {
                ...currentUser,
                userInfo: immediateUpdatedUserInfo
            };
            // Cập nhật store ngay lập tức
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$authStore$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["useAuthStore"].getState().updateUser(immediateUserToUpdate);
            // Cập nhật cache
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$userCache$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["cacheUserData"])(currentUser.id, immediateUserToUpdate);
            console.log("Basic info immediately updated in store");
        }
        // Gọi API để cập nhật thông tin trên server
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$axios$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"].put(`/auth/update-basic-info`, userData);
        const updatedUser = response.data;
        // Sau khi API thành công, lấy dữ liệu mới từ server (không chờ đợi)
        // Sử dụng Promise để không chặn luồng chính
        getUserDataById(currentUser.id).then((userResponse)=>{
            if (userResponse.success && userResponse.user) {
                // Cập nhật toàn bộ dữ liệu user trong store
                __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$authStore$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["useAuthStore"].getState().updateUser(userResponse.user);
                // Cập nhật cache
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$userCache$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["cacheUserData"])(currentUser.id, userResponse.user);
                console.log("User data updated from database after basic info change");
            }
        }).catch((fetchError)=>{
            console.error("Error fetching updated user data:", fetchError);
        // Không cần làm gì vì đã cập nhật store trước đó
        });
        // Trả về kết quả thành công ngay lập tức
        return {
            success: true,
            user: updatedUser,
            // Thêm thông tin đã cập nhật để UI có thể sử dụng ngay
            updatedInfo: {
                fullName: userData.fullName,
                gender: userData.gender,
                dateOfBirth: userData.dateOfBirth,
                bio: userData.bio
            }
        };
    } catch (error) {
        console.error("Update user basic info failed:", error);
        return {
            success: false,
            error: error instanceof Error ? error.message : "Unknown error"
        };
    }
}
async function searchUser(searchValue) {
    try {
        // Kiểm tra xem searchValue có phải là email không
        const isEmail = /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(searchValue);
        // Tạo payload phù hợp dựa trên loại tìm kiếm
        const payload = isEmail ? {
            email: searchValue
        } : {
            phoneNumber: searchValue
        };
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$axios$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"].post("/users/search", payload);
        // Cache the found user
        if (response.data && response.data.id) {
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$userCache$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["cacheUserData"])(response.data.id, response.data);
        }
        return {
            success: true,
            user: response.data
        };
    } catch (error) {
        // Kiểm tra nếu là lỗi 404 (không tìm thấy)
        const axiosError = error;
        if (axiosError.response && axiosError.response.status === 404) {
            // Kiểm tra lại isEmail vì nó đã ra khỏi phạm vi của try
            const isEmailSearch = /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(searchValue);
            console.log(`Không tìm thấy người dùng với ${isEmailSearch ? "email" : "số điện thoại"}: ${searchValue}`);
            // Trả về success: false nhưng không có thông báo lỗi để UI hiển thị "Không tìm thấy"
            return {
                success: false
            };
        }
        console.error("Search user failed:", error);
        return {
            success: false,
            error: error instanceof Error ? error.message : "Unknown error"
        };
    }
}
async function searchUserByPhoneNumber(phoneNumber) {
    return searchUser(phoneNumber);
}
}}),
"[project]/src/stores/authStore.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "useAuthStore": (()=>useAuthStore)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$react$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zustand/esm/react.mjs [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$middleware$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zustand/esm/middleware.mjs [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$actions$2f$auth$2e$action$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/actions/auth.action.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$actions$2f$user$2e$action$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/actions/user.action.ts [app-rsc] (ecmascript)");
;
;
;
;
const storage = {
    getItem: (name)=>{
        if ("TURBOPACK compile-time truthy", 1) return null;
        "TURBOPACK unreachable";
    },
    setItem: (name, value)=>{
        if ("TURBOPACK compile-time truthy", 1) return;
        "TURBOPACK unreachable";
    },
    removeItem: (name)=>{
        if ("TURBOPACK compile-time truthy", 1) return;
        "TURBOPACK unreachable";
    }
};
const useAuthStore = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$react$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["create"])()((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$middleware$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["persist"])((set)=>({
        user: null,
        accessToken: null,
        refreshToken: null,
        isAuthenticated: false,
        isLoading: false,
        // Removed socket and socketId to avoid storing them in localStorage
        deviceId: null,
        _hasHydrated: false,
        setHasHydrated: (state)=>set({
                _hasHydrated: state
            }),
        setAuth: (user, accessToken)=>set({
                accessToken,
                user: user,
                isAuthenticated: !!accessToken,
                isLoading: false
            }),
        login: async (identifier, password, deviceName, deviceType)=>{
            try {
                set({
                    isLoading: true
                });
                const result = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$actions$2f$auth$2e$action$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["login"])(identifier, password, deviceName, deviceType);
                if (!result.success) return false;
                // First set the tokens and basic user data
                console.log("Login successful, setting tokens. accessToken:", result.accessToken ? "Token exists" : "No token");
                // Lưu refreshToken vào state nhưng không lưu vào localStorage
                // (partialize sẽ loại bỏ refreshToken khi lưu vào localStorage)
                console.log("Login successful, saving tokens and deviceId to store", {
                    hasAccessToken: !!result.accessToken,
                    hasRefreshToken: !!result.refreshToken,
                    hasDeviceId: !!result.deviceId
                });
                set({
                    accessToken: result.accessToken,
                    refreshToken: result.refreshToken,
                    deviceId: result.deviceId,
                    isAuthenticated: true,
                    isLoading: false
                });
                // Kiểm tra xem các giá trị đã được lưu đúng chưa
                const state = useAuthStore.getState();
                console.log("After login, state check:", {
                    hasAccessToken: !!state.accessToken,
                    hasRefreshToken: !!state.refreshToken,
                    hasDeviceId: !!state.deviceId,
                    isAuthenticated: state.isAuthenticated
                });
                // Socket sẽ được khởi tạo tự động bởi SocketProvider
                // Then try to get additional user data
                try {
                    const userData = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$actions$2f$user$2e$action$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getUserDataById"])(result.user.userId);
                    if (userData.success && userData.user) {
                        set({
                            user: userData.user,
                            isLoading: false
                        });
                    }
                } catch  {
                    // Set basic user data if additional data fetch fails
                    set({
                        user: result.user,
                        isLoading: false
                    });
                }
                return true;
            } catch  {
                return false;
            } finally{
                set({
                    isLoading: false
                });
            }
        },
        setLoading: (loading)=>set({
                isLoading: loading
            }),
        updateUser: (updatedUser)=>set((state)=>{
                if (!state.user) return {
                    user: null
                };
                // Merge userInfo properly if it exists in updatedUser
                const mergedUserInfo = updatedUser.userInfo ? {
                    ...state.user.userInfo,
                    ...updatedUser.userInfo
                } : state.user.userInfo;
                return {
                    user: {
                        ...state.user,
                        ...updatedUser,
                        userInfo: mergedUserInfo
                    }
                };
            }),
        logout: async ()=>{
            try {
                // Socket sẽ được ngắt kết nối tự động khi accessToken thay đổi
                await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$actions$2f$auth$2e$action$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["logout"])();
            } catch  {
            // Ignore errors from the API
            } finally{
                // Always reset store state to ensure UI updates
                // Xóa cả refreshToken khỏi store
                set({
                    user: null,
                    accessToken: null,
                    refreshToken: null,
                    deviceId: null,
                    isAuthenticated: false,
                    isLoading: false
                });
            }
            return true; // Always return true to ensure UI updates
        },
        setTokens: (accessToken, refreshToken)=>{
            console.log("setTokens called with accessToken:", accessToken ? `${accessToken.substring(0, 10)}...` : "No token", "and refreshToken:", refreshToken ? `${refreshToken.substring(0, 10)}...` : "No token");
            if (!accessToken) {
                console.error("Attempted to set tokens with null/empty accessToken");
                return;
            }
            if (!refreshToken) {
                console.error("Attempted to set tokens with null/empty refreshToken");
                return;
            }
            // Lưu refreshToken vào state nhưng không lưu vào localStorage
            // (được xử lý bởi partialize)
            set({
                accessToken,
                refreshToken,
                isAuthenticated: true
            });
            // Kiểm tra xem tokens đã được lưu đúng chưa
            const state = useAuthStore.getState();
            console.log("After setTokens, state check:", {
                hasAccessToken: !!state.accessToken,
                hasRefreshToken: !!state.refreshToken,
                isAuthenticated: state.isAuthenticated,
                accessTokenPrefix: state.accessToken ? state.accessToken.substring(0, 10) + "..." : "none",
                refreshTokenPrefix: state.refreshToken ? state.refreshToken.substring(0, 10) + "..." : "none"
            });
        // Socket sẽ được cập nhật tự động bởi SocketProvider khi accessToken thay đổi
        }
    }), {
    name: "auth-storage",
    storage: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$middleware$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createJSONStorage"])(()=>storage),
    partialize: (state)=>{
        // Ghi log trạng thái trước khi lưu vào localStorage
        console.log("Persisting auth state to localStorage:", {
            hasAccessToken: !!state.accessToken,
            hasRefreshToken: !!state.refreshToken,
            hasDeviceId: !!state.deviceId,
            isAuthenticated: state.isAuthenticated
        });
        // Lưu refreshToken vào localStorage nhưng được mã hóa đơn giản
        // Lưu ý: Đây không phải là mã hóa an toàn, chỉ là giải pháp tạm thời
        // Trong môi trường sản xuất, nên sử dụng cookie httpOnly hoặc các giải pháp bảo mật hơn
        let encodedRefreshToken = null;
        if (state.refreshToken) {
            try {
                // Mã hóa đơn giản bằng cách đảo ngược và base64
                encodedRefreshToken = btoa(state.refreshToken.split("").reverse().join(""));
            } catch (error) {
                console.error("Error encoding refresh token:", error);
            }
        }
        return {
            accessToken: state.accessToken,
            refreshToken: encodedRefreshToken,
            isAuthenticated: state.isAuthenticated,
            user: state.user,
            deviceId: state.deviceId
        };
    },
    onRehydrateStorage: ()=>(state)=>{
            if (state) {
                // Giải mã refreshToken nếu có
                if (state.refreshToken) {
                    try {
                        // Giải mã bằng cách đảo ngược quá trình mã hóa
                        const decodedRefreshToken = atob(state.refreshToken).split("").reverse().join("");
                        state.refreshToken = decodedRefreshToken;
                        console.log("Successfully decoded refresh token from storage", {
                            hasRefreshToken: true,
                            refreshTokenPrefix: decodedRefreshToken.substring(0, 10) + "..."
                        });
                    } catch (error) {
                        console.error("Error decoding refresh token:", error);
                        // Nếu không thể giải mã, xóa refreshToken để tránh lỗi
                        state.refreshToken = null;
                    }
                } else {
                    console.log("No refresh token found in storage");
                }
                state.setHasHydrated(true);
                // Kiểm tra tính hợp lệ của trạng thái sau khi khôi phục
                console.log("Auth state after rehydration:", {
                    hasAccessToken: !!state.accessToken,
                    hasRefreshToken: !!state.refreshToken,
                    hasDeviceId: !!state.deviceId,
                    isAuthenticated: state.isAuthenticated
                });
            }
        }
}));
}}),
"[project]/src/lib/axios.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "createAxiosInstance": (()=>createAxiosInstance),
    "default": (()=>__TURBOPACK__default__export__),
    "refreshTokenAxios": (()=>refreshTokenAxios)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/axios/lib/axios.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$authStore$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/stores/authStore.ts [app-rsc] (ecmascript)");
;
;
const NEXT_PUBLIC_BACKEND_URL = ("TURBOPACK compile-time value", "http://bondhub.cloud:3000/api/v1");
// Token refresh state management
let isRefreshing = false;
let refreshSubscribers = [];
// Create a base axios instance with common configuration
const createBaseAxiosInstance = ()=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"].create({
        baseURL: NEXT_PUBLIC_BACKEND_URL,
        headers: {
            "Content-Type": "application/json"
        },
        timeout: 15000
    });
};
const refreshTokenAxios = createBaseAxiosInstance();
// Add network error handling for the refresh token axios instance
refreshTokenAxios.interceptors.response.use((response)=>response, (error)=>{
    if (error.code === "ECONNABORTED") {
        return Promise.reject(new Error("Request timeout during token refresh"));
    }
    if (!error.response) {
        return Promise.reject(new Error("Network error during token refresh"));
    }
    return Promise.reject(error);
});
const createAxiosInstance = (token)=>{
    const instance = createBaseAxiosInstance();
    // Add token to headers if provided
    if (token && token.trim() !== "") {
        instance.defaults.headers.common["Authorization"] = `Bearer ${token}`;
    }
    // Add network error handling
    instance.interceptors.response.use((response)=>response, (error)=>{
        if (error.code === "ECONNABORTED") {
            return Promise.reject(new Error("Request timeout. Please try again."));
        }
        if (!error.response) {
            return Promise.reject(new Error("Network error. Please check your connection."));
        }
        return Promise.reject(error);
    });
    return instance;
};
// Main axios instance with full authentication handling
const axiosInstance = createBaseAxiosInstance();
// Add token to all requests
axiosInstance.interceptors.request.use((config)=>{
    try {
        const accessToken = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$authStore$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["useAuthStore"].getState().accessToken;
        if (accessToken) {
            config.headers = config.headers || {};
            config.headers.Authorization = `Bearer ${accessToken}`;
        }
        return config;
    } catch (error) {
        console.error("Error in axios request interceptor:", error);
        return config;
    }
}, (error)=>Promise.reject(error));
// Function to add a request to the refresh token subscribers queue
const subscribeTokenRefresh = (callback)=>{
    refreshSubscribers.push(callback);
};
// Function to notify all subscribers that the token has been refreshed
const onTokenRefreshed = (token)=>{
    refreshSubscribers.forEach((callback)=>callback(token));
    refreshSubscribers = [];
};
// Function to refresh the authentication token
const refreshAuthToken = async ()=>{
    try {
        const authState = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$authStore$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["useAuthStore"].getState();
        const refreshToken = authState.refreshToken;
        const deviceId = authState.deviceId;
        console.log("Starting token refresh process");
        console.log("Current refresh token:", refreshToken ? `${refreshToken.substring(0, 10)}...` : "none");
        console.log("Current device ID:", deviceId || "none");
        // Kiểm tra kỹ lưỡng refreshToken và deviceId
        if (!refreshToken || refreshToken.trim() === "") {
            console.error("Cannot refresh token: No refresh token available");
            throw new Error("No refresh token available");
        }
        if (!deviceId || deviceId.trim() === "") {
            console.error("Cannot refresh token: No device ID available");
            throw new Error("No device ID available");
        }
        // Thêm timeout để tránh chờ quá lâu
        const controller = new AbortController();
        const timeoutId = setTimeout(()=>controller.abort(), 10000); // 10 seconds timeout
        try {
            const response = await refreshTokenAxios.post("/auth/refresh", {
                refreshToken,
                deviceId
            }, {
                signal: controller.signal
            });
            clearTimeout(timeoutId);
            console.log("Refresh token response:", response.data);
            if (!response.data || !response.data.accessToken) {
                console.error("Invalid response from refresh token API");
                throw new Error("Invalid response from refresh token API");
            }
            const { accessToken } = response.data;
            // Keep the same refreshToken since backend doesn't return a new one
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$authStore$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["useAuthStore"].getState().setTokens(accessToken, refreshToken);
            console.log(`Token refresh completed successfully. New token: ${accessToken.substring(0, 10)}...`);
            return accessToken;
        } catch (requestError) {
            clearTimeout(timeoutId);
            throw requestError;
        }
    } catch (error) {
        console.error("Error during token refresh:", error instanceof Error ? error.message : "Unknown error");
        // Xử lý các loại lỗi khác nhau
        if (__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"].isAxiosError(error)) {
            if (error.response?.status === 403) {
                // Forbidden - e.g., refresh token blacklisted
                console.log("Logging out immediately due to forbidden error during token refresh");
                await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$authStore$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["useAuthStore"].getState().logout();
            } else if (error.response?.status === 401) {
                // Unauthorized
                console.log("Logging out due to unauthorized error during token refresh");
                await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$authStore$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["useAuthStore"].getState().logout();
            } else if (error.code === "ERR_CANCELED") {
                console.log("Token refresh request was aborted due to timeout");
            }
        } else if (error instanceof Error && (error.message.includes("refresh token") || error.message.includes("device ID"))) {
            // Lỗi liên quan đến refresh token hoặc device ID
            console.log("Logging out due to missing refresh token or device ID");
            await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$authStore$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["useAuthStore"].getState().logout();
        }
        throw error;
    } finally{
        console.log("Token refresh process completed, resetting isRefreshing flag");
        isRefreshing = false;
    }
};
// Handle response errors, including 401 Unauthorized with token refresh
axiosInstance.interceptors.response.use((response)=>response, async (error)=>{
    // Handle network errors
    if (error.code === "ECONNABORTED") {
        return Promise.reject(new Error("Request timeout. Please try again."));
    }
    if (!error.response) {
        return Promise.reject(new Error("Network error. Please check your connection."));
    }
    const originalRequest = error.config;
    // Handle 401 Unauthorized errors by refreshing the token
    if (error.response?.status === 401 && !originalRequest._retry) {
        console.log(`Received 401 error for request to ${originalRequest.url}, attempting to refresh token...`);
        // Mark this request as retried to prevent infinite loops
        originalRequest._retry = true;
        // Check if we have the necessary data to refresh the token
        const authState = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$authStore$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["useAuthStore"].getState();
        const refreshToken = authState.refreshToken;
        const deviceId = authState.deviceId;
        console.log("Checking refresh token data:", {
            hasRefreshToken: !!refreshToken,
            refreshTokenPrefix: refreshToken ? refreshToken.substring(0, 10) + "..." : "none",
            hasDeviceId: !!deviceId,
            deviceId: deviceId || "none"
        });
        if (!refreshToken || !deviceId) {
            console.error("Cannot refresh token: Missing refresh token or device ID");
            // Automatically logout the user when refresh token is missing
            console.log("Logging out due to missing refresh token or device ID");
            setTimeout(()=>{
                __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$authStore$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["useAuthStore"].getState().logout().catch((e)=>console.error("Error during auto logout:", e));
            }, 500);
            return Promise.reject(new Error("Session expired. Please login again."));
        }
        // If a token refresh is already in progress, queue this request
        if (isRefreshing) {
            console.log(`Token refresh already in progress, queuing request to ${originalRequest.url}`);
            return new Promise((resolve, reject)=>{
                subscribeTokenRefresh((token)=>{
                    console.log(`Processing queued request to ${originalRequest.url} with refreshed token`);
                    if (originalRequest.headers) {
                        originalRequest.headers["Authorization"] = `Bearer ${token}`;
                    } else {
                        originalRequest.headers = {
                            Authorization: `Bearer ${token}`
                        };
                    }
                    resolve(axiosInstance(originalRequest));
                });
                // Add timeout to avoid waiting indefinitely
                setTimeout(()=>{
                    reject(new Error("Token refresh timeout"));
                }, 15000); // 15 seconds timeout
            });
        }
        // Start a new token refresh process
        console.log("Starting new token refresh process");
        isRefreshing = true;
        try {
            const newToken = await refreshAuthToken();
            // Notify all subscribers that the token has been refreshed
            console.log(`Notifying ${refreshSubscribers.length} queued requests about token refresh`);
            onTokenRefreshed(newToken);
            // Update the authorization header and retry the original request
            console.log(`Retrying original request to ${originalRequest.url} with new token`);
            if (originalRequest.headers) {
                originalRequest.headers["Authorization"] = `Bearer ${newToken}`;
            } else {
                originalRequest.headers = {
                    Authorization: `Bearer ${newToken}`
                };
            }
            return axiosInstance(originalRequest);
        } catch (error) {
            // If token refresh fails, notify all subscribers to prevent hanging requests
            console.error("Token refresh failed:", error instanceof Error ? error.message : "Unknown error");
            console.log(`Notifying ${refreshSubscribers.length} queued requests about token refresh failure`);
            onTokenRefreshed(""); // Empty token will cause subscribers to fail properly
            // Only logout if the error is a 401 or network error
            if (__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"].isAxiosError(error) && (error.response?.status === 401 || !error.response)) {
                console.log("Logging out due to authentication error during token refresh");
                // We'll logout after a short delay to allow current operations to complete
                setTimeout(()=>{
                    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$authStore$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["useAuthStore"].getState().logout().catch((e)=>console.error("Error during delayed logout:", e));
                }, 500);
            }
            return Promise.reject(new Error("Session expired. Please login again."));
        }
    }
    // For all other errors, just reject with the original error
    return Promise.reject(error);
});
const __TURBOPACK__default__export__ = axiosInstance;
}}),
"[project]/src/types/base.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// Interfaces for specific Json fields
__turbopack_context__.s({
    "DeviceType": (()=>DeviceType),
    "FriendStatus": (()=>FriendStatus),
    "Gender": (()=>Gender),
    "GroupRole": (()=>GroupRole),
    "MessageType": (()=>MessageType),
    "QrCodeStatus": (()=>QrCodeStatus),
    "ReactionType": (()=>ReactionType)
});
// Enums
var Gender = /*#__PURE__*/ function(Gender) {
    Gender["MALE"] = "MALE";
    Gender["FEMALE"] = "FEMALE";
    Gender["OTHER"] = "OTHER";
    return Gender;
}(Gender || {});
var GroupRole = /*#__PURE__*/ function(GroupRole) {
    GroupRole["LEADER"] = "LEADER";
    GroupRole["CO_LEADER"] = "CO_LEADER";
    GroupRole["MEMBER"] = "MEMBER";
    return GroupRole;
}(GroupRole || {});
var MessageType = /*#__PURE__*/ function(MessageType) {
    MessageType["GROUP"] = "GROUP";
    MessageType["USER"] = "USER";
    return MessageType;
}(MessageType || {});
var DeviceType = /*#__PURE__*/ function(DeviceType) {
    DeviceType["MOBILE"] = "MOBILE";
    DeviceType["TABLET"] = "TABLET";
    DeviceType["DESKTOP"] = "DESKTOP";
    DeviceType["OTHER"] = "OTHER";
    return DeviceType;
}(DeviceType || {});
var FriendStatus = /*#__PURE__*/ function(FriendStatus) {
    FriendStatus["PENDING"] = "PENDING";
    FriendStatus["ACCEPTED"] = "ACCEPTED";
    FriendStatus["DECLINED"] = "DECLINED";
    FriendStatus["BLOCKED"] = "BLOCKED";
    return FriendStatus;
}(FriendStatus || {});
var QrCodeStatus = /*#__PURE__*/ function(QrCodeStatus) {
    QrCodeStatus["PENDING"] = "PENDING";
    QrCodeStatus["SCANNED"] = "SCANNED";
    QrCodeStatus["CONFIRMED"] = "CONFIRMED";
    QrCodeStatus["EXPIRED"] = "EXPIRED";
    QrCodeStatus["CANCELLED"] = "CANCELLED";
    return QrCodeStatus;
}(QrCodeStatus || {});
var ReactionType = /*#__PURE__*/ function(ReactionType) {
    ReactionType["LIKE"] = "LIKE";
    ReactionType["LOVE"] = "LOVE";
    ReactionType["HAHA"] = "HAHA";
    ReactionType["WOW"] = "WOW";
    ReactionType["SAD"] = "SAD";
    ReactionType["ANGRY"] = "ANGRY";
    return ReactionType;
}(ReactionType || {});
;
}}),
"[project]/src/utils/helpers.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "formatDate": (()=>formatDate),
    "formatPhoneNumber": (()=>formatPhoneNumber),
    "getDeviceInfo": (()=>getDeviceInfo),
    "isEmail": (()=>isEmail),
    "isPhoneNumber": (()=>isPhoneNumber),
    "isVietnameseName": (()=>isVietnameseName),
    "translateGender": (()=>translateGender)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$base$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/types/base.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$ua$2d$parser$2d$js$2f$src$2f$main$2f$ua$2d$parser$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/ua-parser-js/src/main/ua-parser.mjs [app-rsc] (ecmascript)");
;
;
const getDeviceInfo = ()=>{
    if ("TURBOPACK compile-time truthy", 1) {
        return {
            deviceType: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$base$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["DeviceType"].OTHER,
            deviceName: "Dell Latitude 5290"
        };
    }
    "TURBOPACK unreachable";
    const parser = undefined;
    const result = undefined;
    // Xác định deviceType
    let deviceType;
    const device = undefined;
    const os = undefined;
    // Lấy deviceName
    const deviceName = undefined;
};
const isEmail = (input)=>{
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(input);
};
const isPhoneNumber = (input)=>{
    const phoneRegex = /^\d{10,11}$/; // Giả sử số điện thoại Việt Nam có 10-11 chữ số
    return phoneRegex.test(input);
};
const formatPhoneNumber = (phone)=>{
    if (!phone) return "";
    // Loại bỏ tất cả các ký tự không phải số
    const cleaned = phone.replace(/\D/g, "");
    // Kiểm tra độ dài và định dạng theo quy tắc Việt Nam
    if (cleaned.length === 10) {
        return `${cleaned.slice(0, 3)} ${cleaned.slice(3, 6)} ${cleaned.slice(6)}`;
    } else if (cleaned.length === 11) {
        return `${cleaned.slice(0, 4)} ${cleaned.slice(4, 7)} ${cleaned.slice(7)}`;
    }
    return cleaned;
};
const formatDate = (date)=>{
    const day = date.getDate().toString().padStart(2, "0");
    const month = (date.getMonth() + 1).toString().padStart(2, "0");
    const year = date.getFullYear();
    return `${day}/${month}/${year}`;
};
const translateGender = (gender)=>{
    if (gender.toLowerCase() === "male") return "Nam";
    if (gender.toLowerCase() === "female") return "Nữ";
    return gender;
};
const isVietnameseName = (input)=>{
    // Regex cho tên tiếng Việt có dấu hoặc không dấu
    // Cho phép chữ cái, dấu cách và dấu tiếng Việt
    // Yêu cầu ít nhất 2 từ (họ và tên)
    const vietnameseNameRegex = /^[A-Za-zÀÁÂÃÈÉÊÌÍÒÓÔÕÙÚĂĐĨŨƠàáâãèéêìíòóôõùúăđĩũơƯĂẠẢẤẦẨẪẬẮẰẲẴẶẸẺẼỀỀỂưăạảấầẩẫậắằẳẵặẹẻẽềềểỄỆỈỊỌỎỐỒỔỖỘỚỜỞỠỢỤỦỨỪễệỉịọỏốồổỗộớờởỡợụủứừỬỮỰỲỴÝỶỸửữựỳỵỷỹ]+(\s[A-Za-zÀÁÂÃÈÉÊÌÍÒÓÔÕÙÚĂĐĨŨƠàáâãèéêìíòóôõùúăđĩũơƯĂẠẢẤẦẨẪẬẮẰẲẴẶẸẺẼỀỀỂưăạảấầẩẫậắằẳẵặẹẻẽềềểỄỆỈỊỌỎỐỒỔỖỘỚỜỞỠỢỤỦỨỪễệỉịọỏốồổỗộớờởỡợụủứừỬỮỰỲỴÝỶỸửữựỳỵỷỹ]+)+$/;
    return vietnameseNameRegex.test(input);
};
}}),
"[project]/src/actions/auth.action.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"0025527522b7882fd826ad1ca0dce0d0ce6726c702":"logout","00d41e3c118c2c91abbbf555473d2cc2477f0f3f28":"refreshToken","4067db1247ecf13e53e76ec48d1280e67e62705ece":"initiateForgotPassword","40e73854e278c980b5cec96ba96518bf359593fae1":"initiateRegistration","6010c31b664c9e4f4ed8e68a84ae00fb9d01e4af9b":"verifyForgotPasswordOtp","606e93584058962effb204a2e915343a3ed5c58fb4":"verifyOtp","6077e13672133007b3a4e8c9c00b04c9dc9718a30c":"resetPassword","60f5648f98cb3e7abcea16c8fb2dfdbed59b782834":"confirmResetPassword","70a829aa97efcb1ce5d25cc2ab07599c6328b4104f":"changePassword","786a5cddf431ffb57c7e3310435586f370d34355f2":"login","7cd3a5e00c5aa3d7589035e8aa10387789473d26e0":"completeRegistration"},"",""] */ __turbopack_context__.s({
    "changePassword": (()=>changePassword),
    "completeRegistration": (()=>completeRegistration),
    "confirmResetPassword": (()=>confirmResetPassword),
    "initiateForgotPassword": (()=>initiateForgotPassword),
    "initiateRegistration": (()=>initiateRegistration),
    "login": (()=>login),
    "logout": (()=>logout),
    "refreshToken": (()=>refreshToken),
    "resetPassword": (()=>resetPassword),
    "verifyForgotPasswordOtp": (()=>verifyForgotPasswordOtp),
    "verifyOtp": (()=>verifyOtp)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/server-reference.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$app$2d$render$2f$encryption$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/app-render/encryption.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/axios/lib/axios.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$axios$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/axios.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$authStore$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/stores/authStore.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$helpers$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/utils/helpers.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-validate.js [app-rsc] (ecmascript)");
;
;
;
;
;
;
async function initiateRegistration(identifier) {
    try {
        // Determine if the identifier is an email or phone number
        const isEmailFormat = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$helpers$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isEmail"])(identifier);
        const serverAxios = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$axios$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createAxiosInstance"])();
        const response = await serverAxios.post("/auth/register/initiate", {
            [isEmailFormat ? "email" : "phoneNumber"]: identifier
        });
        const { registrationId } = response.data;
        return {
            success: true,
            registrationId
        };
    } catch (error) {
        console.error("Initiate registration failed:", error);
        return {
            success: false,
            error: error instanceof Error ? error.message : "Unknown error"
        };
    }
}
async function verifyOtp(registrationId, otp) {
    try {
        const serverAxios = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$axios$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createAxiosInstance"])();
        const response = await serverAxios.post("/auth/register/verify", {
            registrationId,
            otp
        });
        return {
            success: true,
            data: response.data
        };
    } catch (error) {
        console.error("OTP verification failed:", error);
        return {
            success: false,
            error: error instanceof Error ? error.message : "Unknown error"
        };
    }
}
async function completeRegistration(registrationId, password, fullName, dateOfBirth, gender) {
    try {
        const serverAxios = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$axios$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createAxiosInstance"])();
        const response = await serverAxios.post("/auth/register/complete", {
            registrationId,
            password,
            fullName,
            dateOfBirth,
            gender
        });
        const { user } = response.data;
        return {
            success: true,
            user
        };
    } catch (error) {
        console.error("Complete registration failed:", error);
        return {
            success: false,
            error: error instanceof Error ? error.message : "Unknown error"
        };
    }
}
async function login(identifier, password, deviceName, deviceType) {
    try {
        // Create a clean axios instance for login (no token needed)
        const serverAxios = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$axios$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createAxiosInstance"])();
        // Determine if identifier is email or phone number
        const response = await serverAxios.post("/auth/login", {
            [(0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$helpers$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isEmail"])(identifier) ? "email" : "phoneNumber"]: identifier,
            password,
            deviceName,
            deviceType
        });
        // Extract response data
        const { user, accessToken, refreshToken, deviceId } = response.data;
        // Validate required fields
        if (!accessToken || !refreshToken || !deviceId) {
            throw new Error("Invalid login response: missing required tokens");
        }
        return {
            success: true,
            user,
            accessToken,
            refreshToken,
            deviceId
        };
    } catch (error) {
        console.error("Login failed:", error);
        return {
            success: false,
            error: error instanceof Error ? error.message : "Unknown error"
        };
    }
}
async function logout() {
    try {
        const authState = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$authStore$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["useAuthStore"].getState();
        const refreshToken = authState.refreshToken;
        const accessToken = authState.accessToken || "";
        // Only attempt to call the logout API if we have a refresh token
        if (refreshToken) {
            try {
                const serverAxios = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$axios$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createAxiosInstance"])(accessToken);
                await serverAxios.post("/auth/logout", {}, {
                    headers: {
                        "refresh-token": refreshToken
                    }
                });
            } catch (apiError) {
                // Log but continue with local logout even if API call fails
                console.error("API logout failed, continuing with local logout:", apiError);
            }
        }
        // Always return success to ensure UI updates
        return {
            success: true
        };
    } catch (error) {
        console.error("Logout failed:", error);
        return {
            success: false,
            error: error instanceof Error ? error.message : "Unknown error"
        };
    }
}
async function refreshToken() {
    try {
        const authState = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$stores$2f$authStore$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["useAuthStore"].getState();
        const refreshToken = authState.refreshToken;
        const deviceId = authState.deviceId;
        if (!refreshToken) {
            throw new Error("No refresh token available");
        }
        if (!deviceId) {
            throw new Error("No device ID available");
        }
        // Use the dedicated refresh token axios instance
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$axios$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["refreshTokenAxios"].post("/auth/refresh", {
            refreshToken,
            deviceId
        });
        if (!response.data || !response.data.accessToken) {
            throw new Error("Invalid response from refresh token API");
        }
        const { accessToken } = response.data;
        const device = response.data.device;
        // Update tokens in the store if in browser environment
        if ("TURBOPACK compile-time falsy", 0) {
            "TURBOPACK unreachable";
        }
        // Update cookie if running on server
        if ("TURBOPACK compile-time truthy", 1) {
            try {
                const { cookies } = await __turbopack_context__.r("[project]/node_modules/next/headers.js [app-rsc] (ecmascript, async loader)")(__turbopack_context__.i);
                (await cookies()).set("access_token", accessToken, {
                    httpOnly: true,
                    secure: ("TURBOPACK compile-time value", "development") === "production",
                    maxAge: 60 * 60 * 24 * 7,
                    path: "/"
                });
            } catch (cookieError) {
                console.error("Failed to set cookie:", cookieError);
            // Continue even if cookie setting fails
            }
        }
        return {
            success: true,
            accessToken,
            device
        };
    } catch (error) {
        // Only logout if we're in the browser
        if ("TURBOPACK compile-time falsy", 0) {
            "TURBOPACK unreachable";
        }
        return {
            success: false,
            error: error instanceof Error ? error.message : "Unknown error"
        };
    }
}
async function initiateForgotPassword(identifier) {
    try {
        // Kiểm tra xem identifier là email hay số điện thoại
        const isEmailFormat = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$helpers$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isEmail"])(identifier);
        const serverAxios = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$axios$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createAxiosInstance"])();
        const response = await serverAxios.post("/auth/forgot-password", {
            [isEmailFormat ? "email" : "phoneNumber"]: identifier
        });
        const { resetId } = response.data;
        return {
            success: true,
            resetId
        };
    } catch (error) {
        console.error("Initiate forgot password failed:", error);
        return {
            success: false,
            error: error instanceof Error ? error.message : "Unknown error"
        };
    }
}
async function verifyForgotPasswordOtp(resetId, otp) {
    try {
        const serverAxios = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$axios$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createAxiosInstance"])();
        const response = await serverAxios.post("/auth/forgot-password/verify", {
            resetId,
            otp
        });
        return {
            success: true,
            data: response.data
        };
    } catch (error) {
        console.error("Verify forgot password OTP failed:", error);
        return {
            success: false,
            error: error instanceof Error ? error.message : "Unknown error"
        };
    }
}
async function resetPassword(resetId, newPassword) {
    try {
        const serverAxios = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$axios$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createAxiosInstance"])();
        const response = await serverAxios.post("/auth/forgot-password/reset", {
            resetId,
            newPassword
        });
        return {
            success: true,
            data: response.data
        };
    } catch (error) {
        console.error("Reset password failed:", error);
        return {
            success: false,
            error: error instanceof Error ? error.message : "Unknown error"
        };
    }
}
async function changePassword(currentPassword, newPassword, accessToken) {
    try {
        if (!accessToken) {
            return {
                success: false,
                error: "Bạn cần đăng nhập lại để thực hiện thao tác này"
            };
        }
        const serverAxios = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$axios$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createAxiosInstance"])(accessToken);
        const response = await serverAxios.put("/auth/change-password", {
            currentPassword,
            newPassword
        });
        return {
            success: true,
            message: response.data.message || "Đổi mật khẩu thành công"
        };
    } catch (error) {
        console.error("Change password failed:", error);
        // Xử lý lỗi từ API
        if (__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"].isAxiosError(error) && error.response) {
            console.error("Error response:", {
                status: error.response.status,
                data: error.response.data
            });
            // Kiểm tra lỗi mật khẩu cũ không đúng
            if (error.response.status === 400 || error.response.status === 401) {
                if (error.response.data?.message) {
                    // Nếu server trả về thông báo lỗi cụ thể
                    return {
                        success: false,
                        error: error.response.data.message
                    };
                }
                // Nếu không có thông báo cụ thể, kiểm tra nếu là lỗi mật khẩu cũ không đúng
                if (error.response.data?.error === "INVALID_CURRENT_PASSWORD") {
                    return {
                        success: false,
                        error: "Mật khẩu hiện tại không đúng"
                    };
                }
            }
        }
        return {
            success: false,
            error: error instanceof Error ? error.message : "Đã xảy ra lỗi khi đổi mật khẩu"
        };
    }
}
async function confirmResetPassword(token, newPassword) {
    try {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$axios$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"].post("/auth/reset-password/confirm", {
            token,
            newPassword
        });
        return {
            success: true,
            message: response.data.message || "Password has been reset successfully"
        };
    } catch (error) {
        console.error("Confirm reset password failed:", error);
        return {
            success: false,
            error: error instanceof Error ? error.message : "Unknown error"
        };
    }
}
;
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ensureServerEntryExports"])([
    initiateRegistration,
    verifyOtp,
    completeRegistration,
    login,
    logout,
    refreshToken,
    initiateForgotPassword,
    verifyForgotPasswordOtp,
    resetPassword,
    changePassword,
    confirmResetPassword
]);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(initiateRegistration, "40e73854e278c980b5cec96ba96518bf359593fae1", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(verifyOtp, "606e93584058962effb204a2e915343a3ed5c58fb4", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(completeRegistration, "7cd3a5e00c5aa3d7589035e8aa10387789473d26e0", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(login, "786a5cddf431ffb57c7e3310435586f370d34355f2", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(logout, "0025527522b7882fd826ad1ca0dce0d0ce6726c702", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(refreshToken, "00d41e3c118c2c91abbbf555473d2cc2477f0f3f28", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(initiateForgotPassword, "4067db1247ecf13e53e76ec48d1280e67e62705ece", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(verifyForgotPasswordOtp, "6010c31b664c9e4f4ed8e68a84ae00fb9d01e4af9b", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(resetPassword, "6077e13672133007b3a4e8c9c00b04c9dc9718a30c", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(changePassword, "70a829aa97efcb1ce5d25cc2ab07599c6328b4104f", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(confirmResetPassword, "60f5648f98cb3e7abcea16c8fb2dfdbed59b782834", null);
}}),
"[project]/src/utils/relationshipCache.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// Define a type for relationship data
__turbopack_context__.s({
    "cacheRelationship": (()=>cacheRelationship),
    "clearRelationshipCache": (()=>clearRelationshipCache),
    "getCachedRelationship": (()=>getCachedRelationship),
    "isRelationshipCacheValid": (()=>isRelationshipCacheValid),
    "removeCachedRelationship": (()=>removeCachedRelationship)
});
// Cache for relationship data
const relationshipCache = {};
// Cache expiration time in milliseconds (5 minutes)
const RELATIONSHIP_CACHE_EXPIRATION = 5 * 60 * 1000;
function isRelationshipCacheValid(targetId) {
    if (!relationshipCache[targetId]) return false;
    const now = Date.now();
    const cacheTime = relationshipCache[targetId].timestamp;
    return now - cacheTime < RELATIONSHIP_CACHE_EXPIRATION;
}
function getCachedRelationship(targetId) {
    if (isRelationshipCacheValid(targetId)) {
        return relationshipCache[targetId].data;
    }
    return null;
}
function cacheRelationship(targetId, data) {
    relationshipCache[targetId] = {
        data,
        timestamp: Date.now()
    };
}
function removeCachedRelationship(targetId) {
    if (relationshipCache[targetId]) {
        delete relationshipCache[targetId];
    }
}
function clearRelationshipCache() {
    Object.keys(relationshipCache).forEach((key)=>{
        delete relationshipCache[key];
    });
}
}}),
"[project]/src/actions/friend.action.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"40224cec15feb7e5af4da2d3672710db70fdde3b38":"getSentFriendRequests","406f88a7e8a3e3d4c6790948f974957b18aa302d3c":"getReceivedFriendRequests","40765b7ae1092c3a68fcf914347f0b297373fe950e":"getFriendsList","40f7015ada29a7b632b989c001759c1929ea1b50f7":"getBlockedUsers","602666daee370c4b7cd541253a6c5b3eda846249eb":"rejectFriendRequest","604dd71660fae54a3306cda00bc2ac0c2ee23d2141":"removeFriend","607be4a979876d1de147e5859be475d19de660820e":"unblockUser","6089fcf8a1e0e2a2d138d4e347f4aa0ded425d20fe":"batchGetRelationships","608b8e3c5e2d1e07725287aa26bf15eff03cd8a719":"acceptFriendRequest","60db05fee2171b5299bcb41d3928b3ff3198ff681b":"cancelFriendRequest","60dd5c8b1daa63a4e0e736658d190e3917741a5c3d":"getRelationship","60e3d50afefa1bcc6a6ce634bce273f4012a1585b6":"blockUser","707f90db71af7c9d67bfc3803f5b43301bce989fa3":"respondToFriendRequest","7095df0f39808bb81a7443e68a7f2492274f7660b0":"sendFriendRequest"},"",""] */ __turbopack_context__.s({
    "acceptFriendRequest": (()=>acceptFriendRequest),
    "batchGetRelationships": (()=>batchGetRelationships),
    "blockUser": (()=>blockUser),
    "cancelFriendRequest": (()=>cancelFriendRequest),
    "getBlockedUsers": (()=>getBlockedUsers),
    "getFriendsList": (()=>getFriendsList),
    "getReceivedFriendRequests": (()=>getReceivedFriendRequests),
    "getRelationship": (()=>getRelationship),
    "getSentFriendRequests": (()=>getSentFriendRequests),
    "rejectFriendRequest": (()=>rejectFriendRequest),
    "removeFriend": (()=>removeFriend),
    "respondToFriendRequest": (()=>respondToFriendRequest),
    "sendFriendRequest": (()=>sendFriendRequest),
    "unblockUser": (()=>unblockUser)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/server-reference.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$app$2d$render$2f$encryption$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/app-render/encryption.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/axios/lib/axios.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$axios$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/axios.ts [app-rsc] (ecmascript)");
// No need to import Friend from @/types/base
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$relationshipCache$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/utils/relationshipCache.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-validate.js [app-rsc] (ecmascript)");
;
;
;
;
;
async function getFriendsList(token) {
    try {
        console.log("Token received in getFriendsList:", token ? `Token exists: ${token.substring(0, 10)}...` : "No token");
        const serverAxios = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$axios$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createAxiosInstance"])(token);
        console.log("Authorization header:", serverAxios.defaults.headers.common["Authorization"]);
        const response = await serverAxios.get("/friends/list");
        const friendships = response.data;
        // Transform the API response to the format expected by UI components
        const friends = friendships.map((friendship)=>({
                id: friendship.friend.id,
                fullName: friendship.friend.userInfo.fullName,
                profilePictureUrl: friendship.friend.userInfo.profilePictureUrl,
                statusMessage: friendship.friend.userInfo.statusMessage,
                lastSeen: friendship.friend.userInfo.lastSeen,
                email: friendship.friend.email,
                phoneNumber: friendship.friend.phoneNumber,
                gender: friendship.friend.userInfo.gender,
                bio: friendship.friend.userInfo.bio,
                dateOfBirth: friendship.friend.userInfo.dateOfBirth
            }));
        return {
            success: true,
            friends
        };
    } catch (error) {
        console.error("Get friends list failed:", error);
        return {
            success: false,
            error: error instanceof Error ? error.message : "Unknown error"
        };
    }
}
async function getReceivedFriendRequests(token) {
    try {
        const serverAxios = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$axios$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createAxiosInstance"])(token);
        const response = await serverAxios.get("/friends/requests/received");
        // Log raw API response
        console.log("Raw received friend requests:", response.data);
        // Transform the API response to the format expected by UI components
        const requests = response.data.map((request)=>({
                id: request.id,
                fullName: request.sender.userInfo.fullName,
                profilePictureUrl: request.sender.userInfo.profilePictureUrl,
                // Get the introduce message from the API response
                message: request.introduce || "",
                timeAgo: new Date(request.createdAt).toLocaleDateString(),
                // Add senderId for fetching complete user data
                senderId: request.sender.id
            }));
        return {
            success: true,
            requests
        };
    } catch (error) {
        console.error("Get received friend requests failed:", error);
        return {
            success: false,
            error: error instanceof Error ? error.message : "Unknown error"
        };
    }
}
async function getSentFriendRequests(token) {
    try {
        const serverAxios = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$axios$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createAxiosInstance"])(token);
        const response = await serverAxios.get("/friends/requests/sent");
        // Log raw API response
        console.log("Raw sent friend requests:", response.data);
        // Transform the API response to the format expected by UI components
        const requests = response.data.map((request)=>({
                id: request.id,
                fullName: request.receiver.userInfo.fullName,
                profilePictureUrl: request.receiver.userInfo.profilePictureUrl,
                timeAgo: new Date(request.createdAt).toLocaleDateString(),
                // Add receiverId for fetching complete user data
                receiverId: request.receiver.id
            }));
        return {
            success: true,
            requests
        };
    } catch (error) {
        console.error("Get sent friend requests failed:", error);
        return {
            success: false,
            error: error instanceof Error ? error.message : "Unknown error"
        };
    }
}
async function sendFriendRequest(userId, introduce, token) {
    try {
        console.log(`Sending friend request to user ${userId} with token: ${!!token}`);
        const serverAxios = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$axios$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createAxiosInstance"])(token);
        // Tạo payload theo đúng format API yêu cầu
        const payload = {
            receiverId: userId
        };
        // Thêm introduce nếu có
        if (introduce && introduce.trim()) {
            payload.introduce = introduce.trim();
        }
        console.log("Friend request payload:", payload);
        const response = await serverAxios.post("/friends/request", payload);
        console.log("Friend request response:", response.data);
        // Update relationship cache to reflect the new pending status
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$relationshipCache$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["removeCachedRelationship"])(userId);
        return {
            success: true,
            data: response.data
        };
    } catch (error) {
        console.error("Send friend request failed:", error);
        // Log chi tiết hơn về lỗi
        if (__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"].isAxiosError(error) && error.response) {
            console.error("Error response:", {
                status: error.response.status,
                data: error.response.data,
                headers: error.response.headers
            });
            // Return specific error message based on status code
            if (error.response.status === 401) {
                return {
                    success: false,
                    error: "Bạn cần đăng nhập để thực hiện hành động này"
                };
            }
            if (error.response.status === 403) {
                return {
                    success: false,
                    error: "Bạn không có quyền thực hiện hành động này"
                };
            }
            if (error.response.status === 404) {
                return {
                    success: false,
                    error: "Không tìm thấy người dùng"
                };
            }
            if (error.response.data?.message) {
                return {
                    success: false,
                    error: error.response.data.message
                };
            }
        }
        // Network errors or other errors
        if (error instanceof Error) {
            return {
                success: false,
                error: error.message
            };
        }
        return {
            success: false,
            error: "Unknown error"
        };
    }
}
async function respondToFriendRequest(requestId, status, token) {
    try {
        console.log(`respondToFriendRequest: requestId=${requestId}, status=${status}, hasToken=${!!token}`);
        const serverAxios = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$axios$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createAxiosInstance"])(token);
        const payload = {
            requestId,
            status
        };
        console.log("Request payload:", payload);
        const response = await serverAxios.put("/friends/respond", payload);
        console.log("API response:", response.data);
        // Clear all relationship caches since we don't know which user this affects
        // This is a simple approach - a more sophisticated one would track which user's request this is
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$relationshipCache$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["clearRelationshipCache"])();
        return {
            success: true,
            data: response.data
        };
    } catch (error) {
        console.error("Respond to friend request failed:", error);
        // Log chi tiết hơn về lỗi
        if (__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"].isAxiosError(error) && error.response) {
            console.error("Error response:", {
                status: error.response.status,
                data: error.response.data,
                headers: error.response.headers
            });
            // Return specific error message based on status code
            if (error.response.status === 401) {
                return {
                    success: false,
                    error: "Bạn cần đăng nhập để thực hiện hành động này"
                };
            }
            if (error.response.status === 403) {
                return {
                    success: false,
                    error: "Bạn không có quyền thực hiện hành động này"
                };
            }
            if (error.response.status === 404) {
                return {
                    success: false,
                    error: "Không tìm thấy lời mời kết bạn"
                };
            }
            if (error.response.data?.message) {
                return {
                    success: false,
                    error: error.response.data.message
                };
            }
        }
        return {
            success: false,
            error: error instanceof Error ? error.message : "Unknown error"
        };
    }
}
async function acceptFriendRequest(requestId, token) {
    return respondToFriendRequest(requestId, "ACCEPTED", token);
}
async function rejectFriendRequest(requestId, token) {
    console.log("rejectFriendRequest called with requestId:", requestId, "and token:", !!token);
    const result = await respondToFriendRequest(requestId, "DECLINED", token);
    console.log("respondToFriendRequest result:", result);
    return result;
}
async function removeFriend(friendId, token) {
    try {
        const serverAxios = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$axios$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createAxiosInstance"])(token);
        const response = await serverAxios.delete(`/friends/${friendId}`);
        // Update relationship cache
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$relationshipCache$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["removeCachedRelationship"])(friendId);
        return {
            success: true,
            data: response.data
        };
    } catch (error) {
        console.error("Remove friend failed:", error);
        // Log chi tiết hơn về lỗi
        if (__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"].isAxiosError(error) && error.response) {
            console.error("Error response:", {
                status: error.response.status,
                data: error.response.data,
                headers: error.response.headers
            });
            // Return specific error message based on status code
            if (error.response.status === 401) {
                return {
                    success: false,
                    error: "Bạn cần đăng nhập để thực hiện hành động này"
                };
            }
            if (error.response.status === 403) {
                return {
                    success: false,
                    error: "Bạn không có quyền thực hiện hành động này"
                };
            }
            if (error.response.status === 404) {
                return {
                    success: false,
                    error: "Không tìm thấy người dùng"
                };
            }
            if (error.response.data?.message) {
                return {
                    success: false,
                    error: error.response.data.message
                };
            }
        }
        // Network errors or other errors
        if (error instanceof Error) {
            return {
                success: false,
                error: error.message
            };
        }
        return {
            success: false,
            error: "Unknown error"
        };
    }
}
async function cancelFriendRequest(requestId, token) {
    try {
        const serverAxios = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$axios$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createAxiosInstance"])(token);
        const response = await serverAxios.delete(`/friends/request/${requestId}`);
        // Clear all relationship caches since we don't know which user this affects
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$relationshipCache$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["clearRelationshipCache"])();
        return {
            success: true,
            data: response.data
        };
    } catch (error) {
        console.error("Cancel friend request failed:", error);
        return {
            success: false,
            error: error instanceof Error ? error.message : "Unknown error"
        };
    }
}
async function blockUser(userId, token) {
    try {
        const serverAxios = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$axios$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createAxiosInstance"])(token);
        const response = await serverAxios.post(`/friends/block/${userId}`);
        // Update relationship cache
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$relationshipCache$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["removeCachedRelationship"])(userId);
        return {
            success: true,
            data: response.data
        };
    } catch (error) {
        console.error("Block user failed:", error);
        return {
            success: false,
            error: error instanceof Error ? error.message : "Unknown error"
        };
    }
}
async function unblockUser(userId, token) {
    try {
        const serverAxios = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$axios$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createAxiosInstance"])(token);
        const response = await serverAxios.delete(`/friends/block/${userId}`);
        // Update relationship cache
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$relationshipCache$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["removeCachedRelationship"])(userId);
        return {
            success: true,
            data: response.data
        };
    } catch (error) {
        console.error("Unblock user failed:", error);
        return {
            success: false,
            error: error instanceof Error ? error.message : "Unknown error"
        };
    }
}
async function getBlockedUsers(token) {
    try {
        const serverAxios = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$axios$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createAxiosInstance"])(token);
        const response = await serverAxios.get("/friends/blocked");
        // Transform the API response to the format expected by UI components
        const users = response.data.map((item)=>({
                id: item.receiver.id,
                fullName: item.receiver.userInfo.fullName,
                profilePictureUrl: item.receiver.userInfo.profilePictureUrl,
                email: item.receiver.email,
                phoneNumber: item.receiver.phoneNumber
            }));
        return {
            success: true,
            users
        };
    } catch (error) {
        console.error("Get blocked users failed:", error);
        return {
            success: false,
            error: error instanceof Error ? error.message : "Unknown error"
        };
    }
}
async function batchGetRelationships(userIds, token) {
    // Filter out duplicate IDs
    const uniqueIds = [
        ...new Set(userIds)
    ];
    // Check which relationships are already in cache
    const cachedRelationships = {};
    const idsToFetch = [];
    uniqueIds.forEach((id)=>{
        const cachedData = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$relationshipCache$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getCachedRelationship"])(id);
        if (cachedData) {
            cachedRelationships[id] = cachedData;
        } else {
            idsToFetch.push(id);
        }
    });
    // If all relationships are in cache, return immediately
    if (idsToFetch.length === 0) {
        console.log(`All ${uniqueIds.length} relationships found in cache`);
        return {
            success: true,
            relationships: cachedRelationships
        };
    }
    // Otherwise, fetch the remaining relationships
    try {
        console.log(`Batch fetching ${idsToFetch.length} relationships`);
        // Fetch each relationship individually (could be optimized with a batch API endpoint)
        const fetchPromises = idsToFetch.map((id)=>getRelationship(id, token));
        const results = await Promise.all(fetchPromises);
        // Process results
        results.forEach((result, index)=>{
            if (result.success && result.data) {
                const userId = idsToFetch[index];
                cachedRelationships[userId] = result.data;
            }
        });
        return {
            success: true,
            relationships: cachedRelationships
        };
    } catch (error) {
        console.error("Batch get relationships failed:", error);
        return {
            success: false,
            error: error instanceof Error ? error.message : "Unknown error",
            relationships: cachedRelationships
        };
    }
}
async function getRelationship(targetId, token) {
    try {
        // Check if relationship data is in cache and still valid
        const cachedData = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$relationshipCache$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getCachedRelationship"])(targetId);
        if (cachedData) {
            console.log(`Using cached relationship data for user ID: ${targetId}`);
            return {
                success: true,
                data: cachedData
            };
        }
        // Sử dụng serverAxios để gửi token xác thực
        const serverAxios = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$axios$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createAxiosInstance"])(token);
        const response = await serverAxios.get(`/friends/relationship/${targetId}`);
        console.log("Relationship response:", response.data);
        // Store relationship data in cache
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$relationshipCache$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["cacheRelationship"])(targetId, response.data);
        return {
            success: true,
            data: response.data
        };
    } catch (error) {
        console.error("Get relationship failed:", error);
        // Log chi tiết hơn về lỗi
        if (__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"].isAxiosError(error) && error.response) {
            console.error("Error response:", {
                status: error.response.status,
                data: error.response.data,
                headers: error.response.headers
            });
            // Return specific error message based on status code
            if (error.response.status === 401) {
                return {
                    success: false,
                    error: "Bạn cần đăng nhập để thực hiện hành động này"
                };
            }
            if (error.response.status === 403) {
                return {
                    success: false,
                    error: "Bạn không có quyền thực hiện hành động này"
                };
            }
            if (error.response.status === 404) {
                return {
                    success: false,
                    error: "Không tìm thấy người dùng"
                };
            }
            if (error.response.data?.message) {
                return {
                    success: false,
                    error: error.response.data.message
                };
            }
        }
        return {
            success: false,
            error: error instanceof Error ? error.message : "Unknown error"
        };
    }
}
;
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ensureServerEntryExports"])([
    getFriendsList,
    getReceivedFriendRequests,
    getSentFriendRequests,
    sendFriendRequest,
    respondToFriendRequest,
    acceptFriendRequest,
    rejectFriendRequest,
    removeFriend,
    cancelFriendRequest,
    blockUser,
    unblockUser,
    getBlockedUsers,
    batchGetRelationships,
    getRelationship
]);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(getFriendsList, "40765b7ae1092c3a68fcf914347f0b297373fe950e", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(getReceivedFriendRequests, "406f88a7e8a3e3d4c6790948f974957b18aa302d3c", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(getSentFriendRequests, "40224cec15feb7e5af4da2d3672710db70fdde3b38", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(sendFriendRequest, "7095df0f39808bb81a7443e68a7f2492274f7660b0", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(respondToFriendRequest, "707f90db71af7c9d67bfc3803f5b43301bce989fa3", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(acceptFriendRequest, "608b8e3c5e2d1e07725287aa26bf15eff03cd8a719", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(rejectFriendRequest, "602666daee370c4b7cd541253a6c5b3eda846249eb", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(removeFriend, "604dd71660fae54a3306cda00bc2ac0c2ee23d2141", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(cancelFriendRequest, "60db05fee2171b5299bcb41d3928b3ff3198ff681b", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(blockUser, "60e3d50afefa1bcc6a6ce634bce273f4012a1585b6", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(unblockUser, "607be4a979876d1de147e5859be475d19de660820e", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(getBlockedUsers, "40f7015ada29a7b632b989c001759c1929ea1b50f7", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(batchGetRelationships, "6089fcf8a1e0e2a2d138d4e347f4aa0ded425d20fe", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(getRelationship, "60dd5c8b1daa63a4e0e736658d190e3917741a5c3d", null);
}}),
"[project]/src/actions/ai.action.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"40b0a20f4d004bba2947dda603ee685018f4a81036":"generateAIResponse","607fede0612bb083abc8d0cfd789928543f9a39886":"enhanceMessage","60ea3341868954ec4cdbada52f2a60b7a9fbca7fbc":"freestyleAI","70b4780a6698dd7795b394259882542f9405b058a7":"summarizeText"},"",""] */ __turbopack_context__.s({
    "enhanceMessage": (()=>enhanceMessage),
    "freestyleAI": (()=>freestyleAI),
    "generateAIResponse": (()=>generateAIResponse),
    "summarizeText": (()=>summarizeText)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/server-reference.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$app$2d$render$2f$encryption$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/app-render/encryption.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$axios$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/axios.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-validate.js [app-rsc] (ecmascript)");
;
;
;
async function enhanceMessage(message, previousMessages) {
    try {
        const axiosInstance = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$axios$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createAxiosInstance"])();
        console.log("axiosInstance", axiosInstance.defaults.baseURL);
        const response = await axiosInstance.post("/ai/enhance", {
            message,
            previousMessages
        });
        const result = response.data;
        return {
            success: true,
            enhancedMessage: result.enhancedMessage
        };
    } catch (error) {
        console.error("AI message enhancement failed:", error);
        return {
            success: false,
            error: error instanceof Error ? error.message : "Unknown error"
        };
    }
}
async function generateAIResponse(prompt) {
    try {
        const axiosInstance = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$axios$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createAxiosInstance"])();
        const response = await axiosInstance.post("/ai/generate", {
            prompt
        });
        return {
            success: true,
            response: response.data.response
        };
    } catch (error) {
        console.error("AI response generation failed:", error);
        return {
            success: false,
            error: error instanceof Error ? error.message : "Unknown error"
        };
    }
}
async function summarizeText(text, maxLength, previousMessages) {
    try {
        const axiosInstance = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$axios$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createAxiosInstance"])();
        const payload = {
            text
        };
        if (maxLength) {
            payload.maxLength = maxLength.toString();
        }
        if (previousMessages) {
            payload.previousMessages = previousMessages;
        }
        const response = await axiosInstance.post("/ai/summarize", payload);
        return {
            success: true,
            summary: response.data.summary
        };
    } catch (error) {
        console.error("AI text summarization failed:", error);
        return {
            success: false,
            error: error instanceof Error ? error.message : "Unknown error"
        };
    }
}
async function freestyleAI(prompt, systemPrompt) {
    try {
        const axiosInstance = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$axios$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createAxiosInstance"])();
        const payload = {
            prompt
        };
        if (systemPrompt) {
            payload.systemPrompt = systemPrompt;
        }
        const response = await axiosInstance.post("/ai/freestyle", payload);
        return {
            success: true,
            response: response.data.response
        };
    } catch (error) {
        console.error("AI freestyle request failed:", error);
        return {
            success: false,
            error: error instanceof Error ? error.message : "Unknown error"
        };
    }
}
;
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ensureServerEntryExports"])([
    enhanceMessage,
    generateAIResponse,
    summarizeText,
    freestyleAI
]);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(enhanceMessage, "607fede0612bb083abc8d0cfd789928543f9a39886", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(generateAIResponse, "40b0a20f4d004bba2947dda603ee685018f4a81036", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(summarizeText, "70b4780a6698dd7795b394259882542f9405b058a7", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(freestyleAI, "60ea3341868954ec4cdbada52f2a60b7a9fbca7fbc", null);
}}),
"[project]/.next-internal/server/app/(protected)/dashboard/page/actions.js { ACTIONS_MODULE0 => \"[project]/src/actions/auth.action.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/src/actions/friend.action.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/src/actions/ai.action.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$actions$2f$auth$2e$action$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/actions/auth.action.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$actions$2f$friend$2e$action$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/actions/friend.action.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$actions$2f$ai$2e$action$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/actions/ai.action.ts [app-rsc] (ecmascript)");
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
}}),
"[project]/.next-internal/server/app/(protected)/dashboard/page/actions.js { ACTIONS_MODULE0 => \"[project]/src/actions/auth.action.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/src/actions/friend.action.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/src/actions/ai.action.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$actions$2f$auth$2e$action$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/actions/auth.action.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$actions$2f$friend$2e$action$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/actions/friend.action.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$actions$2f$ai$2e$action$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/actions/ai.action.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f28$protected$292f$dashboard$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$src$2f$actions$2f$auth$2e$action$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$src$2f$actions$2f$friend$2e$action$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$src$2f$actions$2f$ai$2e$action$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i('[project]/.next-internal/server/app/(protected)/dashboard/page/actions.js { ACTIONS_MODULE0 => "[project]/src/actions/auth.action.ts [app-rsc] (ecmascript)", ACTIONS_MODULE1 => "[project]/src/actions/friend.action.ts [app-rsc] (ecmascript)", ACTIONS_MODULE2 => "[project]/src/actions/ai.action.ts [app-rsc] (ecmascript)" } [app-rsc] (server actions loader, ecmascript) <locals>');
}}),
"[project]/.next-internal/server/app/(protected)/dashboard/page/actions.js { ACTIONS_MODULE0 => \"[project]/src/actions/auth.action.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/src/actions/friend.action.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/src/actions/ai.action.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript) <exports>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "0025527522b7882fd826ad1ca0dce0d0ce6726c702": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$actions$2f$auth$2e$action$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["logout"]),
    "00d41e3c118c2c91abbbf555473d2cc2477f0f3f28": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$actions$2f$auth$2e$action$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["refreshToken"]),
    "40224cec15feb7e5af4da2d3672710db70fdde3b38": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$actions$2f$friend$2e$action$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getSentFriendRequests"]),
    "4067db1247ecf13e53e76ec48d1280e67e62705ece": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$actions$2f$auth$2e$action$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["initiateForgotPassword"]),
    "406f88a7e8a3e3d4c6790948f974957b18aa302d3c": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$actions$2f$friend$2e$action$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getReceivedFriendRequests"]),
    "40765b7ae1092c3a68fcf914347f0b297373fe950e": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$actions$2f$friend$2e$action$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getFriendsList"]),
    "40b0a20f4d004bba2947dda603ee685018f4a81036": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$actions$2f$ai$2e$action$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["generateAIResponse"]),
    "40e73854e278c980b5cec96ba96518bf359593fae1": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$actions$2f$auth$2e$action$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["initiateRegistration"]),
    "40f7015ada29a7b632b989c001759c1929ea1b50f7": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$actions$2f$friend$2e$action$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getBlockedUsers"]),
    "6010c31b664c9e4f4ed8e68a84ae00fb9d01e4af9b": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$actions$2f$auth$2e$action$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["verifyForgotPasswordOtp"]),
    "602666daee370c4b7cd541253a6c5b3eda846249eb": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$actions$2f$friend$2e$action$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["rejectFriendRequest"]),
    "604dd71660fae54a3306cda00bc2ac0c2ee23d2141": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$actions$2f$friend$2e$action$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["removeFriend"]),
    "606e93584058962effb204a2e915343a3ed5c58fb4": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$actions$2f$auth$2e$action$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["verifyOtp"]),
    "6077e13672133007b3a4e8c9c00b04c9dc9718a30c": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$actions$2f$auth$2e$action$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["resetPassword"]),
    "607be4a979876d1de147e5859be475d19de660820e": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$actions$2f$friend$2e$action$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["unblockUser"]),
    "608b8e3c5e2d1e07725287aa26bf15eff03cd8a719": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$actions$2f$friend$2e$action$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["acceptFriendRequest"]),
    "60db05fee2171b5299bcb41d3928b3ff3198ff681b": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$actions$2f$friend$2e$action$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["cancelFriendRequest"]),
    "60dd5c8b1daa63a4e0e736658d190e3917741a5c3d": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$actions$2f$friend$2e$action$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getRelationship"]),
    "60e3d50afefa1bcc6a6ce634bce273f4012a1585b6": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$actions$2f$friend$2e$action$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["blockUser"]),
    "60f5648f98cb3e7abcea16c8fb2dfdbed59b782834": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$actions$2f$auth$2e$action$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["confirmResetPassword"]),
    "7095df0f39808bb81a7443e68a7f2492274f7660b0": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$actions$2f$friend$2e$action$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["sendFriendRequest"]),
    "70a829aa97efcb1ce5d25cc2ab07599c6328b4104f": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$actions$2f$auth$2e$action$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["changePassword"]),
    "786a5cddf431ffb57c7e3310435586f370d34355f2": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$actions$2f$auth$2e$action$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["login"]),
    "7cd3a5e00c5aa3d7589035e8aa10387789473d26e0": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$actions$2f$auth$2e$action$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["completeRegistration"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$actions$2f$auth$2e$action$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/actions/auth.action.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$actions$2f$friend$2e$action$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/actions/friend.action.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$actions$2f$ai$2e$action$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/actions/ai.action.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f28$protected$292f$dashboard$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$src$2f$actions$2f$auth$2e$action$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$src$2f$actions$2f$friend$2e$action$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$src$2f$actions$2f$ai$2e$action$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i('[project]/.next-internal/server/app/(protected)/dashboard/page/actions.js { ACTIONS_MODULE0 => "[project]/src/actions/auth.action.ts [app-rsc] (ecmascript)", ACTIONS_MODULE1 => "[project]/src/actions/friend.action.ts [app-rsc] (ecmascript)", ACTIONS_MODULE2 => "[project]/src/actions/ai.action.ts [app-rsc] (ecmascript)" } [app-rsc] (server actions loader, ecmascript) <locals>');
}}),
"[project]/.next-internal/server/app/(protected)/dashboard/page/actions.js { ACTIONS_MODULE0 => \"[project]/src/actions/auth.action.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/src/actions/friend.action.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/src/actions/ai.action.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "0025527522b7882fd826ad1ca0dce0d0ce6726c702": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f28$protected$292f$dashboard$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$src$2f$actions$2f$auth$2e$action$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$src$2f$actions$2f$friend$2e$action$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$src$2f$actions$2f$ai$2e$action$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["0025527522b7882fd826ad1ca0dce0d0ce6726c702"]),
    "00d41e3c118c2c91abbbf555473d2cc2477f0f3f28": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f28$protected$292f$dashboard$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$src$2f$actions$2f$auth$2e$action$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$src$2f$actions$2f$friend$2e$action$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$src$2f$actions$2f$ai$2e$action$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["00d41e3c118c2c91abbbf555473d2cc2477f0f3f28"]),
    "40224cec15feb7e5af4da2d3672710db70fdde3b38": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f28$protected$292f$dashboard$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$src$2f$actions$2f$auth$2e$action$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$src$2f$actions$2f$friend$2e$action$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$src$2f$actions$2f$ai$2e$action$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["40224cec15feb7e5af4da2d3672710db70fdde3b38"]),
    "4067db1247ecf13e53e76ec48d1280e67e62705ece": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f28$protected$292f$dashboard$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$src$2f$actions$2f$auth$2e$action$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$src$2f$actions$2f$friend$2e$action$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$src$2f$actions$2f$ai$2e$action$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["4067db1247ecf13e53e76ec48d1280e67e62705ece"]),
    "406f88a7e8a3e3d4c6790948f974957b18aa302d3c": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f28$protected$292f$dashboard$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$src$2f$actions$2f$auth$2e$action$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$src$2f$actions$2f$friend$2e$action$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$src$2f$actions$2f$ai$2e$action$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["406f88a7e8a3e3d4c6790948f974957b18aa302d3c"]),
    "40765b7ae1092c3a68fcf914347f0b297373fe950e": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f28$protected$292f$dashboard$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$src$2f$actions$2f$auth$2e$action$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$src$2f$actions$2f$friend$2e$action$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$src$2f$actions$2f$ai$2e$action$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["40765b7ae1092c3a68fcf914347f0b297373fe950e"]),
    "40b0a20f4d004bba2947dda603ee685018f4a81036": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f28$protected$292f$dashboard$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$src$2f$actions$2f$auth$2e$action$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$src$2f$actions$2f$friend$2e$action$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$src$2f$actions$2f$ai$2e$action$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["40b0a20f4d004bba2947dda603ee685018f4a81036"]),
    "40e73854e278c980b5cec96ba96518bf359593fae1": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f28$protected$292f$dashboard$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$src$2f$actions$2f$auth$2e$action$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$src$2f$actions$2f$friend$2e$action$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$src$2f$actions$2f$ai$2e$action$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["40e73854e278c980b5cec96ba96518bf359593fae1"]),
    "40f7015ada29a7b632b989c001759c1929ea1b50f7": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f28$protected$292f$dashboard$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$src$2f$actions$2f$auth$2e$action$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$src$2f$actions$2f$friend$2e$action$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$src$2f$actions$2f$ai$2e$action$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["40f7015ada29a7b632b989c001759c1929ea1b50f7"]),
    "6010c31b664c9e4f4ed8e68a84ae00fb9d01e4af9b": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f28$protected$292f$dashboard$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$src$2f$actions$2f$auth$2e$action$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$src$2f$actions$2f$friend$2e$action$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$src$2f$actions$2f$ai$2e$action$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["6010c31b664c9e4f4ed8e68a84ae00fb9d01e4af9b"]),
    "602666daee370c4b7cd541253a6c5b3eda846249eb": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f28$protected$292f$dashboard$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$src$2f$actions$2f$auth$2e$action$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$src$2f$actions$2f$friend$2e$action$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$src$2f$actions$2f$ai$2e$action$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["602666daee370c4b7cd541253a6c5b3eda846249eb"]),
    "604dd71660fae54a3306cda00bc2ac0c2ee23d2141": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f28$protected$292f$dashboard$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$src$2f$actions$2f$auth$2e$action$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$src$2f$actions$2f$friend$2e$action$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$src$2f$actions$2f$ai$2e$action$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["604dd71660fae54a3306cda00bc2ac0c2ee23d2141"]),
    "606e93584058962effb204a2e915343a3ed5c58fb4": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f28$protected$292f$dashboard$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$src$2f$actions$2f$auth$2e$action$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$src$2f$actions$2f$friend$2e$action$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$src$2f$actions$2f$ai$2e$action$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["606e93584058962effb204a2e915343a3ed5c58fb4"]),
    "6077e13672133007b3a4e8c9c00b04c9dc9718a30c": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f28$protected$292f$dashboard$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$src$2f$actions$2f$auth$2e$action$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$src$2f$actions$2f$friend$2e$action$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$src$2f$actions$2f$ai$2e$action$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["6077e13672133007b3a4e8c9c00b04c9dc9718a30c"]),
    "607be4a979876d1de147e5859be475d19de660820e": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f28$protected$292f$dashboard$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$src$2f$actions$2f$auth$2e$action$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$src$2f$actions$2f$friend$2e$action$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$src$2f$actions$2f$ai$2e$action$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["607be4a979876d1de147e5859be475d19de660820e"]),
    "608b8e3c5e2d1e07725287aa26bf15eff03cd8a719": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f28$protected$292f$dashboard$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$src$2f$actions$2f$auth$2e$action$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$src$2f$actions$2f$friend$2e$action$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$src$2f$actions$2f$ai$2e$action$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["608b8e3c5e2d1e07725287aa26bf15eff03cd8a719"]),
    "60db05fee2171b5299bcb41d3928b3ff3198ff681b": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f28$protected$292f$dashboard$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$src$2f$actions$2f$auth$2e$action$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$src$2f$actions$2f$friend$2e$action$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$src$2f$actions$2f$ai$2e$action$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["60db05fee2171b5299bcb41d3928b3ff3198ff681b"]),
    "60dd5c8b1daa63a4e0e736658d190e3917741a5c3d": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f28$protected$292f$dashboard$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$src$2f$actions$2f$auth$2e$action$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$src$2f$actions$2f$friend$2e$action$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$src$2f$actions$2f$ai$2e$action$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["60dd5c8b1daa63a4e0e736658d190e3917741a5c3d"]),
    "60e3d50afefa1bcc6a6ce634bce273f4012a1585b6": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f28$protected$292f$dashboard$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$src$2f$actions$2f$auth$2e$action$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$src$2f$actions$2f$friend$2e$action$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$src$2f$actions$2f$ai$2e$action$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["60e3d50afefa1bcc6a6ce634bce273f4012a1585b6"]),
    "60f5648f98cb3e7abcea16c8fb2dfdbed59b782834": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f28$protected$292f$dashboard$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$src$2f$actions$2f$auth$2e$action$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$src$2f$actions$2f$friend$2e$action$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$src$2f$actions$2f$ai$2e$action$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["60f5648f98cb3e7abcea16c8fb2dfdbed59b782834"]),
    "7095df0f39808bb81a7443e68a7f2492274f7660b0": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f28$protected$292f$dashboard$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$src$2f$actions$2f$auth$2e$action$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$src$2f$actions$2f$friend$2e$action$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$src$2f$actions$2f$ai$2e$action$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["7095df0f39808bb81a7443e68a7f2492274f7660b0"]),
    "70a829aa97efcb1ce5d25cc2ab07599c6328b4104f": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f28$protected$292f$dashboard$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$src$2f$actions$2f$auth$2e$action$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$src$2f$actions$2f$friend$2e$action$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$src$2f$actions$2f$ai$2e$action$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["70a829aa97efcb1ce5d25cc2ab07599c6328b4104f"]),
    "786a5cddf431ffb57c7e3310435586f370d34355f2": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f28$protected$292f$dashboard$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$src$2f$actions$2f$auth$2e$action$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$src$2f$actions$2f$friend$2e$action$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$src$2f$actions$2f$ai$2e$action$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["786a5cddf431ffb57c7e3310435586f370d34355f2"]),
    "7cd3a5e00c5aa3d7589035e8aa10387789473d26e0": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f28$protected$292f$dashboard$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$src$2f$actions$2f$auth$2e$action$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$src$2f$actions$2f$friend$2e$action$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$src$2f$actions$2f$ai$2e$action$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["7cd3a5e00c5aa3d7589035e8aa10387789473d26e0"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f28$protected$292f$dashboard$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$src$2f$actions$2f$auth$2e$action$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$src$2f$actions$2f$friend$2e$action$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$src$2f$actions$2f$ai$2e$action$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i('[project]/.next-internal/server/app/(protected)/dashboard/page/actions.js { ACTIONS_MODULE0 => "[project]/src/actions/auth.action.ts [app-rsc] (ecmascript)", ACTIONS_MODULE1 => "[project]/src/actions/friend.action.ts [app-rsc] (ecmascript)", ACTIONS_MODULE2 => "[project]/src/actions/ai.action.ts [app-rsc] (ecmascript)" } [app-rsc] (server actions loader, ecmascript) <module evaluation>');
var __TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f28$protected$292f$dashboard$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$src$2f$actions$2f$auth$2e$action$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$src$2f$actions$2f$friend$2e$action$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$src$2f$actions$2f$ai$2e$action$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__ = __turbopack_context__.i('[project]/.next-internal/server/app/(protected)/dashboard/page/actions.js { ACTIONS_MODULE0 => "[project]/src/actions/auth.action.ts [app-rsc] (ecmascript)", ACTIONS_MODULE1 => "[project]/src/actions/friend.action.ts [app-rsc] (ecmascript)", ACTIONS_MODULE2 => "[project]/src/actions/ai.action.ts [app-rsc] (ecmascript)" } [app-rsc] (server actions loader, ecmascript) <exports>');
}}),
"[project]/src/app/favicon.ico.mjs { IMAGE => \"[project]/src/app/favicon.ico (static in ecmascript)\" } [app-rsc] (structured image object, ecmascript, Next.js server component)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.n(__turbopack_context__.i("[project]/src/app/favicon.ico.mjs { IMAGE => \"[project]/src/app/favicon.ico (static in ecmascript)\" } [app-rsc] (structured image object, ecmascript)"));
}}),
"[project]/src/app/layout.tsx [app-rsc] (ecmascript, Next.js server component)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.n(__turbopack_context__.i("[project]/src/app/layout.tsx [app-rsc] (ecmascript)"));
}}),
"[project]/src/app/not-found.tsx [app-rsc] (ecmascript, Next.js server component)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.n(__turbopack_context__.i("[project]/src/app/not-found.tsx [app-rsc] (ecmascript)"));
}}),
"[project]/src/app/(protected)/dashboard/layout.tsx [app-rsc] (ecmascript, Next.js server component)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.n(__turbopack_context__.i("[project]/src/app/(protected)/dashboard/layout.tsx [app-rsc] (ecmascript)"));
}}),
"[project]/src/app/(protected)/dashboard/page.tsx (client reference/proxy) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const __TURBOPACK__default__export__ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call the default export of [project]/src/app/(protected)/dashboard/page.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/app/(protected)/dashboard/page.tsx <module evaluation>", "default");
}}),
"[project]/src/app/(protected)/dashboard/page.tsx (client reference/proxy)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const __TURBOPACK__default__export__ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call the default export of [project]/src/app/(protected)/dashboard/page.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/app/(protected)/dashboard/page.tsx", "default");
}}),
"[project]/src/app/(protected)/dashboard/page.tsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f28$protected$292f$dashboard$2f$page$2e$tsx__$28$client__reference$2f$proxy$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/app/(protected)/dashboard/page.tsx (client reference/proxy) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f28$protected$292f$dashboard$2f$page$2e$tsx__$28$client__reference$2f$proxy$29$__ = __turbopack_context__.i("[project]/src/app/(protected)/dashboard/page.tsx (client reference/proxy)");
;
__turbopack_context__.n(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f28$protected$292f$dashboard$2f$page$2e$tsx__$28$client__reference$2f$proxy$29$__);
}}),
"[project]/src/app/(protected)/dashboard/page.tsx [app-rsc] (ecmascript, Next.js server component)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.n(__turbopack_context__.i("[project]/src/app/(protected)/dashboard/page.tsx [app-rsc] (ecmascript)"));
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__42f295e0._.js.map