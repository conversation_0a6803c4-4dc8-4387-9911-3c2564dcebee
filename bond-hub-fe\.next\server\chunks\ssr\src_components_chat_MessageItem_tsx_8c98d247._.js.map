{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/src/components/chat/MessageItem.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useState, useEffect, useRef } from \"react\";\r\nimport { Avatar, AvatarFallback, AvatarImage } from \"@/components/ui/avatar\";\r\nimport Image from \"next/image\";\r\nimport { formatMessageTime } from \"@/utils/dateUtils\";\r\nimport {\r\n  Message,\r\n  Media,\r\n  ReactionType,\r\n  UserInfo,\r\n  MessageType,\r\n  User,\r\n} from \"@/types/base\";\r\nimport MediaGrid from \"./MediaGrid\";\r\nimport ForwardMessageDialog from \"./ForwardMessageDialog\";\r\nimport ReactionPicker from \"./ReactionPicker\";\r\nimport ReactionSummary from \"./ReactionSummary\";\r\nimport AudioVisualizer from \"./AudioVisualizer\";\r\nimport { summarizeText } from \"@/actions/ai.action\";\r\nimport { toast } from \"sonner\";\r\n// import { getUserDisplayName } from \"@/utils/userUtils\";\r\n\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { useAuthStore } from \"@/stores/authStore\";\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuItem,\r\n  DropdownMenuTrigger,\r\n} from \"@/components/ui/dropdown-menu\";\r\nimport { Separator } from \"../ui/separator\";\r\nimport { useChatStore } from \"@/stores/chatStore\";\r\nimport { useConversationsStore } from \"@/stores/conversationsStore\";\r\nimport {\r\n  Copy,\r\n  Download,\r\n  Reply,\r\n  Trash,\r\n  MoreHorizontal,\r\n  RotateCcw,\r\n  Pin,\r\n  Info,\r\n  FileText,\r\n  File,\r\n  Mail,\r\n  Video,\r\n  Image as ImageIcon,\r\n  Forward,\r\n  CornerUpRight,\r\n  AtSign,\r\n  Music,\r\n  FileDigit,\r\n} from \"lucide-react\";\r\nimport { getUserInitials } from \"@/utils/userUtils\";\r\nimport {\r\n  Dialog,\r\n  DialogContent,\r\n  DialogDescription,\r\n  DialogHeader,\r\n  DialogTitle,\r\n} from \"@/components/ui/dialog\";\r\n\r\ninterface MediaItemProps {\r\n  media: Media;\r\n  onClick?: () => void;\r\n}\r\n\r\n// Component to render different types of media\r\nfunction MediaItem({ media, onClick }: MediaItemProps) {\r\n  const handleDownload = (e?: React.MouseEvent) => {\r\n    if (e) {\r\n      e.preventDefault();\r\n      e.stopPropagation();\r\n    }\r\n    // Tạo một thẻ a ẩn để tải file\r\n    const link = document.createElement(\"a\");\r\n    link.href = media.url;\r\n    link.download = media.fileName; // Đặt tên file khi tải về\r\n    document.body.appendChild(link);\r\n    link.click();\r\n    document.body.removeChild(link);\r\n\r\n    // Ngăn sự kiện click lan ra ngoài\r\n    return false;\r\n  };\r\n\r\n  const getFileIcon = () => {\r\n    const { type, metadata } = media;\r\n    if (type === \"IMAGE\") return <ImageIcon className=\"h-5 w-5\" />;\r\n    if (type === \"VIDEO\") return <Video className=\"h-5 w-5\" />;\r\n    if (type === \"AUDIO\") return <Music className=\"h-5 w-5\" />;\r\n\r\n    // For documents, show specific icons based on extension\r\n    const ext = metadata.extension.toLowerCase();\r\n    if (ext === \"pdf\") return <FileText className=\"h-5 w-5\" />;\r\n    return <File className=\"h-5 w-5\" />;\r\n  };\r\n\r\n  const formatFileSize = (size: string) => {\r\n    return size;\r\n  };\r\n\r\n  switch (media.type) {\r\n    case \"IMAGE\":\r\n      return (\r\n        <div\r\n          className=\"relative rounded-lg overflow-hidden max-w-full isolate cursor-pointer hover:opacity-90\"\r\n          onClick={onClick}\r\n        >\r\n          <Image\r\n            src={media.url}\r\n            alt={media.fileName}\r\n            className=\"w-full rounded-lg object-cover max-h-[300px]\"\r\n            width={300}\r\n            height={200}\r\n            unoptimized\r\n          />\r\n          <div className=\"absolute inset-0 bg-black/0 hover:bg-black/10 transition-all duration-200\"></div>\r\n\r\n          <button\r\n            className=\"absolute bottom-2 right-2 bg-white/80 p-1 rounded-full shadow-sm hover:bg-white/100 transition-opacity opacity-0 hover:opacity-100\"\r\n            onClick={handleDownload}\r\n          >\r\n            <Download className=\"h-4 w-4\" />\r\n          </button>\r\n        </div>\r\n      );\r\n\r\n    case \"VIDEO\":\r\n      return (\r\n        <div\r\n          className=\"relative rounded-lg overflow-hidden max-w-full isolate cursor-pointer hover:opacity-90\"\r\n          onClick={onClick}\r\n        >\r\n          <video\r\n            src={media.url}\r\n            className=\"w-full rounded-lg max-h-[300px]\"\r\n            style={{ maxWidth: \"100%\" }}\r\n          />\r\n          <div className=\"absolute top-2 left-2 bg-black/50 text-white text-xs px-1.5 py-0.5 rounded z-10\">\r\n            Video\r\n          </div>\r\n          <button\r\n            className=\"absolute bottom-2 right-2 bg-white/80 p-1 rounded-full shadow-sm hover:bg-white/100 transition-opacity opacity-0 hover:opacity-100 z-10\"\r\n            onClick={handleDownload}\r\n          >\r\n            <Download className=\"h-4 w-4\" />\r\n          </button>\r\n        </div>\r\n      );\r\n\r\n    case \"AUDIO\":\r\n      return (\r\n        <div\r\n          className=\"flex flex-col p-2 bg-gray-100 rounded-lg overflow-hidden hover:bg-gray-200 transition-colors isolate cursor-pointer\"\r\n          onClick={onClick}\r\n        >\r\n          <div className=\"flex items-center mb-1\">\r\n            <div className=\"mr-2 p-1.5 bg-white rounded-md flex-shrink-0 shadow-sm\">\r\n              <Music className=\"h-4 w-4 text-blue-500\" />\r\n            </div>\r\n            <p className=\"text-sm font-medium truncate\">{media.fileName}</p>\r\n          </div>\r\n          <div className=\"w-full\">\r\n            <AudioVisualizer\r\n              url={media.url}\r\n              fileName={media.fileName}\r\n              compact={true}\r\n              onDownload={() => handleDownload()}\r\n            />\r\n          </div>\r\n        </div>\r\n      );\r\n\r\n    // For documents and other file types\r\n    default:\r\n      return (\r\n        <div\r\n          className=\"flex items-center p-2 bg-gray-100 rounded-lg overflow-hidden hover:bg-gray-200 transition-colors isolate cursor-pointer\"\r\n          onClick={onClick}\r\n        >\r\n          <div className=\"mr-3 p-2 bg-white rounded-md flex-shrink-0 shadow-sm\">\r\n            {getFileIcon()}\r\n          </div>\r\n          <div className=\"flex-1 min-w-0 overflow-hidden\">\r\n            <p className=\"text-sm font-medium truncate\">{media.fileName}</p>\r\n            <p className=\"text-xs text-gray-500 truncate\">\r\n              {formatFileSize(media.metadata.sizeFormatted)}\r\n            </p>\r\n          </div>\r\n          <button\r\n            className=\"ml-2 p-1.5 bg-white rounded-full flex-shrink-0 shadow-sm hover:bg-gray-50 transition-opacity opacity-0 hover:opacity-100\"\r\n            onClick={handleDownload}\r\n          >\r\n            <Download className=\"h-4 w-4\" />\r\n          </button>\r\n        </div>\r\n      );\r\n  }\r\n}\r\n\r\n// Extend the Message type to include senderName which might be present in API responses\r\ninterface ExtendedMessage extends Message {\r\n  senderName?: string;\r\n}\r\n\r\n// Helper function to get the sender name from a message\r\nfunction getSenderName(\r\n  message: Message | ExtendedMessage,\r\n  userInfo?: UserInfo,\r\n  userInfoFromConversations?: UserInfo | null,\r\n): string {\r\n  // Try to get the best available name with priority order\r\n  const senderName =\r\n    userInfoFromConversations?.fullName ||\r\n    message.sender?.userInfo?.fullName ||\r\n    userInfo?.fullName ||\r\n    (message as ExtendedMessage).senderName ||\r\n    (message.senderId\r\n      ? `Người dùng ${message.senderId.slice(-4)}`\r\n      : \"Thành viên nhóm\");\r\n\r\n  // Don't show \"Unknown\" - use a more user-friendly fallback\r\n  return senderName === \"Unknown\"\r\n    ? message.senderId\r\n      ? `Người dùng ${message.senderId.slice(-4)}`\r\n      : \"Thành viên nhóm\"\r\n    : senderName;\r\n}\r\n\r\ninterface MessageItemProps {\r\n  message: Message | ExtendedMessage;\r\n  isCurrentUser: boolean;\r\n  showAvatar?: boolean;\r\n  onReply?: (message: Message) => void;\r\n  onMessageClick?: (message: Message) => void;\r\n  highlight?: string;\r\n  userInfo?: UserInfo; // Thêm userInfo cho người gửi\r\n  isGroupMessage?: boolean; // Thêm flag để xác định tin nhắn nhóm\r\n}\r\n\r\n// Summary dialog component\r\ninterface SummaryDialogProps {\r\n  isOpen: boolean;\r\n  onClose: () => void;\r\n  originalText: string;\r\n  summary: string;\r\n  isLoading: boolean;\r\n}\r\n\r\nfunction SummaryDialog({\r\n  isOpen,\r\n  onClose,\r\n  originalText,\r\n  summary,\r\n  isLoading,\r\n}: SummaryDialogProps) {\r\n  return (\r\n    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>\r\n      <DialogContent className=\"sm:max-w-md\">\r\n        <DialogHeader>\r\n          <DialogTitle className=\"flex items-center\">\r\n            <FileDigit className=\"h-5 w-5 mr-2 text-blue-500\" />\r\n            Tóm tắt nội dung\r\n          </DialogTitle>\r\n          <DialogDescription>\r\n            Nội dung được tóm tắt bằng trí tuệ nhân tạo\r\n          </DialogDescription>\r\n        </DialogHeader>\r\n        <div className=\"space-y-4 max-h-[60vh] overflow-auto\">\r\n          {isLoading ? (\r\n            <div className=\"py-8 flex justify-center items-center\">\r\n              <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500\"></div>\r\n            </div>\r\n          ) : (\r\n            <>\r\n              <div className=\"bg-blue-50 p-3 rounded-md text-sm border border-blue-100\">\r\n                <h4 className=\"font-medium text-blue-700 mb-1\">\r\n                  Nội dung tóm tắt:\r\n                </h4>\r\n                <p className=\"text-blue-600 whitespace-pre-wrap\">{summary}</p>\r\n              </div>\r\n              <div className=\"bg-gray-100 p-3 rounded-md text-sm\">\r\n                <h4 className=\"font-medium text-gray-700 mb-1\">\r\n                  Nội dung gốc:\r\n                </h4>\r\n                <p className=\"text-gray-600 whitespace-pre-wrap\">\r\n                  {originalText}\r\n                </p>\r\n              </div>\r\n            </>\r\n          )}\r\n        </div>\r\n        <div className=\"flex justify-between mt-4\">\r\n          <Button variant=\"outline\" onClick={onClose}>\r\n            Đóng\r\n          </Button>\r\n          {!isLoading && (\r\n            <Button\r\n              variant=\"outline\"\r\n              onClick={() => {\r\n                navigator.clipboard.writeText(summary);\r\n                toast.success(\"Đã sao chép vào clipboard\");\r\n              }}\r\n            >\r\n              <Copy className=\"h-4 w-4 mr-2\" />\r\n              Sao chép tóm tắt\r\n            </Button>\r\n          )}\r\n        </div>\r\n      </DialogContent>\r\n    </Dialog>\r\n  );\r\n}\r\n\r\n// Define minimum character requirements\r\nconst MIN_SUMMARIZE_LENGTH = 50;\r\n\r\n// Helper function to get message content text\r\nconst getMessageText = (message: Message | ExtendedMessage): string => {\r\n  if (!message.content) return \"\";\r\n  return message.content.text || \"\";\r\n};\r\n\r\n// Helper function to get message content media\r\nconst getMessageMedia = (message: Message | ExtendedMessage): Media[] => {\r\n  if (!message.content) return [];\r\n  return message.content.media || [];\r\n};\r\n\r\nexport default function MessageItem({\r\n  message,\r\n  isCurrentUser,\r\n  showAvatar = true,\r\n  onReply,\r\n  onMessageClick,\r\n  highlight,\r\n  userInfo,\r\n  isGroupMessage,\r\n}: MessageItemProps) {\r\n  // Auto-detect if this is a group message if not explicitly provided\r\n  const isGroup =\r\n    isGroupMessage ||\r\n    message.messageType === MessageType.GROUP ||\r\n    !!message.groupId;\r\n  const [isHovered, setIsHovered] = useState(false);\r\n  const [isForwardDialogOpen, setIsForwardDialogOpen] = useState(false);\r\n  const [isSummarizing, setIsSummarizing] = useState(false);\r\n  const [summaryDialogOpen, setSummaryDialogOpen] = useState(false);\r\n  const [summaryText, setSummaryText] = useState(\"\");\r\n  const formattedTime = formatMessageTime(message.createdAt);\r\n  const currentUser = useAuthStore((state) => state.user);\r\n  // Get chat store for message operations\r\n  const chatStore = useChatStore();\r\n\r\n  // Get user info from conversations store\r\n  const conversationsStore = useConversationsStore();\r\n  const userInfoFromConversations = message.senderId\r\n    ? conversationsStore.getUserInfoFromConversations(message.senderId)\r\n    : null;\r\n\r\n  // Store chatStore in a ref to prevent re-renders\r\n  const chatStoreRef = useRef(chatStore);\r\n  useEffect(() => {\r\n    chatStoreRef.current = chatStore;\r\n  }, [chatStore]);\r\n\r\n  // Ref to track if we've already attempted to mark this message as read\r\n  const markAsReadAttemptedRef = useRef(new Set<string>());\r\n\r\n  // Biến để tái sử dụng về sau\r\n  const currentUserId = currentUser?.id || \"\";\r\n  const isRead =\r\n    Array.isArray(message.readBy) && message.readBy.includes(currentUserId);\r\n  const isSent = message.id && !message.id.startsWith(\"temp-\");\r\n\r\n  // Use a ref to track when we last refreshed group data\r\n  const lastGroupRefreshRef = useRef<Record<string, number>>({});\r\n\r\n  // Check if we need to fetch user information for this message\r\n  useEffect(() => {\r\n    // Only for group messages where sender info is missing or incomplete\r\n    if (\r\n      isGroup &&\r\n      !isCurrentUser &&\r\n      message.senderId &&\r\n      message.groupId &&\r\n      (!message.sender?.userInfo ||\r\n        !message.sender.userInfo.fullName ||\r\n        message.sender.userInfo.fullName === \"Unknown\")\r\n    ) {\r\n      // Check if we've already refreshed this group recently (within 5 seconds)\r\n      const lastRefreshTime = lastGroupRefreshRef.current[message.groupId];\r\n      const now = Date.now();\r\n\r\n      if (lastRefreshTime && now - lastRefreshTime < 5000) {\r\n        console.log(\r\n          `[MessageItem] Skipping refresh for group ${message.groupId}, last refresh was ${now - lastRefreshTime}ms ago`,\r\n        );\r\n        return;\r\n      }\r\n\r\n      console.log(\r\n        `[MessageItem] Message ${message.id} has missing sender info, triggering group refresh`,\r\n      );\r\n\r\n      // Record this refresh\r\n      lastGroupRefreshRef.current[message.groupId] = now;\r\n\r\n      // Clean up old entries\r\n      Object.keys(lastGroupRefreshRef.current).forEach((groupId) => {\r\n        if (now - lastGroupRefreshRef.current[groupId] > 30000) {\r\n          // 30 seconds\r\n          delete lastGroupRefreshRef.current[groupId];\r\n        }\r\n      });\r\n\r\n      // Use setTimeout to break potential update cycles\r\n      setTimeout(() => {\r\n        try {\r\n          // Get fresh references to avoid stale closures\r\n          const currentChatStore = useChatStore.getState();\r\n          const conversationsStore = useConversationsStore.getState();\r\n\r\n          // First refresh the group member cache to ensure we have latest user info\r\n          conversationsStore.refreshGroupMemberCache(message.groupId);\r\n\r\n          // Trigger a refresh of the selected group to get updated member information\r\n          if (\r\n            currentChatStore.refreshSelectedGroup &&\r\n            currentChatStore.selectedGroup?.id === message.groupId\r\n          ) {\r\n            console.log(\r\n              `[MessageItem] Refreshing group data for ${message.groupId}`,\r\n            );\r\n            currentChatStore.refreshSelectedGroup();\r\n          } else {\r\n            // If not the selected group, force an update of the conversations list\r\n            console.log(\r\n              `[MessageItem] Forcing update of conversations to get group ${message.groupId}`,\r\n            );\r\n            conversationsStore.forceUpdate();\r\n          }\r\n        } catch (error) {\r\n          console.error(`[MessageItem] Error refreshing group data:`, error);\r\n        }\r\n      }, 1000); // Increased delay to prevent rapid updates\r\n    }\r\n  }, [\r\n    isGroup,\r\n    isCurrentUser,\r\n    message.senderId,\r\n    message.sender?.userInfo?.fullName, // More specific dependency\r\n    message.id,\r\n    message.groupId,\r\n    // Removed chatStore from dependencies to prevent infinite loops\r\n  ]);\r\n\r\n  // Get current user's reaction\r\n  const getUserReaction = () => {\r\n    return message.reactions?.find((r) => r.userId === currentUser?.id);\r\n  };\r\n\r\n  const handleCopyMessage = () => {\r\n    if (message.content.text) {\r\n      navigator.clipboard.writeText(message.content.text);\r\n    }\r\n  };\r\n\r\n  const handleDeleteMessage = async () => {\r\n    try {\r\n      await chatStore.deleteMessageById(message.id);\r\n    } catch (error) {\r\n      // Silent error handling\r\n    }\r\n  };\r\n\r\n  const handleMarkAsUnread = async () => {\r\n    try {\r\n      if (message.id) {\r\n        await chatStore.markMessageAsUnreadById(message.id);\r\n      }\r\n    } catch (error) {\r\n      // Silent error handling\r\n    }\r\n  };\r\n\r\n  const handleRecallMessage = async () => {\r\n    try {\r\n      await chatStore.recallMessageById(message.id);\r\n      // Force a re-render\r\n      setIsHovered(false);\r\n    } catch (error) {\r\n      // Silent error handling\r\n    }\r\n  };\r\n\r\n  const handleReply = () => {\r\n    if (onReply) {\r\n      onReply(message);\r\n    }\r\n  };\r\n\r\n  const handleMessageClick = () => {\r\n    if (onMessageClick) {\r\n      onMessageClick(message);\r\n    }\r\n  };\r\n\r\n  const handleReaction = async (reactionType: ReactionType) => {\r\n    try {\r\n      await chatStore.addReactionToMessageById(message.id, reactionType);\r\n    } catch (error) {\r\n      // Silent error handling\r\n    }\r\n  };\r\n\r\n  const handleRemoveReaction = async () => {\r\n    try {\r\n      await chatStore.removeReactionFromMessageById(message.id);\r\n    } catch (error) {\r\n      // Silent error handling\r\n    }\r\n  };\r\n\r\n  const handleForwardMessage = () => {\r\n    setIsForwardDialogOpen(true);\r\n  };\r\n\r\n  // Function to process tabs in text\r\n  const processTabsInText = (text: string): React.ReactNode => {\r\n    return text.split(\"\\t\").map((segment, i) => (\r\n      <React.Fragment key={i}>\r\n        {i > 0 && <span className=\"inline-block w-8\"></span>}\r\n        {segment}\r\n      </React.Fragment>\r\n    ));\r\n  };\r\n\r\n  // Function to highlight search text in message content\r\n  const renderHighlightedText = (text: string, searchText: string) => {\r\n    if (!searchText || !text) return text;\r\n\r\n    try {\r\n      // Use case-insensitive regex to find matches\r\n      const regex = new RegExp(\r\n        `(${searchText.replace(/[.*+?^${}()|[\\]\\\\]/g, \"\\\\$&\")})`,\r\n        \"gi\",\r\n      );\r\n\r\n      // First handle line breaks by splitting the text into lines\r\n      const lines = text.split(\"\\n\");\r\n\r\n      return lines.map((line, lineIndex) => {\r\n        // Process tabs in the line\r\n        const lineWithTabs = line.includes(\"\\t\")\r\n          ? processTabsInText(line)\r\n          : line;\r\n\r\n        // If we've processed tabs, we can't apply regex highlighting\r\n        if (line.includes(\"\\t\")) {\r\n          return (\r\n            <span key={lineIndex}>\r\n              {lineWithTabs}\r\n              {lineIndex < lines.length - 1 && <br />}\r\n            </span>\r\n          );\r\n        }\r\n\r\n        // For lines without tabs, apply the highlighting\r\n        const parts = line.split(regex);\r\n\r\n        const highlightedLine = parts.map((part, i) => {\r\n          // Check if this part matches the search term\r\n          if (part.toLowerCase() === searchText.toLowerCase()) {\r\n            return (\r\n              <span\r\n                key={`${lineIndex}-${i}`}\r\n                className=\"bg-yellow-200 px-0.5 rounded\"\r\n              >\r\n                {part}\r\n              </span>\r\n            );\r\n          }\r\n          return part;\r\n        });\r\n\r\n        // Return the line with a line break if it's not the last line\r\n        return (\r\n          <span key={lineIndex}>\r\n            {highlightedLine}\r\n            {lineIndex < lines.length - 1 && <br />}\r\n          </span>\r\n        );\r\n      });\r\n    } catch {\r\n      // If any error in regex, return plain text with line breaks and tabs preserved\r\n      return text.split(\"\\n\").map((line, index) => (\r\n        <span key={index}>\r\n          {line.includes(\"\\t\") ? processTabsInText(line) : line}\r\n          {index < text.split(\"\\n\").length - 1 && <br />}\r\n        </span>\r\n      ));\r\n    }\r\n  };\r\n\r\n  const isDeletedForSelf = message.deletedBy.includes(currentUser?.id || \"\");\r\n\r\n  const handleSummarizeMessage = async () => {\r\n    // Validate message has content\r\n    if (!message.content.text || message.content.text.trim().length === 0) {\r\n      toast.error(\"Không thể tóm tắt\", {\r\n        description: \"Tin nhắn không có nội dung văn bản để tóm tắt\",\r\n      });\r\n      return;\r\n    }\r\n\r\n    // Check message is long enough to summarize\r\n    if (message.content.text.trim().length < MIN_SUMMARIZE_LENGTH) {\r\n      toast.error(\"Không thể tóm tắt\", {\r\n        description: `Tin nhắn cần có ít nhất ${MIN_SUMMARIZE_LENGTH} ký tự để tóm tắt`,\r\n      });\r\n      return;\r\n    }\r\n\r\n    setIsSummarizing(true);\r\n    setSummaryDialogOpen(true);\r\n    setSummaryText(\"\");\r\n\r\n    try {\r\n      const result = await summarizeText(message.content.text, 150);\r\n\r\n      if (result.success && result.summary) {\r\n        setSummaryText(result.summary);\r\n      } else {\r\n        toast.error(\"Không thể tóm tắt\", {\r\n          description: result.error || \"Đã xảy ra lỗi khi tóm tắt nội dung\",\r\n        });\r\n        setSummaryDialogOpen(false);\r\n      }\r\n    } catch {\r\n      toast.error(\"Không thể tóm tắt\", {\r\n        description: \"Đã xảy ra lỗi khi kết nối đến dịch vụ AI\",\r\n      });\r\n      setSummaryDialogOpen(false);\r\n    } finally {\r\n      setIsSummarizing(false);\r\n    }\r\n  };\r\n\r\n  const messageText = getMessageText(message);\r\n  const messageMedia = getMessageMedia(message);\r\n\r\n  if (isDeletedForSelf) {\r\n    return null;\r\n  }\r\n\r\n  return (\r\n    <>\r\n      <ForwardMessageDialog\r\n        message={message}\r\n        isOpen={isForwardDialogOpen}\r\n        onOpenChange={setIsForwardDialogOpen}\r\n      />\r\n\r\n      <SummaryDialog\r\n        isOpen={summaryDialogOpen}\r\n        onClose={() => setSummaryDialogOpen(false)}\r\n        originalText={messageText}\r\n        summary={summaryText}\r\n        isLoading={isSummarizing}\r\n      />\r\n\r\n      <div\r\n        className={`flex group ${isCurrentUser ? \"justify-end\" : \"justify-start\"} mb-2 relative`}\r\n      >\r\n        {!isCurrentUser && showAvatar && (\r\n          <div className=\"mr-2 flex-shrink-0\">\r\n            <Avatar className=\"h-8 w-8\">\r\n              <AvatarImage\r\n                className=\"select-none relative object-cover\"\r\n                src={\r\n                  // Ưu tiên thông tin từ conversations store\r\n                  userInfoFromConversations?.profilePictureUrl ||\r\n                  // Sau đó đến thông tin từ sender trong message\r\n                  message.sender?.userInfo?.profilePictureUrl ||\r\n                  // Cuối cùng đến userInfo được truyền vào từ props\r\n                  userInfo?.profilePictureUrl ||\r\n                  undefined\r\n                }\r\n              />\r\n              <AvatarFallback>\r\n                {getUserInitials({\r\n                  userInfo:\r\n                    // Ưu tiên thông tin từ conversations store\r\n                    userInfoFromConversations ||\r\n                    // Sau đó đến thông tin từ sender trong message\r\n                    message.sender?.userInfo ||\r\n                    // Cuối cùng đến userInfo được truyền vào từ props\r\n                    userInfo ||\r\n                    ({\r\n                      fullName: isGroup ? \"Thành viên\" : \"Người dùng\",\r\n                    } as UserInfo),\r\n                } as User)}\r\n              </AvatarFallback>\r\n            </Avatar>\r\n          </div>\r\n        )}\r\n\r\n        <div\r\n          className={`max-w-[70%] ${!isCurrentUser && !showAvatar ? \"ml-10\" : \"\"} relative overflow-visible group before:content-[''] before:absolute before:top-[-10px] before:h-[calc(100%+20px)] ${isCurrentUser ? \"before:right-full before:w-12\" : 'after:content-[\"\"] after:absolute after:top-[-10px] after:h-[calc(100%+20px)] after:left-full after:w-12'}`}\r\n          onMouseEnter={() => setIsHovered(true)}\r\n          onMouseLeave={() => setIsHovered(false)}\r\n        >\r\n          {/* Hover action buttons */}\r\n          {!message.recalled && (\r\n            <div\r\n              className={`absolute ${isCurrentUser ? \"right-full mr-1\" : \"left-full ml-1\"} top-1/2 -translate-y-1/2 z-30 transition-opacity duration-200 ease-in-out flex ${isCurrentUser ? \"flex-row\" : \"flex-row-reverse\"} gap-0.5 before:content-[''] before:absolute before:top-[-10px] before:bottom-[-10px] ${isCurrentUser ? \"before:right-[-5px] before:left-[-5px]\" : \"before:left-[-5px] before:right-[-5px]\"} before:w-2 before:z-20`}\r\n              style={{\r\n                opacity: isHovered ? 1 : 0,\r\n                pointerEvents: isHovered ? \"auto\" : \"none\",\r\n              }}\r\n              onMouseEnter={() => setIsHovered(true)}\r\n            >\r\n              {/* Reply button - only for messages not from current user */}\r\n              {!isCurrentUser && (\r\n                <Button\r\n                  variant=\"ghost\"\r\n                  size=\"icon\"\r\n                  className=\"h-7 w-7 rounded-full bg-white shadow-sm hover:bg-gray-100\"\r\n                  onClick={handleReply}\r\n                  disabled\r\n                >\r\n                  <AtSign className=\"h-4 w-4 text-gray-600\" />\r\n                </Button>\r\n              )}\r\n\r\n              {/* Forward button */}\r\n              <Button\r\n                variant=\"ghost\"\r\n                size=\"icon\"\r\n                className=\"h-7 w-7 rounded-full bg-white shadow-sm hover:bg-gray-100\"\r\n                onClick={handleForwardMessage}\r\n              >\r\n                <Forward className=\"h-4 w-4 text-gray-600\" />\r\n              </Button>\r\n\r\n              {/* Empty div to maintain spacing */}\r\n              <div></div>\r\n\r\n              {/* Options button */}\r\n              <DropdownMenu>\r\n                <DropdownMenuTrigger asChild>\r\n                  <Button\r\n                    variant=\"ghost\"\r\n                    size=\"icon\"\r\n                    className=\"h-7 w-7 rounded-full bg-white shadow-sm hover:bg-gray-100\"\r\n                  >\r\n                    <MoreHorizontal className=\"h-4 w-4 text-gray-600\" />\r\n                  </Button>\r\n                </DropdownMenuTrigger>\r\n                <DropdownMenuContent align=\"end\" className=\"\">\r\n                  {!message.recalled && (\r\n                    <>\r\n                      <DropdownMenuItem onClick={handleCopyMessage}>\r\n                        <Copy className=\"h-4 w-4 mr-2\" />\r\n                        <span>Sao chép</span>\r\n                      </DropdownMenuItem>\r\n                      {message.content.text &&\r\n                        message.content.text.trim().length > 0 && (\r\n                          <DropdownMenuItem\r\n                            onClick={handleSummarizeMessage}\r\n                            disabled={\r\n                              isSummarizing ||\r\n                              message.content.text.trim().length <\r\n                                MIN_SUMMARIZE_LENGTH\r\n                            }\r\n                            title={\r\n                              message.content.text.trim().length <\r\n                              MIN_SUMMARIZE_LENGTH\r\n                                ? `Tin nhắn cần có ít nhất ${MIN_SUMMARIZE_LENGTH} ký tự để tóm tắt`\r\n                                : \"Tóm tắt nội dung bằng AI\"\r\n                            }\r\n                          >\r\n                            <FileDigit className=\"h-4 w-4 mr-2\" />\r\n                            <span>\r\n                              {isSummarizing\r\n                                ? \"Đang tóm tắt...\"\r\n                                : message.content.text.trim().length <\r\n                                    MIN_SUMMARIZE_LENGTH\r\n                                  ? `Cần ít nhất ${MIN_SUMMARIZE_LENGTH} ký tự`\r\n                                  : \"Tóm tắt nội dung\"}\r\n                            </span>\r\n                          </DropdownMenuItem>\r\n                        )}\r\n                      <Separator />\r\n                    </>\r\n                  )}\r\n                  {!isCurrentUser && !message.recalled && (\r\n                    <DropdownMenuItem disabled onClick={handleReply}>\r\n                      <Reply className=\"h-4 w-4 mr-2\" />\r\n                      <span>Trả lời</span>\r\n                    </DropdownMenuItem>\r\n                  )}\r\n                  {!message.recalled && (\r\n                    <DropdownMenuItem onClick={handleForwardMessage}>\r\n                      <Forward className=\"h-4 w-4 mr-2\" />\r\n                      <span>Chuyển tiếp</span>\r\n                    </DropdownMenuItem>\r\n                  )}\r\n                  <DropdownMenuItem disabled>\r\n                    <Pin className=\"h-4 w-4 mr-2\" />\r\n                    <span>Ghim tin nhắn</span>\r\n                  </DropdownMenuItem>\r\n                  <DropdownMenuItem disabled>\r\n                    <Info className=\"h-4 w-4 mr-2\" />\r\n                    <span>Xem chi tiết</span>\r\n                  </DropdownMenuItem>\r\n                  {!message.recalled && (\r\n                    <DropdownMenuItem disabled onClick={handleMarkAsUnread}>\r\n                      <Mail className=\"h-4 w-4 mr-2\" />\r\n                      <span>Đánh dấu chưa đọc</span>\r\n                    </DropdownMenuItem>\r\n                  )}\r\n                  <Separator />\r\n                  {isCurrentUser && !message.recalled && (\r\n                    <>\r\n                      <DropdownMenuItem\r\n                        className=\"text-destructive\"\r\n                        onClick={handleRecallMessage}\r\n                      >\r\n                        <RotateCcw className=\"h-4 w-4 mr-2\" />\r\n                        <span>Thu hồi</span>\r\n                      </DropdownMenuItem>\r\n                    </>\r\n                  )}\r\n                  <DropdownMenuItem\r\n                    onClick={handleDeleteMessage}\r\n                    className=\"text-red-500 focus:text-red-500\"\r\n                  >\r\n                    <Trash className=\"h-4 w-4 mr-2\" />\r\n                    <span>Xóa chỉ ở phía tôi</span>\r\n                  </DropdownMenuItem>\r\n                </DropdownMenuContent>\r\n              </DropdownMenu>\r\n            </div>\r\n          )}\r\n\r\n          {/* Display sender name for group messages */}\r\n          {isGroup && !isCurrentUser && !message.recalled && (\r\n            <div className=\"text-xs font-medium text-blue-600 mb-1\">\r\n              {getSenderName(message, userInfo, userInfoFromConversations)}\r\n            </div>\r\n          )}\r\n\r\n          {/* If message is a reply to another message */}\r\n          {message.repliedTo && (\r\n            <div\r\n              className={`text-xs mb-1 px-2 py-1 rounded-lg ${isCurrentUser ? \"bg-blue-100 text-blue-800\" : \"bg-gray-100 text-gray-800\"}`}\r\n            >\r\n              <div className=\"flex items-center gap-1\">\r\n                <Reply className=\"h-3 w-3\" />\r\n                <span className=\"font-medium\">Trả lời</span>\r\n              </div>\r\n              <p className=\"truncate\">Tin nhắn gốc đã bị xóa hoặc thu hồi</p>\r\n            </div>\r\n          )}\r\n\r\n          {/* Tin nhắn văn bản */}\r\n          {(message.recalled || messageText) && (\r\n            <div\r\n              className={`rounded-2xl px-3 py-2 break-words w-fit overflow-hidden ${\r\n                isCurrentUser\r\n                  ? message.recalled\r\n                    ? \"bg-gray-100 text-gray-500 italic\"\r\n                    : \"bg-blue-500 text-white ml-auto\"\r\n                  : message.recalled\r\n                    ? \"bg-gray-100 text-gray-500 italic\"\r\n                    : \"bg-gray-200 text-gray-800\"\r\n              } ${!message.recalled ? \"cursor-pointer hover:opacity-90\" : \"\"} ${\r\n                messageMedia.length ||\r\n                message.content?.image ||\r\n                message.content?.video\r\n                  ? \"mb-2\"\r\n                  : \"\"\r\n              }`}\r\n              onClick={!message.recalled ? handleMessageClick : undefined}\r\n            >\r\n              {message.recalled ? (\r\n                <div className=\"flex items-center italic text-sm text-gray-500\">\r\n                  <RotateCcw className=\"h-3 w-3 mr-1\" />\r\n                  Tin nhắn đã bị thu hồi\r\n                </div>\r\n              ) : (\r\n                <div className=\"text-sm\">\r\n                  {message.forwardedFrom && (\r\n                    <div className=\"flex items-center text-xs mb-1 opacity-70\">\r\n                      <CornerUpRight className=\"h-3 w-3 mr-1\" />\r\n                      Tin nhắn đã được chuyển tiếp\r\n                    </div>\r\n                  )}\r\n                  {highlight\r\n                    ? renderHighlightedText(messageText, highlight || \"\")\r\n                    : messageText.split(\"\\n\").map((line, index) => (\r\n                        <span key={index}>\r\n                          {line.includes(\"\\t\") ? processTabsInText(line) : line}\r\n                          {index < messageText.split(\"\\n\").length - 1 && <br />}\r\n                        </span>\r\n                      ))}\r\n                </div>\r\n              )}\r\n            </div>\r\n          )}\r\n\r\n          {/* Media content - outside of message bubble */}\r\n          {!message.recalled && (\r\n            <>\r\n              {/* Forwarded indicator for media-only messages */}\r\n              {message.forwardedFrom && !message.content.text && (\r\n                <div\r\n                  className={`text-xs mb-1 px-2 py-1 rounded-lg ${isCurrentUser ? \"bg-blue-100 text-blue-800\" : \"bg-gray-100 text-gray-800\"}`}\r\n                >\r\n                  <div className=\"flex items-center gap-1\">\r\n                    <CornerUpRight className=\"h-3 w-3\" />\r\n                    <span className=\"font-medium\">\r\n                      Tin nhắn đã được chuyển tiếp\r\n                    </span>\r\n                  </div>\r\n                </div>\r\n              )}\r\n              {/* Legacy image support */}\r\n              {message.content.image && (\r\n                <div\r\n                  className=\"mt-1 relative rounded-lg overflow-hidden max-w-full isolate cursor-pointer hover:opacity-90\"\r\n                  onClick={handleMessageClick}\r\n                >\r\n                  <Image\r\n                    src={message.content.image}\r\n                    alt=\"Image\"\r\n                    className=\"w-full rounded-lg object-cover max-h-[300px]\"\r\n                    width={300}\r\n                    height={200}\r\n                  />\r\n                  <button\r\n                    className=\"absolute bottom-2 right-2 bg-white/80 p-1 rounded-full shadow-sm hover:bg-white/100 transition-opacity opacity-0 hover:opacity-100\"\r\n                    onClick={(e) => {\r\n                      e.preventDefault();\r\n                      e.stopPropagation();\r\n                      const link = document.createElement(\"a\");\r\n                      link.href = message.content.image || \"\";\r\n                      link.download = \"image.jpg\"; // Default name\r\n                      document.body.appendChild(link);\r\n                      link.click();\r\n                      document.body.removeChild(link);\r\n                    }}\r\n                  >\r\n                    <Download className=\"h-4 w-4\" />\r\n                  </button>\r\n                </div>\r\n              )}\r\n\r\n              {/* Legacy video support */}\r\n              {message.content.video && (\r\n                <div\r\n                  className=\"mt-1 relative rounded-lg overflow-hidden max-w-full isolate cursor-pointer hover:opacity-90\"\r\n                  onClick={handleMessageClick}\r\n                >\r\n                  <video\r\n                    src={message.content.video}\r\n                    controls\r\n                    className=\"w-full rounded-lg max-h-[300px]\"\r\n                    style={{ maxWidth: \"100%\" }}\r\n                  />\r\n                  <button\r\n                    className=\"absolute bottom-2 right-2 bg-white/80 p-1 rounded-full shadow-sm hover:bg-white/100 transition-opacity opacity-0 hover:opacity-100 z-10\"\r\n                    onClick={(e) => {\r\n                      e.preventDefault();\r\n                      e.stopPropagation();\r\n                      const link = document.createElement(\"a\");\r\n                      link.href = message.content.video || \"\";\r\n                      link.download = \"video.mp4\"; // Default name\r\n                      document.body.appendChild(link);\r\n                      link.click();\r\n                      document.body.removeChild(link);\r\n                    }}\r\n                  >\r\n                    <Download className=\"h-4 w-4\" />\r\n                  </button>\r\n                </div>\r\n              )}\r\n\r\n              {/* New media array support */}\r\n              {message.content.media && message.content.media.length > 0 && (\r\n                <div\r\n                  className=\"mt-2 w-full overflow-hidden cursor-pointer hover:opacity-90\"\r\n                  onClick={handleMessageClick}\r\n                >\r\n                  {message.content.media.length === 1 ? (\r\n                    <MediaItem\r\n                      media={message.content.media[0]}\r\n                      onClick={handleMessageClick}\r\n                    />\r\n                  ) : (\r\n                    <MediaGrid\r\n                      media={message.content.media}\r\n                      onClick={handleMessageClick}\r\n                      onDownload={(media) => {\r\n                        const link = document.createElement(\"a\");\r\n                        link.href = media.url;\r\n                        link.download = media.fileName;\r\n                        link.target = \"_blank\";\r\n                        link.rel = \"noopener noreferrer\";\r\n                        link.setAttribute(\"download\", media.fileName);\r\n                        document.body.appendChild(link);\r\n                        link.click();\r\n                        document.body.removeChild(link);\r\n                      }}\r\n                    />\r\n                  )}\r\n                </div>\r\n              )}\r\n            </>\r\n          )}\r\n          <div className=\"flex justify-between items-center mt-1\">\r\n            <div\r\n              className={`text-xs text-gray-500 ${isCurrentUser ? \"text-right\" : \"text-left\"} flex items-center gap-1`}\r\n            >\r\n              {formattedTime}\r\n              {isCurrentUser && (\r\n                <span className=\"ml-1\">\r\n                  {isRead ? (\r\n                    <span title=\"Đã xem\" className=\"text-gray-500\">\r\n                      Đã xem\r\n                    </span>\r\n                  ) : isSent ? (\r\n                    <span title=\"Đã gửi\" className=\"text-gray-400\">\r\n                      Đã gửi\r\n                    </span>\r\n                  ) : (\r\n                    <span title=\"Đang gửi\" className=\"text-gray-300\">\r\n                      <svg\r\n                        xmlns=\"http://www.w3.org/2000/svg\"\r\n                        width=\"14\"\r\n                        height=\"14\"\r\n                        viewBox=\"0 0 24 24\"\r\n                        fill=\"none\"\r\n                        stroke=\"currentColor\"\r\n                        strokeWidth=\"2\"\r\n                        strokeLinecap=\"round\"\r\n                        strokeLinejoin=\"round\"\r\n                      >\r\n                        <circle cx=\"12\" cy=\"12\" r=\"10\" />\r\n                      </svg>\r\n                    </span>\r\n                  )}\r\n                </span>\r\n              )}\r\n            </div>\r\n\r\n            {/* Reaction summary and buttons */}\r\n            <div className=\"flex items-center gap-1\">\r\n              {/* Reaction summary */}\r\n              {!message.recalled &&\r\n                message.reactions &&\r\n                message.reactions.length > 0 && (\r\n                  <ReactionSummary reactions={message.reactions} />\r\n                )}\r\n\r\n              {/* Reaction buttons - only show if message is not recalled */}\r\n              {!message.recalled && (\r\n                <ReactionPicker\r\n                  isCurrentUser={isCurrentUser}\r\n                  userReaction={getUserReaction()}\r\n                  onReaction={handleReaction}\r\n                  onRemoveReaction={handleRemoveReaction}\r\n                />\r\n              )}\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {isCurrentUser && showAvatar && (\r\n          <div className=\"ml-2 flex-shrink-0\">\r\n            <Avatar className=\"h-8 w-8\">\r\n              <AvatarImage\r\n                src={\r\n                  currentUser?.userInfo?.profilePictureUrl ||\r\n                  \"/placeholder-avatar.svg\"\r\n                }\r\n                className=\"object-cover\"\r\n              />\r\n              <AvatarFallback>\r\n                {getUserInitials(\r\n                  currentUser ||\r\n                    ({\r\n                      userInfo: {\r\n                        fullName: \"Bạn\",\r\n                      },\r\n                    } as User),\r\n                )}\r\n              </AvatarFallback>\r\n            </Avatar>\r\n          </div>\r\n        )}\r\n      </div>\r\n    </>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAQA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,0DAA0D;AAE1D;AACA;AACA;AAMA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAoBA;AACA;AAvDA;;;;;;;;;;;;;;;;;;;;;;;AAoEA,+CAA+C;AAC/C,SAAS,UAAU,EAAE,KAAK,EAAE,OAAO,EAAkB;IACnD,MAAM,iBAAiB,CAAC;QACtB,IAAI,GAAG;YACL,EAAE,cAAc;YAChB,EAAE,eAAe;QACnB;QACA,+BAA+B;QAC/B,MAAM,OAAO,SAAS,aAAa,CAAC;QACpC,KAAK,IAAI,GAAG,MAAM,GAAG;QACrB,KAAK,QAAQ,GAAG,MAAM,QAAQ,EAAE,0BAA0B;QAC1D,SAAS,IAAI,CAAC,WAAW,CAAC;QAC1B,KAAK,KAAK;QACV,SAAS,IAAI,CAAC,WAAW,CAAC;QAE1B,kCAAkC;QAClC,OAAO;IACT;IAEA,MAAM,cAAc;QAClB,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG;QAC3B,IAAI,SAAS,SAAS,qBAAO,8OAAC,oMAAA,CAAA,QAAS;YAAC,WAAU;;;;;;QAClD,IAAI,SAAS,SAAS,qBAAO,8OAAC,oMAAA,CAAA,QAAK;YAAC,WAAU;;;;;;QAC9C,IAAI,SAAS,SAAS,qBAAO,8OAAC,oMAAA,CAAA,QAAK;YAAC,WAAU;;;;;;QAE9C,wDAAwD;QACxD,MAAM,MAAM,SAAS,SAAS,CAAC,WAAW;QAC1C,IAAI,QAAQ,OAAO,qBAAO,8OAAC,8MAAA,CAAA,WAAQ;YAAC,WAAU;;;;;;QAC9C,qBAAO,8OAAC,kMAAA,CAAA,OAAI;YAAC,WAAU;;;;;;IACzB;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAO;IACT;IAEA,OAAQ,MAAM,IAAI;QAChB,KAAK;YACH,qBACE,8OAAC;gBACC,WAAU;gBACV,SAAS;;kCAET,8OAAC,6HAAA,CAAA,UAAK;wBACJ,KAAK,MAAM,GAAG;wBACd,KAAK,MAAM,QAAQ;wBACnB,WAAU;wBACV,OAAO;wBACP,QAAQ;wBACR,WAAW;;;;;;kCAEb,8OAAC;wBAAI,WAAU;;;;;;kCAEf,8OAAC;wBACC,WAAU;wBACV,SAAS;kCAET,cAAA,8OAAC,0MAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;;;;;;;;;;;;QAK5B,KAAK;YACH,qBACE,8OAAC;gBACC,WAAU;gBACV,SAAS;;kCAET,8OAAC;wBACC,KAAK,MAAM,GAAG;wBACd,WAAU;wBACV,OAAO;4BAAE,UAAU;wBAAO;;;;;;kCAE5B,8OAAC;wBAAI,WAAU;kCAAkF;;;;;;kCAGjG,8OAAC;wBACC,WAAU;wBACV,SAAS;kCAET,cAAA,8OAAC,0MAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;;;;;;;;;;;;QAK5B,KAAK;YACH,qBACE,8OAAC;gBACC,WAAU;gBACV,SAAS;;kCAET,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;;;;;;0CAEnB,8OAAC;gCAAE,WAAU;0CAAgC,MAAM,QAAQ;;;;;;;;;;;;kCAE7D,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,6IAAA,CAAA,UAAe;4BACd,KAAK,MAAM,GAAG;4BACd,UAAU,MAAM,QAAQ;4BACxB,SAAS;4BACT,YAAY,IAAM;;;;;;;;;;;;;;;;;QAM5B,qCAAqC;QACrC;YACE,qBACE,8OAAC;gBACC,WAAU;gBACV,SAAS;;kCAET,8OAAC;wBAAI,WAAU;kCACZ;;;;;;kCAEH,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAE,WAAU;0CAAgC,MAAM,QAAQ;;;;;;0CAC3D,8OAAC;gCAAE,WAAU;0CACV,eAAe,MAAM,QAAQ,CAAC,aAAa;;;;;;;;;;;;kCAGhD,8OAAC;wBACC,WAAU;wBACV,SAAS;kCAET,cAAA,8OAAC,0MAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;;;;;;;;;;;;IAI9B;AACF;AAOA,wDAAwD;AACxD,SAAS,cACP,OAAkC,EAClC,QAAmB,EACnB,yBAA2C;IAE3C,yDAAyD;IACzD,MAAM,aACJ,2BAA2B,YAC3B,QAAQ,MAAM,EAAE,UAAU,YAC1B,UAAU,YACV,AAAC,QAA4B,UAAU,IACvC,CAAC,QAAQ,QAAQ,GACb,CAAC,WAAW,EAAE,QAAQ,QAAQ,CAAC,KAAK,CAAC,CAAC,IAAI,GAC1C,iBAAiB;IAEvB,2DAA2D;IAC3D,OAAO,eAAe,YAClB,QAAQ,QAAQ,GACd,CAAC,WAAW,EAAE,QAAQ,QAAQ,CAAC,KAAK,CAAC,CAAC,IAAI,GAC1C,oBACF;AACN;AAsBA,SAAS,cAAc,EACrB,MAAM,EACN,OAAO,EACP,YAAY,EACZ,OAAO,EACP,SAAS,EACU;IACnB,qBACE,8OAAC,kIAAA,CAAA,SAAM;QAAC,MAAM;QAAQ,cAAc,CAAC,OAAS,CAAC,QAAQ;kBACrD,cAAA,8OAAC,kIAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,8OAAC,kIAAA,CAAA,eAAY;;sCACX,8OAAC,kIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,8OAAC,gNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;gCAA+B;;;;;;;sCAGtD,8OAAC,kIAAA,CAAA,oBAAiB;sCAAC;;;;;;;;;;;;8BAIrB,8OAAC;oBAAI,WAAU;8BACZ,0BACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;;;;;;;;;6CAGjB;;0CACE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAiC;;;;;;kDAG/C,8OAAC;wCAAE,WAAU;kDAAqC;;;;;;;;;;;;0CAEpD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAiC;;;;;;kDAG/C,8OAAC;wCAAE,WAAU;kDACV;;;;;;;;;;;;;;;;;;;8BAMX,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,kIAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAU,SAAS;sCAAS;;;;;;wBAG3C,CAAC,2BACA,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,SAAS;gCACP,UAAU,SAAS,CAAC,SAAS,CAAC;gCAC9B,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;4BAChB;;8CAEA,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;;;;;;;;;;;;;AAQ/C;AAEA,wCAAwC;AACxC,MAAM,uBAAuB;AAE7B,8CAA8C;AAC9C,MAAM,iBAAiB,CAAC;IACtB,IAAI,CAAC,QAAQ,OAAO,EAAE,OAAO;IAC7B,OAAO,QAAQ,OAAO,CAAC,IAAI,IAAI;AACjC;AAEA,+CAA+C;AAC/C,MAAM,kBAAkB,CAAC;IACvB,IAAI,CAAC,QAAQ,OAAO,EAAE,OAAO,EAAE;IAC/B,OAAO,QAAQ,OAAO,CAAC,KAAK,IAAI,EAAE;AACpC;AAEe,SAAS,YAAY,EAClC,OAAO,EACP,aAAa,EACb,aAAa,IAAI,EACjB,OAAO,EACP,cAAc,EACd,SAAS,EACT,QAAQ,EACR,cAAc,EACG;IACjB,oEAAoE;IACpE,MAAM,UACJ,kBACA,QAAQ,WAAW,KAAK,oHAAA,CAAA,cAAW,CAAC,KAAK,IACzC,CAAC,CAAC,QAAQ,OAAO;IACnB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,gBAAgB,CAAA,GAAA,yHAAA,CAAA,oBAAiB,AAAD,EAAE,QAAQ,SAAS;IACzD,MAAM,cAAc,CAAA,GAAA,0HAAA,CAAA,eAAY,AAAD,EAAE,CAAC,QAAU,MAAM,IAAI;IACtD,wCAAwC;IACxC,MAAM,YAAY,CAAA,GAAA,0HAAA,CAAA,eAAY,AAAD;IAE7B,yCAAyC;IACzC,MAAM,qBAAqB,CAAA,GAAA,mIAAA,CAAA,wBAAqB,AAAD;IAC/C,MAAM,4BAA4B,QAAQ,QAAQ,GAC9C,mBAAmB,4BAA4B,CAAC,QAAQ,QAAQ,IAChE;IAEJ,iDAAiD;IACjD,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAC5B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,aAAa,OAAO,GAAG;IACzB,GAAG;QAAC;KAAU;IAEd,uEAAuE;IACvE,MAAM,yBAAyB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE,IAAI;IAE1C,6BAA6B;IAC7B,MAAM,gBAAgB,aAAa,MAAM;IACzC,MAAM,SACJ,MAAM,OAAO,CAAC,QAAQ,MAAM,KAAK,QAAQ,MAAM,CAAC,QAAQ,CAAC;IAC3D,MAAM,SAAS,QAAQ,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,UAAU,CAAC;IAEpD,uDAAuD;IACvD,MAAM,sBAAsB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAA0B,CAAC;IAE5D,8DAA8D;IAC9D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,qEAAqE;QACrE,IACE,WACA,CAAC,iBACD,QAAQ,QAAQ,IAChB,QAAQ,OAAO,IACf,CAAC,CAAC,QAAQ,MAAM,EAAE,YAChB,CAAC,QAAQ,MAAM,CAAC,QAAQ,CAAC,QAAQ,IACjC,QAAQ,MAAM,CAAC,QAAQ,CAAC,QAAQ,KAAK,SAAS,GAChD;YACA,0EAA0E;YAC1E,MAAM,kBAAkB,oBAAoB,OAAO,CAAC,QAAQ,OAAO,CAAC;YACpE,MAAM,MAAM,KAAK,GAAG;YAEpB,IAAI,mBAAmB,MAAM,kBAAkB,MAAM;gBACnD,QAAQ,GAAG,CACT,CAAC,yCAAyC,EAAE,QAAQ,OAAO,CAAC,mBAAmB,EAAE,MAAM,gBAAgB,MAAM,CAAC;gBAEhH;YACF;YAEA,QAAQ,GAAG,CACT,CAAC,sBAAsB,EAAE,QAAQ,EAAE,CAAC,kDAAkD,CAAC;YAGzF,sBAAsB;YACtB,oBAAoB,OAAO,CAAC,QAAQ,OAAO,CAAC,GAAG;YAE/C,uBAAuB;YACvB,OAAO,IAAI,CAAC,oBAAoB,OAAO,EAAE,OAAO,CAAC,CAAC;gBAChD,IAAI,MAAM,oBAAoB,OAAO,CAAC,QAAQ,GAAG,OAAO;oBACtD,aAAa;oBACb,OAAO,oBAAoB,OAAO,CAAC,QAAQ;gBAC7C;YACF;YAEA,kDAAkD;YAClD,WAAW;gBACT,IAAI;oBACF,+CAA+C;oBAC/C,MAAM,mBAAmB,0HAAA,CAAA,eAAY,CAAC,QAAQ;oBAC9C,MAAM,qBAAqB,mIAAA,CAAA,wBAAqB,CAAC,QAAQ;oBAEzD,0EAA0E;oBAC1E,mBAAmB,uBAAuB,CAAC,QAAQ,OAAO;oBAE1D,4EAA4E;oBAC5E,IACE,iBAAiB,oBAAoB,IACrC,iBAAiB,aAAa,EAAE,OAAO,QAAQ,OAAO,EACtD;wBACA,QAAQ,GAAG,CACT,CAAC,wCAAwC,EAAE,QAAQ,OAAO,EAAE;wBAE9D,iBAAiB,oBAAoB;oBACvC,OAAO;wBACL,uEAAuE;wBACvE,QAAQ,GAAG,CACT,CAAC,2DAA2D,EAAE,QAAQ,OAAO,EAAE;wBAEjF,mBAAmB,WAAW;oBAChC;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,CAAC,0CAA0C,CAAC,EAAE;gBAC9D;YACF,GAAG,OAAO,2CAA2C;QACvD;IACF,GAAG;QACD;QACA;QACA,QAAQ,QAAQ;QAChB,QAAQ,MAAM,EAAE,UAAU;QAC1B,QAAQ,EAAE;QACV,QAAQ,OAAO;KAEhB;IAED,8BAA8B;IAC9B,MAAM,kBAAkB;QACtB,OAAO,QAAQ,SAAS,EAAE,KAAK,CAAC,IAAM,EAAE,MAAM,KAAK,aAAa;IAClE;IAEA,MAAM,oBAAoB;QACxB,IAAI,QAAQ,OAAO,CAAC,IAAI,EAAE;YACxB,UAAU,SAAS,CAAC,SAAS,CAAC,QAAQ,OAAO,CAAC,IAAI;QACpD;IACF;IAEA,MAAM,sBAAsB;QAC1B,IAAI;YACF,MAAM,UAAU,iBAAiB,CAAC,QAAQ,EAAE;QAC9C,EAAE,OAAO,OAAO;QACd,wBAAwB;QAC1B;IACF;IAEA,MAAM,qBAAqB;QACzB,IAAI;YACF,IAAI,QAAQ,EAAE,EAAE;gBACd,MAAM,UAAU,uBAAuB,CAAC,QAAQ,EAAE;YACpD;QACF,EAAE,OAAO,OAAO;QACd,wBAAwB;QAC1B;IACF;IAEA,MAAM,sBAAsB;QAC1B,IAAI;YACF,MAAM,UAAU,iBAAiB,CAAC,QAAQ,EAAE;YAC5C,oBAAoB;YACpB,aAAa;QACf,EAAE,OAAO,OAAO;QACd,wBAAwB;QAC1B;IACF;IAEA,MAAM,cAAc;QAClB,IAAI,SAAS;YACX,QAAQ;QACV;IACF;IAEA,MAAM,qBAAqB;QACzB,IAAI,gBAAgB;YAClB,eAAe;QACjB;IACF;IAEA,MAAM,iBAAiB,OAAO;QAC5B,IAAI;YACF,MAAM,UAAU,wBAAwB,CAAC,QAAQ,EAAE,EAAE;QACvD,EAAE,OAAO,OAAO;QACd,wBAAwB;QAC1B;IACF;IAEA,MAAM,uBAAuB;QAC3B,IAAI;YACF,MAAM,UAAU,6BAA6B,CAAC,QAAQ,EAAE;QAC1D,EAAE,OAAO,OAAO;QACd,wBAAwB;QAC1B;IACF;IAEA,MAAM,uBAAuB;QAC3B,uBAAuB;IACzB;IAEA,mCAAmC;IACnC,MAAM,oBAAoB,CAAC;QACzB,OAAO,KAAK,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,SAAS,kBACpC,8OAAC,qMAAA,CAAA,UAAK,CAAC,QAAQ;;oBACZ,IAAI,mBAAK,8OAAC;wBAAK,WAAU;;;;;;oBACzB;;eAFkB;;;;;IAKzB;IAEA,uDAAuD;IACvD,MAAM,wBAAwB,CAAC,MAAc;QAC3C,IAAI,CAAC,cAAc,CAAC,MAAM,OAAO;QAEjC,IAAI;YACF,6CAA6C;YAC7C,MAAM,QAAQ,IAAI,OAChB,CAAC,CAAC,EAAE,WAAW,OAAO,CAAC,uBAAuB,QAAQ,CAAC,CAAC,EACxD;YAGF,4DAA4D;YAC5D,MAAM,QAAQ,KAAK,KAAK,CAAC;YAEzB,OAAO,MAAM,GAAG,CAAC,CAAC,MAAM;gBACtB,2BAA2B;gBAC3B,MAAM,eAAe,KAAK,QAAQ,CAAC,QAC/B,kBAAkB,QAClB;gBAEJ,6DAA6D;gBAC7D,IAAI,KAAK,QAAQ,CAAC,OAAO;oBACvB,qBACE,8OAAC;;4BACE;4BACA,YAAY,MAAM,MAAM,GAAG,mBAAK,8OAAC;;;;;;uBAFzB;;;;;gBAKf;gBAEA,iDAAiD;gBACjD,MAAM,QAAQ,KAAK,KAAK,CAAC;gBAEzB,MAAM,kBAAkB,MAAM,GAAG,CAAC,CAAC,MAAM;oBACvC,6CAA6C;oBAC7C,IAAI,KAAK,WAAW,OAAO,WAAW,WAAW,IAAI;wBACnD,qBACE,8OAAC;4BAEC,WAAU;sCAET;2BAHI,GAAG,UAAU,CAAC,EAAE,GAAG;;;;;oBAM9B;oBACA,OAAO;gBACT;gBAEA,8DAA8D;gBAC9D,qBACE,8OAAC;;wBACE;wBACA,YAAY,MAAM,MAAM,GAAG,mBAAK,8OAAC;;;;;;mBAFzB;;;;;YAKf;QACF,EAAE,OAAM;YACN,+EAA+E;YAC/E,OAAO,KAAK,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,MAAM,sBACjC,8OAAC;;wBACE,KAAK,QAAQ,CAAC,QAAQ,kBAAkB,QAAQ;wBAChD,QAAQ,KAAK,KAAK,CAAC,MAAM,MAAM,GAAG,mBAAK,8OAAC;;;;;;mBAFhC;;;;;QAKf;IACF;IAEA,MAAM,mBAAmB,QAAQ,SAAS,CAAC,QAAQ,CAAC,aAAa,MAAM;IAEvE,MAAM,yBAAyB;QAC7B,+BAA+B;QAC/B,IAAI,CAAC,QAAQ,OAAO,CAAC,IAAI,IAAI,QAAQ,OAAO,CAAC,IAAI,CAAC,IAAI,GAAG,MAAM,KAAK,GAAG;YACrE,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,qBAAqB;gBAC/B,aAAa;YACf;YACA;QACF;QAEA,4CAA4C;QAC5C,IAAI,QAAQ,OAAO,CAAC,IAAI,CAAC,IAAI,GAAG,MAAM,GAAG,sBAAsB;YAC7D,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,qBAAqB;gBAC/B,aAAa,CAAC,wBAAwB,EAAE,qBAAqB,iBAAiB,CAAC;YACjF;YACA;QACF;QAEA,iBAAiB;QACjB,qBAAqB;QACrB,eAAe;QAEf,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,sJAAA,CAAA,gBAAa,AAAD,EAAE,QAAQ,OAAO,CAAC,IAAI,EAAE;YAEzD,IAAI,OAAO,OAAO,IAAI,OAAO,OAAO,EAAE;gBACpC,eAAe,OAAO,OAAO;YAC/B,OAAO;gBACL,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,qBAAqB;oBAC/B,aAAa,OAAO,KAAK,IAAI;gBAC/B;gBACA,qBAAqB;YACvB;QACF,EAAE,OAAM;YACN,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,qBAAqB;gBAC/B,aAAa;YACf;YACA,qBAAqB;QACvB,SAAU;YACR,iBAAiB;QACnB;IACF;IAEA,MAAM,cAAc,eAAe;IACnC,MAAM,eAAe,gBAAgB;IAErC,IAAI,kBAAkB;QACpB,OAAO;IACT;IAEA,qBACE;;0BACE,8OAAC,kJAAA,CAAA,UAAoB;gBACnB,SAAS;gBACT,QAAQ;gBACR,cAAc;;;;;;0BAGhB,8OAAC;gBACC,QAAQ;gBACR,SAAS,IAAM,qBAAqB;gBACpC,cAAc;gBACd,SAAS;gBACT,WAAW;;;;;;0BAGb,8OAAC;gBACC,WAAW,CAAC,WAAW,EAAE,gBAAgB,gBAAgB,gBAAgB,cAAc,CAAC;;oBAEvF,CAAC,iBAAiB,4BACjB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;4BAAC,WAAU;;8CAChB,8OAAC,kIAAA,CAAA,cAAW;oCACV,WAAU;oCACV,KACE,2CAA2C;oCAC3C,2BAA2B,qBAC3B,+CAA+C;oCAC/C,QAAQ,MAAM,EAAE,UAAU,qBAC1B,kDAAkD;oCAClD,UAAU,qBACV;;;;;;8CAGJ,8OAAC,kIAAA,CAAA,iBAAc;8CACZ,CAAA,GAAA,yHAAA,CAAA,kBAAe,AAAD,EAAE;wCACf,UACE,2CAA2C;wCAC3C,6BACA,+CAA+C;wCAC/C,QAAQ,MAAM,EAAE,YAChB,kDAAkD;wCAClD,YACC;4CACC,UAAU,UAAU,eAAe;wCACrC;oCACJ;;;;;;;;;;;;;;;;;kCAMR,8OAAC;wBACC,WAAW,CAAC,YAAY,EAAE,CAAC,iBAAiB,CAAC,aAAa,UAAU,GAAG,mHAAmH,EAAE,gBAAgB,kCAAkC,4GAA4G;wBAC1V,cAAc,IAAM,aAAa;wBACjC,cAAc,IAAM,aAAa;;4BAGhC,CAAC,QAAQ,QAAQ,kBAChB,8OAAC;gCACC,WAAW,CAAC,SAAS,EAAE,gBAAgB,oBAAoB,iBAAiB,gFAAgF,EAAE,gBAAgB,aAAa,mBAAmB,sFAAsF,EAAE,gBAAgB,2CAA2C,yCAAyC,uBAAuB,CAAC;gCACla,OAAO;oCACL,SAAS,YAAY,IAAI;oCACzB,eAAe,YAAY,SAAS;gCACtC;gCACA,cAAc,IAAM,aAAa;;oCAGhC,CAAC,+BACA,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,WAAU;wCACV,SAAS;wCACT,QAAQ;kDAER,cAAA,8OAAC,0MAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;;;;;;kDAKtB,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,WAAU;wCACV,SAAS;kDAET,cAAA,8OAAC,wMAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;;;;;;kDAIrB,8OAAC;;;;;kDAGD,8OAAC,4IAAA,CAAA,eAAY;;0DACX,8OAAC,4IAAA,CAAA,sBAAmB;gDAAC,OAAO;0DAC1B,cAAA,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,WAAU;8DAEV,cAAA,8OAAC,gNAAA,CAAA,iBAAc;wDAAC,WAAU;;;;;;;;;;;;;;;;0DAG9B,8OAAC,4IAAA,CAAA,sBAAmB;gDAAC,OAAM;gDAAM,WAAU;;oDACxC,CAAC,QAAQ,QAAQ,kBAChB;;0EACE,8OAAC,4IAAA,CAAA,mBAAgB;gEAAC,SAAS;;kFACzB,8OAAC,kMAAA,CAAA,OAAI;wEAAC,WAAU;;;;;;kFAChB,8OAAC;kFAAK;;;;;;;;;;;;4DAEP,QAAQ,OAAO,CAAC,IAAI,IACnB,QAAQ,OAAO,CAAC,IAAI,CAAC,IAAI,GAAG,MAAM,GAAG,mBACnC,8OAAC,4IAAA,CAAA,mBAAgB;gEACf,SAAS;gEACT,UACE,iBACA,QAAQ,OAAO,CAAC,IAAI,CAAC,IAAI,GAAG,MAAM,GAChC;gEAEJ,OACE,QAAQ,OAAO,CAAC,IAAI,CAAC,IAAI,GAAG,MAAM,GAClC,uBACI,CAAC,wBAAwB,EAAE,qBAAqB,iBAAiB,CAAC,GAClE;;kFAGN,8OAAC,gNAAA,CAAA,YAAS;wEAAC,WAAU;;;;;;kFACrB,8OAAC;kFACE,gBACG,oBACA,QAAQ,OAAO,CAAC,IAAI,CAAC,IAAI,GAAG,MAAM,GAChC,uBACA,CAAC,YAAY,EAAE,qBAAqB,MAAM,CAAC,GAC3C;;;;;;;;;;;;0EAId,8OAAC,qIAAA,CAAA,YAAS;;;;;;;oDAGb,CAAC,iBAAiB,CAAC,QAAQ,QAAQ,kBAClC,8OAAC,4IAAA,CAAA,mBAAgB;wDAAC,QAAQ;wDAAC,SAAS;;0EAClC,8OAAC,oMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;0EACjB,8OAAC;0EAAK;;;;;;;;;;;;oDAGT,CAAC,QAAQ,QAAQ,kBAChB,8OAAC,4IAAA,CAAA,mBAAgB;wDAAC,SAAS;;0EACzB,8OAAC,wMAAA,CAAA,UAAO;gEAAC,WAAU;;;;;;0EACnB,8OAAC;0EAAK;;;;;;;;;;;;kEAGV,8OAAC,4IAAA,CAAA,mBAAgB;wDAAC,QAAQ;;0EACxB,8OAAC,gMAAA,CAAA,MAAG;gEAAC,WAAU;;;;;;0EACf,8OAAC;0EAAK;;;;;;;;;;;;kEAER,8OAAC,4IAAA,CAAA,mBAAgB;wDAAC,QAAQ;;0EACxB,8OAAC,kMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;0EAChB,8OAAC;0EAAK;;;;;;;;;;;;oDAEP,CAAC,QAAQ,QAAQ,kBAChB,8OAAC,4IAAA,CAAA,mBAAgB;wDAAC,QAAQ;wDAAC,SAAS;;0EAClC,8OAAC,kMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;0EAChB,8OAAC;0EAAK;;;;;;;;;;;;kEAGV,8OAAC,qIAAA,CAAA,YAAS;;;;;oDACT,iBAAiB,CAAC,QAAQ,QAAQ,kBACjC;kEACE,cAAA,8OAAC,4IAAA,CAAA,mBAAgB;4DACf,WAAU;4DACV,SAAS;;8EAET,8OAAC,gNAAA,CAAA,YAAS;oEAAC,WAAU;;;;;;8EACrB,8OAAC;8EAAK;;;;;;;;;;;;;kEAIZ,8OAAC,4IAAA,CAAA,mBAAgB;wDACf,SAAS;wDACT,WAAU;;0EAEV,8OAAC,oMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;0EACjB,8OAAC;0EAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4BAQf,WAAW,CAAC,iBAAiB,CAAC,QAAQ,QAAQ,kBAC7C,8OAAC;gCAAI,WAAU;0CACZ,cAAc,SAAS,UAAU;;;;;;4BAKrC,QAAQ,SAAS,kBAChB,8OAAC;gCACC,WAAW,CAAC,kCAAkC,EAAE,gBAAgB,8BAA8B,6BAA6B;;kDAE3H,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;0DACjB,8OAAC;gDAAK,WAAU;0DAAc;;;;;;;;;;;;kDAEhC,8OAAC;wCAAE,WAAU;kDAAW;;;;;;;;;;;;4BAK3B,CAAC,QAAQ,QAAQ,IAAI,WAAW,mBAC/B,8OAAC;gCACC,WAAW,CAAC,wDAAwD,EAClE,gBACI,QAAQ,QAAQ,GACd,qCACA,mCACF,QAAQ,QAAQ,GACd,qCACA,4BACP,CAAC,EAAE,CAAC,QAAQ,QAAQ,GAAG,oCAAoC,GAAG,CAAC,EAC9D,aAAa,MAAM,IACnB,QAAQ,OAAO,EAAE,SACjB,QAAQ,OAAO,EAAE,QACb,SACA,IACJ;gCACF,SAAS,CAAC,QAAQ,QAAQ,GAAG,qBAAqB;0CAEjD,QAAQ,QAAQ,iBACf,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,gNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;wCAAiB;;;;;;yDAIxC,8OAAC;oCAAI,WAAU;;wCACZ,QAAQ,aAAa,kBACpB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,4NAAA,CAAA,gBAAa;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;wCAI7C,YACG,sBAAsB,aAAa,aAAa,MAChD,YAAY,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,MAAM,sBACjC,8OAAC;;oDACE,KAAK,QAAQ,CAAC,QAAQ,kBAAkB,QAAQ;oDAChD,QAAQ,YAAY,KAAK,CAAC,MAAM,MAAM,GAAG,mBAAK,8OAAC;;;;;;+CAFvC;;;;;;;;;;;;;;;;4BAWxB,CAAC,QAAQ,QAAQ,kBAChB;;oCAEG,QAAQ,aAAa,IAAI,CAAC,QAAQ,OAAO,CAAC,IAAI,kBAC7C,8OAAC;wCACC,WAAW,CAAC,kCAAkC,EAAE,gBAAgB,8BAA8B,6BAA6B;kDAE3H,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,4NAAA,CAAA,gBAAa;oDAAC,WAAU;;;;;;8DACzB,8OAAC;oDAAK,WAAU;8DAAc;;;;;;;;;;;;;;;;;oCAOnC,QAAQ,OAAO,CAAC,KAAK,kBACpB,8OAAC;wCACC,WAAU;wCACV,SAAS;;0DAET,8OAAC,6HAAA,CAAA,UAAK;gDACJ,KAAK,QAAQ,OAAO,CAAC,KAAK;gDAC1B,KAAI;gDACJ,WAAU;gDACV,OAAO;gDACP,QAAQ;;;;;;0DAEV,8OAAC;gDACC,WAAU;gDACV,SAAS,CAAC;oDACR,EAAE,cAAc;oDAChB,EAAE,eAAe;oDACjB,MAAM,OAAO,SAAS,aAAa,CAAC;oDACpC,KAAK,IAAI,GAAG,QAAQ,OAAO,CAAC,KAAK,IAAI;oDACrC,KAAK,QAAQ,GAAG,aAAa,eAAe;oDAC5C,SAAS,IAAI,CAAC,WAAW,CAAC;oDAC1B,KAAK,KAAK;oDACV,SAAS,IAAI,CAAC,WAAW,CAAC;gDAC5B;0DAEA,cAAA,8OAAC,0MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;;;;;;;;;;;;oCAMzB,QAAQ,OAAO,CAAC,KAAK,kBACpB,8OAAC;wCACC,WAAU;wCACV,SAAS;;0DAET,8OAAC;gDACC,KAAK,QAAQ,OAAO,CAAC,KAAK;gDAC1B,QAAQ;gDACR,WAAU;gDACV,OAAO;oDAAE,UAAU;gDAAO;;;;;;0DAE5B,8OAAC;gDACC,WAAU;gDACV,SAAS,CAAC;oDACR,EAAE,cAAc;oDAChB,EAAE,eAAe;oDACjB,MAAM,OAAO,SAAS,aAAa,CAAC;oDACpC,KAAK,IAAI,GAAG,QAAQ,OAAO,CAAC,KAAK,IAAI;oDACrC,KAAK,QAAQ,GAAG,aAAa,eAAe;oDAC5C,SAAS,IAAI,CAAC,WAAW,CAAC;oDAC1B,KAAK,KAAK;oDACV,SAAS,IAAI,CAAC,WAAW,CAAC;gDAC5B;0DAEA,cAAA,8OAAC,0MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;;;;;;;;;;;;oCAMzB,QAAQ,OAAO,CAAC,KAAK,IAAI,QAAQ,OAAO,CAAC,KAAK,CAAC,MAAM,GAAG,mBACvD,8OAAC;wCACC,WAAU;wCACV,SAAS;kDAER,QAAQ,OAAO,CAAC,KAAK,CAAC,MAAM,KAAK,kBAChC,8OAAC;4CACC,OAAO,QAAQ,OAAO,CAAC,KAAK,CAAC,EAAE;4CAC/B,SAAS;;;;;iEAGX,8OAAC,uIAAA,CAAA,UAAS;4CACR,OAAO,QAAQ,OAAO,CAAC,KAAK;4CAC5B,SAAS;4CACT,YAAY,CAAC;gDACX,MAAM,OAAO,SAAS,aAAa,CAAC;gDACpC,KAAK,IAAI,GAAG,MAAM,GAAG;gDACrB,KAAK,QAAQ,GAAG,MAAM,QAAQ;gDAC9B,KAAK,MAAM,GAAG;gDACd,KAAK,GAAG,GAAG;gDACX,KAAK,YAAY,CAAC,YAAY,MAAM,QAAQ;gDAC5C,SAAS,IAAI,CAAC,WAAW,CAAC;gDAC1B,KAAK,KAAK;gDACV,SAAS,IAAI,CAAC,WAAW,CAAC;4CAC5B;;;;;;;;;;;;;0CAOZ,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,WAAW,CAAC,sBAAsB,EAAE,gBAAgB,eAAe,YAAY,wBAAwB,CAAC;;4CAEvG;4CACA,+BACC,8OAAC;gDAAK,WAAU;0DACb,uBACC,8OAAC;oDAAK,OAAM;oDAAS,WAAU;8DAAgB;;;;;2DAG7C,uBACF,8OAAC;oDAAK,OAAM;oDAAS,WAAU;8DAAgB;;;;;yEAI/C,8OAAC;oDAAK,OAAM;oDAAW,WAAU;8DAC/B,cAAA,8OAAC;wDACC,OAAM;wDACN,OAAM;wDACN,QAAO;wDACP,SAAQ;wDACR,MAAK;wDACL,QAAO;wDACP,aAAY;wDACZ,eAAc;wDACd,gBAAe;kEAEf,cAAA,8OAAC;4DAAO,IAAG;4DAAK,IAAG;4DAAK,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAStC,8OAAC;wCAAI,WAAU;;4CAEZ,CAAC,QAAQ,QAAQ,IAChB,QAAQ,SAAS,IACjB,QAAQ,SAAS,CAAC,MAAM,GAAG,mBACzB,8OAAC,6IAAA,CAAA,UAAe;gDAAC,WAAW,QAAQ,SAAS;;;;;;4CAIhD,CAAC,QAAQ,QAAQ,kBAChB,8OAAC,4IAAA,CAAA,UAAc;gDACb,eAAe;gDACf,cAAc;gDACd,YAAY;gDACZ,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;oBAO3B,iBAAiB,4BAChB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;4BAAC,WAAU;;8CAChB,8OAAC,kIAAA,CAAA,cAAW;oCACV,KACE,aAAa,UAAU,qBACvB;oCAEF,WAAU;;;;;;8CAEZ,8OAAC,kIAAA,CAAA,iBAAc;8CACZ,CAAA,GAAA,yHAAA,CAAA,kBAAe,AAAD,EACb,eACG;wCACC,UAAU;4CACR,UAAU;wCACZ;oCACF;;;;;;;;;;;;;;;;;;;;;;;;;AASpB", "debugId": null}}]}