{"version": 3, "sources": [], "sections": [{"offset": {"line": 95, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/src/utils/userCache.ts"], "sourcesContent": ["import { User, UserInfo } from \"@/types/base\";\r\n\r\n// Create a cache for user data to avoid redundant API calls\r\nconst userDataCache: Record<string, { user: User; timestamp: number }> = {};\r\nconst userInfoCache: Record<string, { userInfo: UserInfo; timestamp: number }> =\r\n  {};\r\n\r\n// Cache expiration time in milliseconds (10 minutes for better performance)\r\nconst CACHE_EXPIRATION = 10 * 60 * 1000;\r\n\r\n// Function to check if cached data is still valid\r\nexport function isCacheValid(userId: string): boolean {\r\n  if (!userDataCache[userId]) return false;\r\n\r\n  const now = Date.now();\r\n  const cacheTime = userDataCache[userId].timestamp;\r\n\r\n  return now - cacheTime < CACHE_EXPIRATION;\r\n}\r\n\r\n// Function to check if cached userInfo is still valid\r\nexport function isUserInfoCacheValid(userId: string): boolean {\r\n  if (!userInfoCache[userId]) return false;\r\n\r\n  const now = Date.now();\r\n  const cacheTime = userInfoCache[userId].timestamp;\r\n\r\n  return now - cacheTime < CACHE_EXPIRATION;\r\n}\r\n\r\n// Function to get user data from cache\r\nexport function getCachedUserData(\r\n  userId: string,\r\n  allowExpired: boolean = false,\r\n): User | null {\r\n  // If we allow expired data, just check if it exists\r\n  if (allowExpired && userDataCache[userId]) {\r\n    console.log(`[USER_CACHE] Returning expired cache data for user ${userId}`);\r\n    return userDataCache[userId].user;\r\n  }\r\n\r\n  // Otherwise check if it's valid\r\n  if (isCacheValid(userId)) {\r\n    console.log(`[USER_CACHE] Returning valid cache data for user ${userId}`);\r\n    return userDataCache[userId].user;\r\n  }\r\n\r\n  return null;\r\n}\r\n\r\n// Function to get userInfo from cache\r\nexport function getCachedUserInfo(\r\n  userId: string,\r\n  allowExpired: boolean = true, // Allow expired for better UX\r\n): UserInfo | null {\r\n  if (allowExpired && userInfoCache[userId]) {\r\n    return userInfoCache[userId].userInfo;\r\n  }\r\n\r\n  if (isUserInfoCacheValid(userId)) {\r\n    return userInfoCache[userId].userInfo;\r\n  }\r\n\r\n  return null;\r\n}\r\n\r\n// Function to store user data in cache\r\nexport function cacheUserData(userId: string, user: User): void {\r\n  userDataCache[userId] = {\r\n    user,\r\n    timestamp: Date.now(),\r\n  };\r\n\r\n  // Also cache userInfo if available\r\n  if (user.userInfo) {\r\n    cacheUserInfo(userId, user.userInfo);\r\n  }\r\n}\r\n\r\n// Function to store userInfo in cache\r\nexport function cacheUserInfo(userId: string, userInfo: UserInfo): void {\r\n  userInfoCache[userId] = {\r\n    userInfo,\r\n    timestamp: Date.now(),\r\n  };\r\n}\r\n\r\n// Function to remove user data from cache\r\nexport function removeCachedUserData(userId: string): void {\r\n  if (userDataCache[userId]) {\r\n    delete userDataCache[userId];\r\n  }\r\n  if (userInfoCache[userId]) {\r\n    delete userInfoCache[userId];\r\n  }\r\n}\r\n\r\n// Function to cache user info from group members\r\nexport function cacheUserInfoFromGroupMembers(\r\n  memberUsers: Array<{\r\n    id: string;\r\n    fullName: string;\r\n    profilePictureUrl?: string | null;\r\n  }>,\r\n): void {\r\n  memberUsers.forEach((member) => {\r\n    if (member.id && member.fullName) {\r\n      cacheUserInfo(member.id, {\r\n        id: member.id,\r\n        fullName: member.fullName,\r\n        profilePictureUrl: member.profilePictureUrl || null,\r\n        statusMessage: \"\",\r\n        blockStrangers: false,\r\n        createdAt: new Date(),\r\n        updatedAt: new Date(),\r\n        userAuth: { id: member.id } as User,\r\n      });\r\n    }\r\n  });\r\n}\r\n\r\n// Function to clear the entire cache\r\nexport function clearUserCache(): void {\r\n  Object.keys(userDataCache).forEach((key) => {\r\n    delete userDataCache[key];\r\n  });\r\n}\r\n\r\n// Function to invalidate specific user cache entries\r\nexport function invalidateUserCache(userId: string): void {\r\n  if (userInfoCache[userId]) {\r\n    delete userInfoCache[userId];\r\n  }\r\n  if (userDataCache[userId]) {\r\n    delete userDataCache[userId];\r\n  }\r\n}\r\n\r\n// Function to invalidate cache for multiple users\r\nexport function invalidateMultipleUserCache(userIds: string[]): void {\r\n  userIds.forEach((userId) => {\r\n    invalidateUserCache(userId);\r\n  });\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAEA,4DAA4D;AAC5D,MAAM,gBAAmE,CAAC;AAC1E,MAAM,gBACJ,CAAC;AAEH,4EAA4E;AAC5E,MAAM,mBAAmB,KAAK,KAAK;AAG5B,SAAS,aAAa,MAAc;IACzC,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,OAAO;IAEnC,MAAM,MAAM,KAAK,GAAG;IACpB,MAAM,YAAY,aAAa,CAAC,OAAO,CAAC,SAAS;IAEjD,OAAO,MAAM,YAAY;AAC3B;AAGO,SAAS,qBAAqB,MAAc;IACjD,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,OAAO;IAEnC,MAAM,MAAM,KAAK,GAAG;IACpB,MAAM,YAAY,aAAa,CAAC,OAAO,CAAC,SAAS;IAEjD,OAAO,MAAM,YAAY;AAC3B;AAGO,SAAS,kBACd,MAAc,EACd,eAAwB,KAAK;IAE7B,oDAAoD;IACpD,IAAI,gBAAgB,aAAa,CAAC,OAAO,EAAE;QACzC,QAAQ,GAAG,CAAC,CAAC,mDAAmD,EAAE,QAAQ;QAC1E,OAAO,aAAa,CAAC,OAAO,CAAC,IAAI;IACnC;IAEA,gCAAgC;IAChC,IAAI,aAAa,SAAS;QACxB,QAAQ,GAAG,CAAC,CAAC,iDAAiD,EAAE,QAAQ;QACxE,OAAO,aAAa,CAAC,OAAO,CAAC,IAAI;IACnC;IAEA,OAAO;AACT;AAGO,SAAS,kBACd,MAAc,EACd,eAAwB,IAAI;IAE5B,IAAI,gBAAgB,aAAa,CAAC,OAAO,EAAE;QACzC,OAAO,aAAa,CAAC,OAAO,CAAC,QAAQ;IACvC;IAEA,IAAI,qBAAqB,SAAS;QAChC,OAAO,aAAa,CAAC,OAAO,CAAC,QAAQ;IACvC;IAEA,OAAO;AACT;AAGO,SAAS,cAAc,MAAc,EAAE,IAAU;IACtD,aAAa,CAAC,OAAO,GAAG;QACtB;QACA,WAAW,KAAK,GAAG;IACrB;IAEA,mCAAmC;IACnC,IAAI,KAAK,QAAQ,EAAE;QACjB,cAAc,QAAQ,KAAK,QAAQ;IACrC;AACF;AAGO,SAAS,cAAc,MAAc,EAAE,QAAkB;IAC9D,aAAa,CAAC,OAAO,GAAG;QACtB;QACA,WAAW,KAAK,GAAG;IACrB;AACF;AAGO,SAAS,qBAAqB,MAAc;IACjD,IAAI,aAAa,CAAC,OAAO,EAAE;QACzB,OAAO,aAAa,CAAC,OAAO;IAC9B;IACA,IAAI,aAAa,CAAC,OAAO,EAAE;QACzB,OAAO,aAAa,CAAC,OAAO;IAC9B;AACF;AAGO,SAAS,8BACd,WAIE;IAEF,YAAY,OAAO,CAAC,CAAC;QACnB,IAAI,OAAO,EAAE,IAAI,OAAO,QAAQ,EAAE;YAChC,cAAc,OAAO,EAAE,EAAE;gBACvB,IAAI,OAAO,EAAE;gBACb,UAAU,OAAO,QAAQ;gBACzB,mBAAmB,OAAO,iBAAiB,IAAI;gBAC/C,eAAe;gBACf,gBAAgB;gBAChB,WAAW,IAAI;gBACf,WAAW,IAAI;gBACf,UAAU;oBAAE,IAAI,OAAO,EAAE;gBAAC;YAC5B;QACF;IACF;AACF;AAGO,SAAS;IACd,OAAO,IAAI,CAAC,eAAe,OAAO,CAAC,CAAC;QAClC,OAAO,aAAa,CAAC,IAAI;IAC3B;AACF;AAGO,SAAS,oBAAoB,MAAc;IAChD,IAAI,aAAa,CAAC,OAAO,EAAE;QACzB,OAAO,aAAa,CAAC,OAAO;IAC9B;IACA,IAAI,aAAa,CAAC,OAAO,EAAE;QACzB,OAAO,aAAa,CAAC,OAAO;IAC9B;AACF;AAGO,SAAS,4BAA4B,OAAiB;IAC3D,QAAQ,OAAO,CAAC,CAAC;QACf,oBAAoB;IACtB;AACF", "debugId": null}}, {"offset": {"line": 213, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/src/actions/user.action.ts"], "sourcesContent": ["//\"use server\";\nimport axiosInstance from \"@/lib/axios\";\nimport { useAuthStore } from \"@/stores/authStore\";\nimport { User } from \"@/types/base\";\nimport { AxiosError } from \"axios\";\nimport {\n  getCachedUserData,\n  cacheUserData,\n  removeCachedUserData,\n} from \"@/utils/userCache\";\n\n// <PERSON><PERSON>y danh s<PERSON>ch tất cả users\nexport async function getAllUsers() {\n  try {\n    const response = await axiosInstance.get(\"/users\");\n    const users: User[] = response.data;\n    return { success: true, users };\n  } catch (error) {\n    console.error(\"Get all users failed:\", error);\n    return {\n      success: false,\n      error: error instanceof Error ? error.message : \"Unknown error\",\n    };\n  }\n}\n\n// Lấy thông tin user theo ID\nexport async function getUserDataById(id: string, token?: string) {\n  try {\n    // Kiểm tra id hợp lệ\n    if (!id || typeof id !== \"string\" || id.trim() === \"\") {\n      console.error(\"[USER_ACTION] Invalid user ID provided:\", id);\n      return {\n        success: false,\n        error: \"Invalid user ID\",\n      };\n    }\n\n    // Check if user data is in cache and still valid\n    const cachedUser = getCachedUserData(id);\n    if (cachedUser) {\n      console.log(`[USER_ACTION] Using cached user data for ID: ${id}`);\n      return { success: true, user: cachedUser };\n    }\n\n    console.log(`[USER_ACTION] Fetching user data for ID: ${id}`);\n\n    // Add timeout to the request to prevent hanging\n    const controller = new AbortController();\n    const timeoutId = setTimeout(() => controller.abort(), 5000); // 5 second timeout\n\n    try {\n      // Set up request config\n      const config: any = {\n        signal: controller.signal,\n      };\n\n      // If token is provided, use it in the request\n      if (token) {\n        config.headers = {\n          ...config.headers,\n          Authorization: `Bearer ${token}`,\n        };\n        console.log(`[USER_ACTION] Using provided token for user data request`);\n      }\n\n      const response = await axiosInstance.get(`/users/${id}`, config);\n\n      clearTimeout(timeoutId);\n\n      const user: User = response.data;\n      console.log(\n        `[USER_ACTION] Successfully fetched user data for ID: ${id}`,\n        user,\n      );\n\n      // Store user data in cache\n      cacheUserData(id, user);\n\n      return { success: true, user };\n    } catch (requestError) {\n      clearTimeout(timeoutId);\n      throw requestError; // Re-throw to be caught by the outer catch\n    }\n  } catch (error) {\n    console.error(`[USER_ACTION] Get user by ID failed for ID ${id}:`, error);\n\n    // Check if this is a timeout or abort error\n    if (\n      error &&\n      typeof error === \"object\" &&\n      \"name\" in error &&\n      (error.name === \"AbortError\" || error.name === \"TimeoutError\")\n    ) {\n      console.warn(`[USER_ACTION] Request timed out for user ID: ${id}`);\n\n      // Try to get from cache even if expired as fallback\n      const expiredCachedUser = getCachedUserData(id, true);\n      if (expiredCachedUser) {\n        console.log(\n          `[USER_ACTION] Using expired cached user data for ID: ${id} after timeout`,\n        );\n        return { success: true, user: expiredCachedUser };\n      }\n    }\n\n    // Tạo user giả nếu không tìm thấy (chỉ cho mục đích hiển thị UI)\n    const axiosError = error as AxiosError;\n    if (axiosError.response && axiosError.response.status === 404) {\n      console.log(`[USER_ACTION] Creating placeholder user for ID ${id}`);\n      const placeholderUser: User = {\n        id: id,\n        email: null,\n        phoneNumber: null,\n        passwordHash: \"\",\n        createdAt: new Date(),\n        updatedAt: new Date(),\n        userInfo: {\n          id: id,\n          fullName: `Người dùng ${id.slice(-4)}`, // Use last 4 chars of ID for uniqueness\n          profilePictureUrl: null,\n          statusMessage: \"\",\n          blockStrangers: false,\n          createdAt: new Date(),\n          updatedAt: new Date(),\n          userAuth: null as unknown as User,\n        },\n        refreshTokens: [],\n        qrCodes: [],\n        posts: [],\n        stories: [],\n        groupMembers: [],\n        cloudFiles: [],\n        pinnedItems: [],\n        sentFriends: [],\n        receivedFriends: [],\n        contacts: [],\n        contactOf: [],\n        settings: [],\n        postReactions: [],\n        hiddenPosts: [],\n        addedBy: [],\n        notifications: [],\n        sentMessages: [],\n        receivedMessages: [],\n        comments: [],\n      };\n\n      // Cache the placeholder user too\n      cacheUserData(id, placeholderUser);\n\n      return { success: true, user: placeholderUser };\n    }\n\n    // For any other error, create a generic placeholder user\n    console.log(\n      `[USER_ACTION] Creating generic placeholder user for ID ${id} due to error`,\n    );\n    const genericUser: User = {\n      id: id,\n      email: null,\n      phoneNumber: null,\n      passwordHash: \"\",\n      createdAt: new Date(),\n      updatedAt: new Date(),\n      userInfo: {\n        id: id,\n        fullName: `Người dùng ${id.slice(-4)}`, // Use last 4 chars of ID for uniqueness\n        profilePictureUrl: null,\n        statusMessage: \"\",\n        blockStrangers: false,\n        createdAt: new Date(),\n        updatedAt: new Date(),\n        userAuth: null as unknown as User,\n      },\n      refreshTokens: [],\n      qrCodes: [],\n      posts: [],\n      stories: [],\n      groupMembers: [],\n      cloudFiles: [],\n      pinnedItems: [],\n      sentFriends: [],\n      receivedFriends: [],\n      contacts: [],\n      contactOf: [],\n      settings: [],\n      postReactions: [],\n      hiddenPosts: [],\n      addedBy: [],\n      notifications: [],\n      sentMessages: [],\n      receivedMessages: [],\n      comments: [],\n    };\n\n    // Cache the generic user too\n    cacheUserData(id, genericUser);\n\n    return { success: true, user: genericUser };\n  }\n}\n\n// Batch fetch multiple users at once\nexport async function batchGetUserData(userIds: string[]) {\n  // Filter out duplicate IDs\n  const uniqueIds = [...new Set(userIds)];\n\n  // Check which users are already in cache\n  const cachedUsers: User[] = [];\n  const idsToFetch: string[] = [];\n\n  uniqueIds.forEach((id) => {\n    const cachedUser = getCachedUserData(id);\n    if (cachedUser) {\n      cachedUsers.push(cachedUser);\n    } else {\n      idsToFetch.push(id);\n    }\n  });\n\n  // If all users are in cache, return immediately\n  if (idsToFetch.length === 0) {\n    console.log(`All ${uniqueIds.length} users found in cache`);\n    return { success: true, users: cachedUsers };\n  }\n\n  // Otherwise, fetch the remaining users\n  try {\n    console.log(`Batch fetching ${idsToFetch.length} users`);\n\n    // Fetch each user individually (could be optimized with a batch API endpoint)\n    const fetchPromises = idsToFetch.map((id) => getUserDataById(id));\n    const results = await Promise.all(fetchPromises);\n\n    // Combine cached and newly fetched users\n    const fetchedUsers = results\n      .filter((result) => result.success && result.user)\n      .map((result) => result.user as User);\n\n    const allUsers = [...cachedUsers, ...fetchedUsers];\n\n    return { success: true, users: allUsers };\n  } catch (error) {\n    console.error(\"Batch get users failed:\", error);\n    return {\n      success: false,\n      error: error instanceof Error ? error.message : \"Unknown error\",\n      users: cachedUsers, // Return any cached users we did find\n    };\n  }\n}\n\n// Lấy thông tin cơ bản của user theo ID\nexport async function getUserBasicInfo(id: string) {\n  try {\n    const response = await axiosInstance.get(`/users/${id}/basic-info`);\n    const userInfo = response.data; // Type tùy thuộc vào định nghĩa của bạn\n    return { success: true, userInfo };\n  } catch (error) {\n    console.error(\"Get user basic info failed:\", error);\n    return {\n      success: false,\n      error: error instanceof Error ? error.message : \"Unknown error\",\n    };\n  }\n}\n\n// Cập nhật thông tin user (giả sử backend có endpoint PATCH /users/:id)\nexport async function updateUser(id: string, userData: Partial<User>) {\n  try {\n    const response = await axiosInstance.patch(`/users/${id}`, userData);\n    const updatedUser: User = response.data;\n\n    // Cập nhật lại thông tin trong store nếu user hiện tại đang được chỉnh sửa\n    const currentUser = useAuthStore.getState().user;\n    if (currentUser && currentUser.id === id) {\n      useAuthStore.getState().updateUser(updatedUser);\n    }\n\n    // Update the cache with the new user data\n    cacheUserData(id, updatedUser);\n\n    return { success: true, user: updatedUser };\n  } catch (error) {\n    console.error(\"Update user failed:\", error);\n    return {\n      success: false,\n      error: error instanceof Error ? error.message : \"Unknown error\",\n    };\n  }\n}\n\n// Xóa user (giả sử backend có endpoint DELETE /users/:id)\nexport async function deleteUser(id: string) {\n  try {\n    await axiosInstance.delete(`/users/${id}`);\n\n    // Nếu user hiện tại bị xóa, thực hiện logout\n    const currentUser = useAuthStore.getState().user;\n    if (currentUser && currentUser.id === id) {\n      useAuthStore.getState().logout();\n    }\n\n    // Remove from cache if exists\n    removeCachedUserData(id);\n\n    return { success: true };\n  } catch (error) {\n    console.error(\"Delete user failed:\", error);\n    return {\n      success: false,\n      error: error instanceof Error ? error.message : \"Unknown error\",\n    };\n  }\n}\n\n// Upload profile picture\nexport async function updateProfilePicture(file: File) {\n  try {\n    // Make sure file is not undefined\n    if (!file) {\n      throw new Error(\"No file selected\");\n    }\n\n    const formData = new FormData();\n    formData.append(\"file\", file);\n\n    // Change the content type to multipart/form-data\n    const response = await axiosInstance.put(\n      \"/auth/update-profile-picture\",\n      formData,\n      {\n        headers: {\n          \"Content-Type\": \"multipart/form-data\",\n        },\n      },\n    );\n\n    const { message, url } = response.data;\n\n    // Thêm timestamp vào URL để tránh cache\n    const urlWithTimestamp = `${url}?t=${new Date().getTime()}`;\n\n    // Lấy dữ liệu user hiện tại từ store\n    const currentUser = useAuthStore.getState().user;\n    if (currentUser && currentUser.id) {\n      // Cập nhật ngay lập tức trong store với URL mới có timestamp\n      if (currentUser.userInfo) {\n        const updatedUser = {\n          ...currentUser,\n          userInfo: {\n            ...currentUser.userInfo,\n            profilePictureUrl: urlWithTimestamp,\n          },\n        };\n\n        // Cập nhật store\n        useAuthStore.getState().updateUser(updatedUser);\n\n        // Cập nhật cache\n        cacheUserData(currentUser.id, updatedUser);\n\n        console.log(\n          \"Profile picture immediately updated in store with timestamp\",\n        );\n      }\n\n      // Sau đó, thử lấy dữ liệu đầy đủ từ server (không chờ đợi)\n      getUserDataById(currentUser.id)\n        .then((userResponse) => {\n          if (userResponse.success && userResponse.user) {\n            // Đảm bảo URL hình ảnh mới có timestamp\n            const serverUser = userResponse.user;\n            if (serverUser.userInfo && serverUser.userInfo.profilePictureUrl) {\n              serverUser.userInfo.profilePictureUrl = `${serverUser.userInfo.profilePictureUrl}?t=${new Date().getTime()}`;\n            }\n\n            // Cập nhật store với dữ liệu đầy đủ từ server\n            useAuthStore.getState().updateUser(serverUser);\n\n            // Cập nhật cache\n            cacheUserData(currentUser.id, serverUser);\n\n            console.log(\n              \"User data updated from database after profile picture change\",\n            );\n          }\n        })\n        .catch((fetchError) => {\n          console.error(\"Error fetching updated user data:\", fetchError);\n          // Không cần làm gì vì đã cập nhật store trước đó\n        });\n    }\n\n    // Trả về URL có timestamp để client có thể sử dụng ngay\n    return { success: true, message, url: urlWithTimestamp };\n  } catch (error) {\n    console.error(\"Update profile picture failed:\", error);\n    return {\n      success: false,\n      error: error instanceof Error ? error.message : \"Unknown error\",\n    };\n  }\n}\n\n// Upload cover image\nexport async function updateCoverImage(file: File) {\n  try {\n    // Make sure file is not undefined\n    if (!file) {\n      throw new Error(\"No file selected\");\n    }\n\n    const formData = new FormData();\n    formData.append(\"file\", file);\n\n    // Change the content type to multipart/form-data\n    const response = await axiosInstance.put(\n      \"/auth/update-cover-image\",\n      formData,\n      {\n        headers: {\n          \"Content-Type\": \"multipart/form-data\",\n        },\n      },\n    );\n\n    const { message, url } = response.data;\n\n    // Thêm timestamp vào URL để tránh cache\n    const urlWithTimestamp = `${url}?t=${new Date().getTime()}`;\n\n    // Lấy dữ liệu user hiện tại từ store\n    const currentUser = useAuthStore.getState().user;\n    if (currentUser && currentUser.id) {\n      // Cập nhật ngay lập tức trong store với URL mới có timestamp\n      if (currentUser.userInfo) {\n        const updatedUser = {\n          ...currentUser,\n          userInfo: {\n            ...currentUser.userInfo,\n            coverImgUrl: urlWithTimestamp,\n          },\n        };\n\n        // Cập nhật store\n        useAuthStore.getState().updateUser(updatedUser);\n\n        // Cập nhật cache\n        cacheUserData(currentUser.id, updatedUser);\n\n        console.log(\"Cover image immediately updated in store with timestamp\");\n      }\n\n      // Sau đó, thử lấy dữ liệu đầy đủ từ server (không chờ đợi)\n      getUserDataById(currentUser.id)\n        .then((userResponse) => {\n          if (userResponse.success && userResponse.user) {\n            // Đảm bảo URL hình ảnh mới có timestamp\n            const serverUser = userResponse.user;\n            if (serverUser.userInfo && serverUser.userInfo.coverImgUrl) {\n              serverUser.userInfo.coverImgUrl = `${serverUser.userInfo.coverImgUrl}?t=${new Date().getTime()}`;\n            }\n\n            // Cập nhật store với dữ liệu đầy đủ từ server\n            useAuthStore.getState().updateUser(serverUser);\n\n            // Cập nhật cache\n            cacheUserData(currentUser.id, serverUser);\n\n            console.log(\n              \"User data updated from database after cover image change\",\n            );\n          }\n        })\n        .catch((fetchError) => {\n          console.error(\"Error fetching updated user data:\", fetchError);\n          // Không cần làm gì vì đã cập nhật store trước đó\n        });\n    }\n\n    // Trả về URL có timestamp để client có thể sử dụng ngay\n    return { success: true, message, url: urlWithTimestamp };\n  } catch (error) {\n    console.error(\"Update cover image failed:\", error);\n    return {\n      success: false,\n      error: error instanceof Error ? error.message : \"Unknown error\",\n    };\n  }\n}\n\n// Cập nhật thông tin cơ bản của người dùng\nexport async function updateUserBasicInfo(userData: {\n  fullName?: string;\n  phoneNumber?: string;\n  bio?: string;\n  gender?: string;\n  dateOfBirth?: Date;\n}) {\n  try {\n    const currentUser = useAuthStore.getState().user;\n    if (!currentUser || !currentUser.id) {\n      throw new Error(\"User not authenticated\");\n    }\n\n    // Cập nhật ngay lập tức trong store trước khi gọi API\n    // Điều này giúp UI cập nhật ngay lập tức\n    if (currentUser.userInfo) {\n      // Xử lý gender để đảm bảo đúng kiểu Gender\n      let updatedGender = currentUser.userInfo.gender;\n      if (userData.gender) {\n        // Chuyển đổi string thành Gender enum\n        if (\n          userData.gender === \"MALE\" ||\n          userData.gender === \"FEMALE\" ||\n          userData.gender === \"OTHER\"\n        ) {\n          updatedGender = userData.gender as any; // Ép kiểu an toàn vì đã kiểm tra giá trị\n        }\n      }\n\n      const immediateUpdatedUserInfo = {\n        ...currentUser.userInfo,\n        fullName: userData.fullName || currentUser.userInfo.fullName,\n        gender: updatedGender,\n        dateOfBirth: userData.dateOfBirth || currentUser.userInfo.dateOfBirth,\n        bio: userData.bio || currentUser.userInfo.bio,\n      };\n\n      const immediateUserToUpdate = {\n        ...currentUser,\n        userInfo: immediateUpdatedUserInfo,\n      };\n\n      // Cập nhật store ngay lập tức\n      useAuthStore.getState().updateUser(immediateUserToUpdate);\n\n      // Cập nhật cache\n      cacheUserData(currentUser.id, immediateUserToUpdate as User);\n\n      console.log(\"Basic info immediately updated in store\");\n    }\n\n    // Gọi API để cập nhật thông tin trên server\n    const response = await axiosInstance.put(\n      `/auth/update-basic-info`,\n      userData,\n    );\n    const updatedUser = response.data;\n\n    // Sau khi API thành công, lấy dữ liệu mới từ server (không chờ đợi)\n    // Sử dụng Promise để không chặn luồng chính\n    getUserDataById(currentUser.id)\n      .then((userResponse) => {\n        if (userResponse.success && userResponse.user) {\n          // Cập nhật toàn bộ dữ liệu user trong store\n          useAuthStore.getState().updateUser(userResponse.user);\n\n          // Cập nhật cache\n          cacheUserData(currentUser.id, userResponse.user);\n\n          console.log(\n            \"User data updated from database after basic info change\",\n          );\n        }\n      })\n      .catch((fetchError) => {\n        console.error(\"Error fetching updated user data:\", fetchError);\n        // Không cần làm gì vì đã cập nhật store trước đó\n      });\n\n    // Trả về kết quả thành công ngay lập tức\n    return {\n      success: true,\n      user: updatedUser,\n      // Thêm thông tin đã cập nhật để UI có thể sử dụng ngay\n      updatedInfo: {\n        fullName: userData.fullName,\n        gender: userData.gender,\n        dateOfBirth: userData.dateOfBirth,\n        bio: userData.bio,\n      },\n    };\n  } catch (error) {\n    console.error(\"Update user basic info failed:\", error);\n    return {\n      success: false,\n      error: error instanceof Error ? error.message : \"Unknown error\",\n    };\n  }\n}\n\n// Tìm kiếm người dùng theo email hoặc số điện thoại\nexport async function searchUser(searchValue: string) {\n  try {\n    // Kiểm tra xem searchValue có phải là email không\n    const isEmail = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(searchValue);\n\n    // Tạo payload phù hợp dựa trên loại tìm kiếm\n    const payload = isEmail\n      ? { email: searchValue }\n      : { phoneNumber: searchValue };\n\n    const response = await axiosInstance.post(\"/users/search\", payload);\n\n    // Cache the found user\n    if (response.data && response.data.id) {\n      cacheUserData(response.data.id, response.data);\n    }\n\n    return { success: true, user: response.data };\n  } catch (error) {\n    // Kiểm tra nếu là lỗi 404 (không tìm thấy)\n    const axiosError = error as AxiosError;\n    if (axiosError.response && axiosError.response.status === 404) {\n      // Kiểm tra lại isEmail vì nó đã ra khỏi phạm vi của try\n      const isEmailSearch = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(searchValue);\n      console.log(\n        `Không tìm thấy người dùng với ${isEmailSearch ? \"email\" : \"số điện thoại\"}: ${searchValue}`,\n      );\n      // Trả về success: false nhưng không có thông báo lỗi để UI hiển thị \"Không tìm thấy\"\n      return { success: false };\n    }\n\n    console.error(\"Search user failed:\", error);\n    return {\n      success: false,\n      error: error instanceof Error ? error.message : \"Unknown error\",\n    };\n  }\n}\n\n// Tìm kiếm người dùng theo số điện thoại (giữ lại để tương thích ngược)\nexport async function searchUserByPhoneNumber(phoneNumber: string) {\n  return searchUser(phoneNumber);\n}\n"], "names": [], "mappings": "AAAA,eAAe;;;;;;;;;;;;;;AACf;AACA;AAGA;;;;AAOO,eAAe;IACpB,IAAI;QACF,MAAM,WAAW,MAAM,mHAAA,CAAA,UAAa,CAAC,GAAG,CAAC;QACzC,MAAM,QAAgB,SAAS,IAAI;QACnC,OAAO;YAAE,SAAS;YAAM;QAAM;IAChC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yBAAyB;QACvC,OAAO;YACL,SAAS;YACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAClD;IACF;AACF;AAGO,eAAe,gBAAgB,EAAU,EAAE,KAAc;IAC9D,IAAI;QACF,qBAAqB;QACrB,IAAI,CAAC,MAAM,OAAO,OAAO,YAAY,GAAG,IAAI,OAAO,IAAI;YACrD,QAAQ,KAAK,CAAC,2CAA2C;YACzD,OAAO;gBACL,SAAS;gBACT,OAAO;YACT;QACF;QAEA,iDAAiD;QACjD,MAAM,aAAa,CAAA,GAAA,yHAAA,CAAA,oBAAiB,AAAD,EAAE;QACrC,IAAI,YAAY;YACd,QAAQ,GAAG,CAAC,CAAC,6CAA6C,EAAE,IAAI;YAChE,OAAO;gBAAE,SAAS;gBAAM,MAAM;YAAW;QAC3C;QAEA,QAAQ,GAAG,CAAC,CAAC,yCAAyC,EAAE,IAAI;QAE5D,gDAAgD;QAChD,MAAM,aAAa,IAAI;QACvB,MAAM,YAAY,WAAW,IAAM,WAAW,KAAK,IAAI,OAAO,mBAAmB;QAEjF,IAAI;YACF,wBAAwB;YACxB,MAAM,SAAc;gBAClB,QAAQ,WAAW,MAAM;YAC3B;YAEA,8CAA8C;YAC9C,IAAI,OAAO;gBACT,OAAO,OAAO,GAAG;oBACf,GAAG,OAAO,OAAO;oBACjB,eAAe,CAAC,OAAO,EAAE,OAAO;gBAClC;gBACA,QAAQ,GAAG,CAAC,CAAC,wDAAwD,CAAC;YACxE;YAEA,MAAM,WAAW,MAAM,mHAAA,CAAA,UAAa,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,IAAI,EAAE;YAEzD,aAAa;YAEb,MAAM,OAAa,SAAS,IAAI;YAChC,QAAQ,GAAG,CACT,CAAC,qDAAqD,EAAE,IAAI,EAC5D;YAGF,2BAA2B;YAC3B,CAAA,GAAA,yHAAA,CAAA,gBAAa,AAAD,EAAE,IAAI;YAElB,OAAO;gBAAE,SAAS;gBAAM;YAAK;QAC/B,EAAE,OAAO,cAAc;YACrB,aAAa;YACb,MAAM,cAAc,2CAA2C;QACjE;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,CAAC,2CAA2C,EAAE,GAAG,CAAC,CAAC,EAAE;QAEnE,4CAA4C;QAC5C,IACE,SACA,OAAO,UAAU,YACjB,UAAU,SACV,CAAC,MAAM,IAAI,KAAK,gBAAgB,MAAM,IAAI,KAAK,cAAc,GAC7D;YACA,QAAQ,IAAI,CAAC,CAAC,6CAA6C,EAAE,IAAI;YAEjE,oDAAoD;YACpD,MAAM,oBAAoB,CAAA,GAAA,yHAAA,CAAA,oBAAiB,AAAD,EAAE,IAAI;YAChD,IAAI,mBAAmB;gBACrB,QAAQ,GAAG,CACT,CAAC,qDAAqD,EAAE,GAAG,cAAc,CAAC;gBAE5E,OAAO;oBAAE,SAAS;oBAAM,MAAM;gBAAkB;YAClD;QACF;QAEA,iEAAiE;QACjE,MAAM,aAAa;QACnB,IAAI,WAAW,QAAQ,IAAI,WAAW,QAAQ,CAAC,MAAM,KAAK,KAAK;YAC7D,QAAQ,GAAG,CAAC,CAAC,+CAA+C,EAAE,IAAI;YAClE,MAAM,kBAAwB;gBAC5B,IAAI;gBACJ,OAAO;gBACP,aAAa;gBACb,cAAc;gBACd,WAAW,IAAI;gBACf,WAAW,IAAI;gBACf,UAAU;oBACR,IAAI;oBACJ,UAAU,CAAC,WAAW,EAAE,GAAG,KAAK,CAAC,CAAC,IAAI;oBACtC,mBAAmB;oBACnB,eAAe;oBACf,gBAAgB;oBAChB,WAAW,IAAI;oBACf,WAAW,IAAI;oBACf,UAAU;gBACZ;gBACA,eAAe,EAAE;gBACjB,SAAS,EAAE;gBACX,OAAO,EAAE;gBACT,SAAS,EAAE;gBACX,cAAc,EAAE;gBAChB,YAAY,EAAE;gBACd,aAAa,EAAE;gBACf,aAAa,EAAE;gBACf,iBAAiB,EAAE;gBACnB,UAAU,EAAE;gBACZ,WAAW,EAAE;gBACb,UAAU,EAAE;gBACZ,eAAe,EAAE;gBACjB,aAAa,EAAE;gBACf,SAAS,EAAE;gBACX,eAAe,EAAE;gBACjB,cAAc,EAAE;gBAChB,kBAAkB,EAAE;gBACpB,UAAU,EAAE;YACd;YAEA,iCAAiC;YACjC,CAAA,GAAA,yHAAA,CAAA,gBAAa,AAAD,EAAE,IAAI;YAElB,OAAO;gBAAE,SAAS;gBAAM,MAAM;YAAgB;QAChD;QAEA,yDAAyD;QACzD,QAAQ,GAAG,CACT,CAAC,uDAAuD,EAAE,GAAG,aAAa,CAAC;QAE7E,MAAM,cAAoB;YACxB,IAAI;YACJ,OAAO;YACP,aAAa;YACb,cAAc;YACd,WAAW,IAAI;YACf,WAAW,IAAI;YACf,UAAU;gBACR,IAAI;gBACJ,UAAU,CAAC,WAAW,EAAE,GAAG,KAAK,CAAC,CAAC,IAAI;gBACtC,mBAAmB;gBACnB,eAAe;gBACf,gBAAgB;gBAChB,WAAW,IAAI;gBACf,WAAW,IAAI;gBACf,UAAU;YACZ;YACA,eAAe,EAAE;YACjB,SAAS,EAAE;YACX,OAAO,EAAE;YACT,SAAS,EAAE;YACX,cAAc,EAAE;YAChB,YAAY,EAAE;YACd,aAAa,EAAE;YACf,aAAa,EAAE;YACf,iBAAiB,EAAE;YACnB,UAAU,EAAE;YACZ,WAAW,EAAE;YACb,UAAU,EAAE;YACZ,eAAe,EAAE;YACjB,aAAa,EAAE;YACf,SAAS,EAAE;YACX,eAAe,EAAE;YACjB,cAAc,EAAE;YAChB,kBAAkB,EAAE;YACpB,UAAU,EAAE;QACd;QAEA,6BAA6B;QAC7B,CAAA,GAAA,yHAAA,CAAA,gBAAa,AAAD,EAAE,IAAI;QAElB,OAAO;YAAE,SAAS;YAAM,MAAM;QAAY;IAC5C;AACF;AAGO,eAAe,iBAAiB,OAAiB;IACtD,2BAA2B;IAC3B,MAAM,YAAY;WAAI,IAAI,IAAI;KAAS;IAEvC,yCAAyC;IACzC,MAAM,cAAsB,EAAE;IAC9B,MAAM,aAAuB,EAAE;IAE/B,UAAU,OAAO,CAAC,CAAC;QACjB,MAAM,aAAa,CAAA,GAAA,yHAAA,CAAA,oBAAiB,AAAD,EAAE;QACrC,IAAI,YAAY;YACd,YAAY,IAAI,CAAC;QACnB,OAAO;YACL,WAAW,IAAI,CAAC;QAClB;IACF;IAEA,gDAAgD;IAChD,IAAI,WAAW,MAAM,KAAK,GAAG;QAC3B,QAAQ,GAAG,CAAC,CAAC,IAAI,EAAE,UAAU,MAAM,CAAC,qBAAqB,CAAC;QAC1D,OAAO;YAAE,SAAS;YAAM,OAAO;QAAY;IAC7C;IAEA,uCAAuC;IACvC,IAAI;QACF,QAAQ,GAAG,CAAC,CAAC,eAAe,EAAE,WAAW,MAAM,CAAC,MAAM,CAAC;QAEvD,8EAA8E;QAC9E,MAAM,gBAAgB,WAAW,GAAG,CAAC,CAAC,KAAO,gBAAgB;QAC7D,MAAM,UAAU,MAAM,QAAQ,GAAG,CAAC;QAElC,yCAAyC;QACzC,MAAM,eAAe,QAClB,MAAM,CAAC,CAAC,SAAW,OAAO,OAAO,IAAI,OAAO,IAAI,EAChD,GAAG,CAAC,CAAC,SAAW,OAAO,IAAI;QAE9B,MAAM,WAAW;eAAI;eAAgB;SAAa;QAElD,OAAO;YAAE,SAAS;YAAM,OAAO;QAAS;IAC1C,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,OAAO;YACL,SAAS;YACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAChD,OAAO;QACT;IACF;AACF;AAGO,eAAe,iBAAiB,EAAU;IAC/C,IAAI;QACF,MAAM,WAAW,MAAM,mHAAA,CAAA,UAAa,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,GAAG,WAAW,CAAC;QAClE,MAAM,WAAW,SAAS,IAAI,EAAE,wCAAwC;QACxE,OAAO;YAAE,SAAS;YAAM;QAAS;IACnC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,+BAA+B;QAC7C,OAAO;YACL,SAAS;YACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAClD;IACF;AACF;AAGO,eAAe,WAAW,EAAU,EAAE,QAAuB;IAClE,IAAI;QACF,MAAM,WAAW,MAAM,mHAAA,CAAA,UAAa,CAAC,KAAK,CAAC,CAAC,OAAO,EAAE,IAAI,EAAE;QAC3D,MAAM,cAAoB,SAAS,IAAI;QAEvC,2EAA2E;QAC3E,MAAM,cAAc,0HAAA,CAAA,eAAY,CAAC,QAAQ,GAAG,IAAI;QAChD,IAAI,eAAe,YAAY,EAAE,KAAK,IAAI;YACxC,0HAAA,CAAA,eAAY,CAAC,QAAQ,GAAG,UAAU,CAAC;QACrC;QAEA,0CAA0C;QAC1C,CAAA,GAAA,yHAAA,CAAA,gBAAa,AAAD,EAAE,IAAI;QAElB,OAAO;YAAE,SAAS;YAAM,MAAM;QAAY;IAC5C,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uBAAuB;QACrC,OAAO;YACL,SAAS;YACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAClD;IACF;AACF;AAGO,eAAe,WAAW,EAAU;IACzC,IAAI;QACF,MAAM,mHAAA,CAAA,UAAa,CAAC,MAAM,CAAC,CAAC,OAAO,EAAE,IAAI;QAEzC,6CAA6C;QAC7C,MAAM,cAAc,0HAAA,CAAA,eAAY,CAAC,QAAQ,GAAG,IAAI;QAChD,IAAI,eAAe,YAAY,EAAE,KAAK,IAAI;YACxC,0HAAA,CAAA,eAAY,CAAC,QAAQ,GAAG,MAAM;QAChC;QAEA,8BAA8B;QAC9B,CAAA,GAAA,yHAAA,CAAA,uBAAoB,AAAD,EAAE;QAErB,OAAO;YAAE,SAAS;QAAK;IACzB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uBAAuB;QACrC,OAAO;YACL,SAAS;YACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAClD;IACF;AACF;AAGO,eAAe,qBAAqB,IAAU;IACnD,IAAI;QACF,kCAAkC;QAClC,IAAI,CAAC,MAAM;YACT,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,QAAQ;QAExB,iDAAiD;QACjD,MAAM,WAAW,MAAM,mHAAA,CAAA,UAAa,CAAC,GAAG,CACtC,gCACA,UACA;YACE,SAAS;gBACP,gBAAgB;YAClB;QACF;QAGF,MAAM,EAAE,OAAO,EAAE,GAAG,EAAE,GAAG,SAAS,IAAI;QAEtC,wCAAwC;QACxC,MAAM,mBAAmB,GAAG,IAAI,GAAG,EAAE,IAAI,OAAO,OAAO,IAAI;QAE3D,qCAAqC;QACrC,MAAM,cAAc,0HAAA,CAAA,eAAY,CAAC,QAAQ,GAAG,IAAI;QAChD,IAAI,eAAe,YAAY,EAAE,EAAE;YACjC,6DAA6D;YAC7D,IAAI,YAAY,QAAQ,EAAE;gBACxB,MAAM,cAAc;oBAClB,GAAG,WAAW;oBACd,UAAU;wBACR,GAAG,YAAY,QAAQ;wBACvB,mBAAmB;oBACrB;gBACF;gBAEA,iBAAiB;gBACjB,0HAAA,CAAA,eAAY,CAAC,QAAQ,GAAG,UAAU,CAAC;gBAEnC,iBAAiB;gBACjB,CAAA,GAAA,yHAAA,CAAA,gBAAa,AAAD,EAAE,YAAY,EAAE,EAAE;gBAE9B,QAAQ,GAAG,CACT;YAEJ;YAEA,2DAA2D;YAC3D,gBAAgB,YAAY,EAAE,EAC3B,IAAI,CAAC,CAAC;gBACL,IAAI,aAAa,OAAO,IAAI,aAAa,IAAI,EAAE;oBAC7C,wCAAwC;oBACxC,MAAM,aAAa,aAAa,IAAI;oBACpC,IAAI,WAAW,QAAQ,IAAI,WAAW,QAAQ,CAAC,iBAAiB,EAAE;wBAChE,WAAW,QAAQ,CAAC,iBAAiB,GAAG,GAAG,WAAW,QAAQ,CAAC,iBAAiB,CAAC,GAAG,EAAE,IAAI,OAAO,OAAO,IAAI;oBAC9G;oBAEA,8CAA8C;oBAC9C,0HAAA,CAAA,eAAY,CAAC,QAAQ,GAAG,UAAU,CAAC;oBAEnC,iBAAiB;oBACjB,CAAA,GAAA,yHAAA,CAAA,gBAAa,AAAD,EAAE,YAAY,EAAE,EAAE;oBAE9B,QAAQ,GAAG,CACT;gBAEJ;YACF,GACC,KAAK,CAAC,CAAC;gBACN,QAAQ,KAAK,CAAC,qCAAqC;YACnD,iDAAiD;YACnD;QACJ;QAEA,wDAAwD;QACxD,OAAO;YAAE,SAAS;YAAM;YAAS,KAAK;QAAiB;IACzD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kCAAkC;QAChD,OAAO;YACL,SAAS;YACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAClD;IACF;AACF;AAGO,eAAe,iBAAiB,IAAU;IAC/C,IAAI;QACF,kCAAkC;QAClC,IAAI,CAAC,MAAM;YACT,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,QAAQ;QAExB,iDAAiD;QACjD,MAAM,WAAW,MAAM,mHAAA,CAAA,UAAa,CAAC,GAAG,CACtC,4BACA,UACA;YACE,SAAS;gBACP,gBAAgB;YAClB;QACF;QAGF,MAAM,EAAE,OAAO,EAAE,GAAG,EAAE,GAAG,SAAS,IAAI;QAEtC,wCAAwC;QACxC,MAAM,mBAAmB,GAAG,IAAI,GAAG,EAAE,IAAI,OAAO,OAAO,IAAI;QAE3D,qCAAqC;QACrC,MAAM,cAAc,0HAAA,CAAA,eAAY,CAAC,QAAQ,GAAG,IAAI;QAChD,IAAI,eAAe,YAAY,EAAE,EAAE;YACjC,6DAA6D;YAC7D,IAAI,YAAY,QAAQ,EAAE;gBACxB,MAAM,cAAc;oBAClB,GAAG,WAAW;oBACd,UAAU;wBACR,GAAG,YAAY,QAAQ;wBACvB,aAAa;oBACf;gBACF;gBAEA,iBAAiB;gBACjB,0HAAA,CAAA,eAAY,CAAC,QAAQ,GAAG,UAAU,CAAC;gBAEnC,iBAAiB;gBACjB,CAAA,GAAA,yHAAA,CAAA,gBAAa,AAAD,EAAE,YAAY,EAAE,EAAE;gBAE9B,QAAQ,GAAG,CAAC;YACd;YAEA,2DAA2D;YAC3D,gBAAgB,YAAY,EAAE,EAC3B,IAAI,CAAC,CAAC;gBACL,IAAI,aAAa,OAAO,IAAI,aAAa,IAAI,EAAE;oBAC7C,wCAAwC;oBACxC,MAAM,aAAa,aAAa,IAAI;oBACpC,IAAI,WAAW,QAAQ,IAAI,WAAW,QAAQ,CAAC,WAAW,EAAE;wBAC1D,WAAW,QAAQ,CAAC,WAAW,GAAG,GAAG,WAAW,QAAQ,CAAC,WAAW,CAAC,GAAG,EAAE,IAAI,OAAO,OAAO,IAAI;oBAClG;oBAEA,8CAA8C;oBAC9C,0HAAA,CAAA,eAAY,CAAC,QAAQ,GAAG,UAAU,CAAC;oBAEnC,iBAAiB;oBACjB,CAAA,GAAA,yHAAA,CAAA,gBAAa,AAAD,EAAE,YAAY,EAAE,EAAE;oBAE9B,QAAQ,GAAG,CACT;gBAEJ;YACF,GACC,KAAK,CAAC,CAAC;gBACN,QAAQ,KAAK,CAAC,qCAAqC;YACnD,iDAAiD;YACnD;QACJ;QAEA,wDAAwD;QACxD,OAAO;YAAE,SAAS;YAAM;YAAS,KAAK;QAAiB;IACzD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8BAA8B;QAC5C,OAAO;YACL,SAAS;YACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAClD;IACF;AACF;AAGO,eAAe,oBAAoB,QAMzC;IACC,IAAI;QACF,MAAM,cAAc,0HAAA,CAAA,eAAY,CAAC,QAAQ,GAAG,IAAI;QAChD,IAAI,CAAC,eAAe,CAAC,YAAY,EAAE,EAAE;YACnC,MAAM,IAAI,MAAM;QAClB;QAEA,sDAAsD;QACtD,yCAAyC;QACzC,IAAI,YAAY,QAAQ,EAAE;YACxB,2CAA2C;YAC3C,IAAI,gBAAgB,YAAY,QAAQ,CAAC,MAAM;YAC/C,IAAI,SAAS,MAAM,EAAE;gBACnB,sCAAsC;gBACtC,IACE,SAAS,MAAM,KAAK,UACpB,SAAS,MAAM,KAAK,YACpB,SAAS,MAAM,KAAK,SACpB;oBACA,gBAAgB,SAAS,MAAM,EAAS,yCAAyC;gBACnF;YACF;YAEA,MAAM,2BAA2B;gBAC/B,GAAG,YAAY,QAAQ;gBACvB,UAAU,SAAS,QAAQ,IAAI,YAAY,QAAQ,CAAC,QAAQ;gBAC5D,QAAQ;gBACR,aAAa,SAAS,WAAW,IAAI,YAAY,QAAQ,CAAC,WAAW;gBACrE,KAAK,SAAS,GAAG,IAAI,YAAY,QAAQ,CAAC,GAAG;YAC/C;YAEA,MAAM,wBAAwB;gBAC5B,GAAG,WAAW;gBACd,UAAU;YACZ;YAEA,8BAA8B;YAC9B,0HAAA,CAAA,eAAY,CAAC,QAAQ,GAAG,UAAU,CAAC;YAEnC,iBAAiB;YACjB,CAAA,GAAA,yHAAA,CAAA,gBAAa,AAAD,EAAE,YAAY,EAAE,EAAE;YAE9B,QAAQ,GAAG,CAAC;QACd;QAEA,4CAA4C;QAC5C,MAAM,WAAW,MAAM,mHAAA,CAAA,UAAa,CAAC,GAAG,CACtC,CAAC,uBAAuB,CAAC,EACzB;QAEF,MAAM,cAAc,SAAS,IAAI;QAEjC,oEAAoE;QACpE,4CAA4C;QAC5C,gBAAgB,YAAY,EAAE,EAC3B,IAAI,CAAC,CAAC;YACL,IAAI,aAAa,OAAO,IAAI,aAAa,IAAI,EAAE;gBAC7C,4CAA4C;gBAC5C,0HAAA,CAAA,eAAY,CAAC,QAAQ,GAAG,UAAU,CAAC,aAAa,IAAI;gBAEpD,iBAAiB;gBACjB,CAAA,GAAA,yHAAA,CAAA,gBAAa,AAAD,EAAE,YAAY,EAAE,EAAE,aAAa,IAAI;gBAE/C,QAAQ,GAAG,CACT;YAEJ;QACF,GACC,KAAK,CAAC,CAAC;YACN,QAAQ,KAAK,CAAC,qCAAqC;QACnD,iDAAiD;QACnD;QAEF,yCAAyC;QACzC,OAAO;YACL,SAAS;YACT,MAAM;YACN,uDAAuD;YACvD,aAAa;gBACX,UAAU,SAAS,QAAQ;gBAC3B,QAAQ,SAAS,MAAM;gBACvB,aAAa,SAAS,WAAW;gBACjC,KAAK,SAAS,GAAG;YACnB;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kCAAkC;QAChD,OAAO;YACL,SAAS;YACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAClD;IACF;AACF;AAGO,eAAe,WAAW,WAAmB;IAClD,IAAI;QACF,kDAAkD;QAClD,MAAM,UAAU,6BAA6B,IAAI,CAAC;QAElD,6CAA6C;QAC7C,MAAM,UAAU,UACZ;YAAE,OAAO;QAAY,IACrB;YAAE,aAAa;QAAY;QAE/B,MAAM,WAAW,MAAM,mHAAA,CAAA,UAAa,CAAC,IAAI,CAAC,iBAAiB;QAE3D,uBAAuB;QACvB,IAAI,SAAS,IAAI,IAAI,SAAS,IAAI,CAAC,EAAE,EAAE;YACrC,CAAA,GAAA,yHAAA,CAAA,gBAAa,AAAD,EAAE,SAAS,IAAI,CAAC,EAAE,EAAE,SAAS,IAAI;QAC/C;QAEA,OAAO;YAAE,SAAS;YAAM,MAAM,SAAS,IAAI;QAAC;IAC9C,EAAE,OAAO,OAAO;QACd,2CAA2C;QAC3C,MAAM,aAAa;QACnB,IAAI,WAAW,QAAQ,IAAI,WAAW,QAAQ,CAAC,MAAM,KAAK,KAAK;YAC7D,wDAAwD;YACxD,MAAM,gBAAgB,6BAA6B,IAAI,CAAC;YACxD,QAAQ,GAAG,CACT,CAAC,8BAA8B,EAAE,gBAAgB,UAAU,gBAAgB,EAAE,EAAE,aAAa;YAE9F,qFAAqF;YACrF,OAAO;gBAAE,SAAS;YAAM;QAC1B;QAEA,QAAQ,KAAK,CAAC,uBAAuB;QACrC,OAAO;YACL,SAAS;YACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAClD;IACF;AACF;AAGO,eAAe,wBAAwB,WAAmB;IAC/D,OAAO,WAAW;AACpB", "debugId": null}}, {"offset": {"line": 772, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/src/stores/authStore.ts"], "sourcesContent": ["import { create } from \"zustand\";\nimport { DeviceType, User, UserInfo } from \"@/types/base\";\nimport { persist, createJSONStorage } from \"zustand/middleware\";\nimport {\n  login as loginAction,\n  logout as logoutAction,\n} from \"@/actions/auth.action\";\nimport { getUserDataById } from \"@/actions/user.action\";\n\nconst storage = {\n  getItem: (name: string): string | null => {\n    if (typeof window === \"undefined\") return null;\n    return localStorage.getItem(name);\n  },\n  setItem: (name: string, value: string): void => {\n    if (typeof window === \"undefined\") return;\n    localStorage.setItem(name, value);\n  },\n  removeItem: (name: string): void => {\n    if (typeof window === \"undefined\") return;\n    localStorage.removeItem(name);\n  },\n};\n\ninterface UserWithInfo extends User {\n  userInfo: UserInfo;\n}\n\ninterface AuthState {\n  user: UserWithInfo | null;\n  accessToken: string | null;\n  refreshToken: string | null;\n  isAuthenticated: boolean;\n  isLoading: boolean;\n  deviceId: string | null;\n  _hasHydrated: boolean;\n  setAuth: (user: User, accessToken: string) => void;\n  login: (\n    identifier: string,\n    password: string,\n    deviceName: string,\n    deviceType: DeviceType,\n  ) => Promise<boolean>;\n  updateUser: (user: Partial<User>) => void;\n  logout: () => Promise<boolean>;\n  setTokens: (accessToken: string, refreshToken: string) => void;\n  setLoading: (loading: boolean) => void;\n  setHasHydrated: (state: boolean) => void;\n}\n\nexport const useAuthStore = create<AuthState>()(\n  persist(\n    (set) => ({\n      user: null,\n      accessToken: null,\n      refreshToken: null,\n      isAuthenticated: false,\n      isLoading: false,\n      // Removed socket and socketId to avoid storing them in localStorage\n      deviceId: null,\n      _hasHydrated: false,\n      setHasHydrated: (state) => set({ _hasHydrated: state }),\n      setAuth: (user: User, accessToken: string) =>\n        set({\n          accessToken,\n          user: user as UserWithInfo,\n          isAuthenticated: !!accessToken,\n          isLoading: false,\n        }),\n      login: async (\n        identifier: string,\n        password: string,\n        deviceName: string,\n        deviceType: DeviceType,\n      ) => {\n        try {\n          set({ isLoading: true });\n          const result = await loginAction(\n            identifier,\n            password,\n            deviceName,\n            deviceType,\n          );\n\n          if (!result.success) return false;\n\n          // First set the tokens and basic user data\n          console.log(\n            \"Login successful, setting tokens. accessToken:\",\n            result.accessToken ? \"Token exists\" : \"No token\",\n          );\n\n          // Lưu refreshToken vào state nhưng không lưu vào localStorage\n          // (partialize sẽ loại bỏ refreshToken khi lưu vào localStorage)\n          console.log(\"Login successful, saving tokens and deviceId to store\", {\n            hasAccessToken: !!result.accessToken,\n            hasRefreshToken: !!result.refreshToken,\n            hasDeviceId: !!result.deviceId,\n          });\n\n          set({\n            accessToken: result.accessToken,\n            refreshToken: result.refreshToken,\n            deviceId: result.deviceId,\n            isAuthenticated: true,\n            isLoading: false,\n          });\n\n          // Kiểm tra xem các giá trị đã được lưu đúng chưa\n          const state = useAuthStore.getState();\n          console.log(\"After login, state check:\", {\n            hasAccessToken: !!state.accessToken,\n            hasRefreshToken: !!state.refreshToken,\n            hasDeviceId: !!state.deviceId,\n            isAuthenticated: state.isAuthenticated,\n          });\n\n          // Socket sẽ được khởi tạo tự động bởi SocketProvider\n          // Then try to get additional user data\n          try {\n            const userData = await getUserDataById(result.user.userId);\n            if (userData.success && userData.user) {\n              set({\n                user: userData.user as UserWithInfo,\n                isLoading: false,\n              });\n            }\n          } catch {\n            // Set basic user data if additional data fetch fails\n            set({\n              user: result.user as UserWithInfo,\n              isLoading: false,\n            });\n          }\n          return true;\n        } catch {\n          return false;\n        } finally {\n          set({ isLoading: false });\n        }\n      },\n      setLoading: (loading: boolean) => set({ isLoading: loading }),\n      updateUser: (updatedUser) =>\n        set((state) => {\n          if (!state.user) return { user: null };\n\n          // Merge userInfo properly if it exists in updatedUser\n          const mergedUserInfo = updatedUser.userInfo\n            ? { ...state.user.userInfo, ...updatedUser.userInfo }\n            : state.user.userInfo;\n\n          return {\n            user: {\n              ...state.user,\n              ...updatedUser,\n              userInfo: mergedUserInfo,\n            },\n          };\n        }),\n      logout: async () => {\n        try {\n          // Socket sẽ được ngắt kết nối tự động khi accessToken thay đổi\n          await logoutAction();\n        } catch {\n          // Ignore errors from the API\n        } finally {\n          // Always reset store state to ensure UI updates\n          // Xóa cả refreshToken khỏi store\n          set({\n            user: null,\n            accessToken: null,\n            refreshToken: null,\n            deviceId: null,\n            isAuthenticated: false,\n            isLoading: false,\n          });\n        }\n\n        return true; // Always return true to ensure UI updates\n      },\n      setTokens: (accessToken, refreshToken) => {\n        console.log(\n          \"setTokens called with accessToken:\",\n          accessToken ? `${accessToken.substring(0, 10)}...` : \"No token\",\n          \"and refreshToken:\",\n          refreshToken ? `${refreshToken.substring(0, 10)}...` : \"No token\",\n        );\n\n        if (!accessToken) {\n          console.error(\"Attempted to set tokens with null/empty accessToken\");\n          return;\n        }\n\n        if (!refreshToken) {\n          console.error(\"Attempted to set tokens with null/empty refreshToken\");\n          return;\n        }\n\n        // Lưu refreshToken vào state nhưng không lưu vào localStorage\n        // (được xử lý bởi partialize)\n        set({\n          accessToken,\n          refreshToken,\n          isAuthenticated: true,\n          // Keep existing deviceId\n        });\n\n        // Kiểm tra xem tokens đã được lưu đúng chưa\n        const state = useAuthStore.getState();\n        console.log(\"After setTokens, state check:\", {\n          hasAccessToken: !!state.accessToken,\n          hasRefreshToken: !!state.refreshToken,\n          isAuthenticated: state.isAuthenticated,\n          accessTokenPrefix: state.accessToken\n            ? state.accessToken.substring(0, 10) + \"...\"\n            : \"none\",\n          refreshTokenPrefix: state.refreshToken\n            ? state.refreshToken.substring(0, 10) + \"...\"\n            : \"none\",\n        });\n\n        // Socket sẽ được cập nhật tự động bởi SocketProvider khi accessToken thay đổi\n      },\n    }),\n\n    {\n      name: \"auth-storage\",\n      storage: createJSONStorage(() => storage),\n      partialize: (state) => {\n        // Ghi log trạng thái trước khi lưu vào localStorage\n        console.log(\"Persisting auth state to localStorage:\", {\n          hasAccessToken: !!state.accessToken,\n          hasRefreshToken: !!state.refreshToken,\n          hasDeviceId: !!state.deviceId,\n          isAuthenticated: state.isAuthenticated,\n        });\n\n        // Lưu refreshToken vào localStorage nhưng được mã hóa đơn giản\n        // Lưu ý: Đây không phải là mã hóa an toàn, chỉ là giải pháp tạm thời\n        // Trong môi trường sản xuất, nên sử dụng cookie httpOnly hoặc các giải pháp bảo mật hơn\n        let encodedRefreshToken: string | null = null;\n        if (state.refreshToken) {\n          try {\n            // Mã hóa đơn giản bằng cách đảo ngược và base64\n            encodedRefreshToken = btoa(\n              state.refreshToken.split(\"\").reverse().join(\"\"),\n            );\n          } catch (error) {\n            console.error(\"Error encoding refresh token:\", error);\n          }\n        }\n\n        return {\n          accessToken: state.accessToken,\n          refreshToken: encodedRefreshToken, // Lưu refreshToken đã được mã hóa\n          isAuthenticated: state.isAuthenticated,\n          user: state.user,\n          deviceId: state.deviceId, // Đảm bảo deviceId được lưu\n        };\n      },\n      onRehydrateStorage: () => (state) => {\n        if (state) {\n          // Giải mã refreshToken nếu có\n          if (state.refreshToken) {\n            try {\n              // Giải mã bằng cách đảo ngược quá trình mã hóa\n              const decodedRefreshToken = atob(state.refreshToken)\n                .split(\"\")\n                .reverse()\n                .join(\"\");\n              state.refreshToken = decodedRefreshToken;\n\n              console.log(\"Successfully decoded refresh token from storage\", {\n                hasRefreshToken: true,\n                refreshTokenPrefix:\n                  decodedRefreshToken.substring(0, 10) + \"...\",\n              });\n            } catch (error) {\n              console.error(\"Error decoding refresh token:\", error);\n              // Nếu không thể giải mã, xóa refreshToken để tránh lỗi\n              state.refreshToken = null;\n            }\n          } else {\n            console.log(\"No refresh token found in storage\");\n          }\n\n          state.setHasHydrated(true);\n\n          // Kiểm tra tính hợp lệ của trạng thái sau khi khôi phục\n          console.log(\"Auth state after rehydration:\", {\n            hasAccessToken: !!state.accessToken,\n            hasRefreshToken: !!state.refreshToken,\n            hasDeviceId: !!state.deviceId,\n            isAuthenticated: state.isAuthenticated,\n          });\n        }\n      },\n    },\n  ),\n);\n"], "names": [], "mappings": ";;;AAAA;AAEA;AACA;AAIA;;;;;AAEA,MAAM,UAAU;IACd,SAAS,CAAC;QACR,wCAAmC,OAAO;;IAE5C;IACA,SAAS,CAAC,MAAc;QACtB,wCAAmC;;IAErC;IACA,YAAY,CAAC;QACX,wCAAmC;;IAErC;AACF;AA4BO,MAAM,eAAe,CAAA,GAAA,wIAAA,CAAA,SAAM,AAAD,IAC/B,CAAA,GAAA,6IAAA,CAAA,UAAO,AAAD,EACJ,CAAC,MAAQ,CAAC;QACR,MAAM;QACN,aAAa;QACb,cAAc;QACd,iBAAiB;QACjB,WAAW;QACX,oEAAoE;QACpE,UAAU;QACV,cAAc;QACd,gBAAgB,CAAC,QAAU,IAAI;gBAAE,cAAc;YAAM;QACrD,SAAS,CAAC,MAAY,cACpB,IAAI;gBACF;gBACA,MAAM;gBACN,iBAAiB,CAAC,CAAC;gBACnB,WAAW;YACb;QACF,OAAO,OACL,YACA,UACA,YACA;YAEA,IAAI;gBACF,IAAI;oBAAE,WAAW;gBAAK;gBACtB,MAAM,SAAS,MAAM,CAAA,GAAA,gIAAA,CAAA,QAAW,AAAD,EAC7B,YACA,UACA,YACA;gBAGF,IAAI,CAAC,OAAO,OAAO,EAAE,OAAO;gBAE5B,2CAA2C;gBAC3C,QAAQ,GAAG,CACT,kDACA,OAAO,WAAW,GAAG,iBAAiB;gBAGxC,8DAA8D;gBAC9D,gEAAgE;gBAChE,QAAQ,GAAG,CAAC,yDAAyD;oBACnE,gBAAgB,CAAC,CAAC,OAAO,WAAW;oBACpC,iBAAiB,CAAC,CAAC,OAAO,YAAY;oBACtC,aAAa,CAAC,CAAC,OAAO,QAAQ;gBAChC;gBAEA,IAAI;oBACF,aAAa,OAAO,WAAW;oBAC/B,cAAc,OAAO,YAAY;oBACjC,UAAU,OAAO,QAAQ;oBACzB,iBAAiB;oBACjB,WAAW;gBACb;gBAEA,iDAAiD;gBACjD,MAAM,QAAQ,aAAa,QAAQ;gBACnC,QAAQ,GAAG,CAAC,6BAA6B;oBACvC,gBAAgB,CAAC,CAAC,MAAM,WAAW;oBACnC,iBAAiB,CAAC,CAAC,MAAM,YAAY;oBACrC,aAAa,CAAC,CAAC,MAAM,QAAQ;oBAC7B,iBAAiB,MAAM,eAAe;gBACxC;gBAEA,qDAAqD;gBACrD,uCAAuC;gBACvC,IAAI;oBACF,MAAM,WAAW,MAAM,CAAA,GAAA,gIAAA,CAAA,kBAAe,AAAD,EAAE,OAAO,IAAI,CAAC,MAAM;oBACzD,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE;wBACrC,IAAI;4BACF,MAAM,SAAS,IAAI;4BACnB,WAAW;wBACb;oBACF;gBACF,EAAE,OAAM;oBACN,qDAAqD;oBACrD,IAAI;wBACF,MAAM,OAAO,IAAI;wBACjB,WAAW;oBACb;gBACF;gBACA,OAAO;YACT,EAAE,OAAM;gBACN,OAAO;YACT,SAAU;gBACR,IAAI;oBAAE,WAAW;gBAAM;YACzB;QACF;QACA,YAAY,CAAC,UAAqB,IAAI;gBAAE,WAAW;YAAQ;QAC3D,YAAY,CAAC,cACX,IAAI,CAAC;gBACH,IAAI,CAAC,MAAM,IAAI,EAAE,OAAO;oBAAE,MAAM;gBAAK;gBAErC,sDAAsD;gBACtD,MAAM,iBAAiB,YAAY,QAAQ,GACvC;oBAAE,GAAG,MAAM,IAAI,CAAC,QAAQ;oBAAE,GAAG,YAAY,QAAQ;gBAAC,IAClD,MAAM,IAAI,CAAC,QAAQ;gBAEvB,OAAO;oBACL,MAAM;wBACJ,GAAG,MAAM,IAAI;wBACb,GAAG,WAAW;wBACd,UAAU;oBACZ;gBACF;YACF;QACF,QAAQ;YACN,IAAI;gBACF,+DAA+D;gBAC/D,MAAM,CAAA,GAAA,gIAAA,CAAA,SAAY,AAAD;YACnB,EAAE,OAAM;YACN,6BAA6B;YAC/B,SAAU;gBACR,gDAAgD;gBAChD,iCAAiC;gBACjC,IAAI;oBACF,MAAM;oBACN,aAAa;oBACb,cAAc;oBACd,UAAU;oBACV,iBAAiB;oBACjB,WAAW;gBACb;YACF;YAEA,OAAO,MAAM,0CAA0C;QACzD;QACA,WAAW,CAAC,aAAa;YACvB,QAAQ,GAAG,CACT,sCACA,cAAc,GAAG,YAAY,SAAS,CAAC,GAAG,IAAI,GAAG,CAAC,GAAG,YACrD,qBACA,eAAe,GAAG,aAAa,SAAS,CAAC,GAAG,IAAI,GAAG,CAAC,GAAG;YAGzD,IAAI,CAAC,aAAa;gBAChB,QAAQ,KAAK,CAAC;gBACd;YACF;YAEA,IAAI,CAAC,cAAc;gBACjB,QAAQ,KAAK,CAAC;gBACd;YACF;YAEA,8DAA8D;YAC9D,8BAA8B;YAC9B,IAAI;gBACF;gBACA;gBACA,iBAAiB;YAEnB;YAEA,4CAA4C;YAC5C,MAAM,QAAQ,aAAa,QAAQ;YACnC,QAAQ,GAAG,CAAC,iCAAiC;gBAC3C,gBAAgB,CAAC,CAAC,MAAM,WAAW;gBACnC,iBAAiB,CAAC,CAAC,MAAM,YAAY;gBACrC,iBAAiB,MAAM,eAAe;gBACtC,mBAAmB,MAAM,WAAW,GAChC,MAAM,WAAW,CAAC,SAAS,CAAC,GAAG,MAAM,QACrC;gBACJ,oBAAoB,MAAM,YAAY,GAClC,MAAM,YAAY,CAAC,SAAS,CAAC,GAAG,MAAM,QACtC;YACN;QAEA,8EAA8E;QAChF;IACF,CAAC,GAED;IACE,MAAM;IACN,SAAS,CAAA,GAAA,6IAAA,CAAA,oBAAiB,AAAD,EAAE,IAAM;IACjC,YAAY,CAAC;QACX,oDAAoD;QACpD,QAAQ,GAAG,CAAC,0CAA0C;YACpD,gBAAgB,CAAC,CAAC,MAAM,WAAW;YACnC,iBAAiB,CAAC,CAAC,MAAM,YAAY;YACrC,aAAa,CAAC,CAAC,MAAM,QAAQ;YAC7B,iBAAiB,MAAM,eAAe;QACxC;QAEA,+DAA+D;QAC/D,qEAAqE;QACrE,wFAAwF;QACxF,IAAI,sBAAqC;QACzC,IAAI,MAAM,YAAY,EAAE;YACtB,IAAI;gBACF,gDAAgD;gBAChD,sBAAsB,KACpB,MAAM,YAAY,CAAC,KAAK,CAAC,IAAI,OAAO,GAAG,IAAI,CAAC;YAEhD,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,iCAAiC;YACjD;QACF;QAEA,OAAO;YACL,aAAa,MAAM,WAAW;YAC9B,cAAc;YACd,iBAAiB,MAAM,eAAe;YACtC,MAAM,MAAM,IAAI;YAChB,UAAU,MAAM,QAAQ;QAC1B;IACF;IACA,oBAAoB,IAAM,CAAC;YACzB,IAAI,OAAO;gBACT,8BAA8B;gBAC9B,IAAI,MAAM,YAAY,EAAE;oBACtB,IAAI;wBACF,+CAA+C;wBAC/C,MAAM,sBAAsB,KAAK,MAAM,YAAY,EAChD,KAAK,CAAC,IACN,OAAO,GACP,IAAI,CAAC;wBACR,MAAM,YAAY,GAAG;wBAErB,QAAQ,GAAG,CAAC,mDAAmD;4BAC7D,iBAAiB;4BACjB,oBACE,oBAAoB,SAAS,CAAC,GAAG,MAAM;wBAC3C;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,iCAAiC;wBAC/C,uDAAuD;wBACvD,MAAM,YAAY,GAAG;oBACvB;gBACF,OAAO;oBACL,QAAQ,GAAG,CAAC;gBACd;gBAEA,MAAM,cAAc,CAAC;gBAErB,wDAAwD;gBACxD,QAAQ,GAAG,CAAC,iCAAiC;oBAC3C,gBAAgB,CAAC,CAAC,MAAM,WAAW;oBACnC,iBAAiB,CAAC,CAAC,MAAM,YAAY;oBACrC,aAAa,CAAC,CAAC,MAAM,QAAQ;oBAC7B,iBAAiB,MAAM,eAAe;gBACxC;YACF;QACF;AACF", "debugId": null}}, {"offset": {"line": 1008, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/src/lib/axios.ts"], "sourcesContent": ["import axios, {\r\n  AxiosError,\r\n  AxiosInstance,\r\n  AxiosRequestConfig,\r\n  InternalAxiosRequestConfig,\r\n} from \"axios\";\r\nimport { useAuthStore } from \"@/stores/authStore\";\r\n\r\nconst NEXT_PUBLIC_BACKEND_URL = process.env.NEXT_PUBLIC_BACKEND_URL;\r\n\r\n// Token refresh state management\r\nlet isRefreshing = false;\r\nlet refreshSubscribers: ((token: string) => void)[] = [];\r\n\r\n// Define response types for better type safety\r\ninterface RefreshTokenResponse {\r\n  accessToken: string;\r\n  device?: {\r\n    id: string;\r\n    name: string;\r\n    type: string;\r\n  };\r\n}\r\n\r\n// Create a base axios instance with common configuration\r\nconst createBaseAxiosInstance = (): AxiosInstance => {\r\n  return axios.create({\r\n    baseURL: NEXT_PUBLIC_BACKEND_URL,\r\n    headers: { \"Content-Type\": \"application/json\" },\r\n    timeout: 15000, // 15 seconds timeout\r\n  });\r\n};\r\n\r\n// Create an axios instance specifically for refresh token operations\r\nexport const refreshTokenAxios = createBaseAxiosInstance();\r\n\r\n// Add network error handling for the refresh token axios instance\r\nrefreshTokenAxios.interceptors.response.use(\r\n  (response) => response,\r\n  (error: AxiosError) => {\r\n    if (error.code === \"ECONNABORTED\") {\r\n      return Promise.reject(new Error(\"Request timeout during token refresh\"));\r\n    }\r\n\r\n    if (!error.response) {\r\n      return Promise.reject(new Error(\"Network error during token refresh\"));\r\n    }\r\n\r\n    return Promise.reject(error);\r\n  },\r\n);\r\n\r\n// Create an axios instance with an optional token\r\nexport const createAxiosInstance = (token?: string): AxiosInstance => {\r\n  const instance = createBaseAxiosInstance();\r\n\r\n  // Add token to headers if provided\r\n  if (token && token.trim() !== \"\") {\r\n    instance.defaults.headers.common[\"Authorization\"] = `Bearer ${token}`;\r\n  }\r\n\r\n  // Add network error handling\r\n  instance.interceptors.response.use(\r\n    (response) => response,\r\n    (error: AxiosError) => {\r\n      if (error.code === \"ECONNABORTED\") {\r\n        return Promise.reject(new Error(\"Request timeout. Please try again.\"));\r\n      }\r\n\r\n      if (!error.response) {\r\n        return Promise.reject(\r\n          new Error(\"Network error. Please check your connection.\"),\r\n        );\r\n      }\r\n\r\n      return Promise.reject(error);\r\n    },\r\n  );\r\n\r\n  return instance;\r\n};\r\n\r\n// Main axios instance with full authentication handling\r\nconst axiosInstance = createBaseAxiosInstance();\r\n\r\n// Add token to all requests\r\naxiosInstance.interceptors.request.use(\r\n  (config: InternalAxiosRequestConfig) => {\r\n    try {\r\n      const accessToken = useAuthStore.getState().accessToken;\r\n\r\n      if (accessToken) {\r\n        config.headers = config.headers || {};\r\n        config.headers.Authorization = `Bearer ${accessToken}`;\r\n      }\r\n      return config;\r\n    } catch (error) {\r\n      console.error(\"Error in axios request interceptor:\", error);\r\n      return config;\r\n    }\r\n  },\r\n  (error) => Promise.reject(error),\r\n);\r\n\r\n// Function to add a request to the refresh token subscribers queue\r\nconst subscribeTokenRefresh = (callback: (token: string) => void) => {\r\n  refreshSubscribers.push(callback);\r\n};\r\n\r\n// Function to notify all subscribers that the token has been refreshed\r\nconst onTokenRefreshed = (token: string) => {\r\n  refreshSubscribers.forEach((callback) => callback(token));\r\n  refreshSubscribers = [];\r\n};\r\n\r\n// Function to refresh the authentication token\r\nconst refreshAuthToken = async (): Promise<string> => {\r\n  try {\r\n    const authState = useAuthStore.getState();\r\n    const refreshToken = authState.refreshToken;\r\n    const deviceId = authState.deviceId;\r\n\r\n    console.log(\"Starting token refresh process\");\r\n    console.log(\r\n      \"Current refresh token:\",\r\n      refreshToken ? `${refreshToken.substring(0, 10)}...` : \"none\",\r\n    );\r\n    console.log(\"Current device ID:\", deviceId || \"none\");\r\n\r\n    // Kiểm tra kỹ lưỡng refreshToken và deviceId\r\n    if (!refreshToken || refreshToken.trim() === \"\") {\r\n      console.error(\"Cannot refresh token: No refresh token available\");\r\n      throw new Error(\"No refresh token available\");\r\n    }\r\n\r\n    if (!deviceId || deviceId.trim() === \"\") {\r\n      console.error(\"Cannot refresh token: No device ID available\");\r\n      throw new Error(\"No device ID available\");\r\n    }\r\n\r\n    // Thêm timeout để tránh chờ quá lâu\r\n    const controller = new AbortController();\r\n    const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 seconds timeout\r\n\r\n    try {\r\n      const response = await refreshTokenAxios.post<RefreshTokenResponse>(\r\n        \"/auth/refresh\",\r\n        {\r\n          refreshToken,\r\n          deviceId,\r\n        },\r\n        {\r\n          signal: controller.signal,\r\n        },\r\n      );\r\n\r\n      clearTimeout(timeoutId);\r\n      console.log(\"Refresh token response:\", response.data);\r\n\r\n      if (!response.data || !response.data.accessToken) {\r\n        console.error(\"Invalid response from refresh token API\");\r\n        throw new Error(\"Invalid response from refresh token API\");\r\n      }\r\n\r\n      const { accessToken } = response.data;\r\n\r\n      // Keep the same refreshToken since backend doesn't return a new one\r\n      useAuthStore.getState().setTokens(accessToken, refreshToken);\r\n\r\n      console.log(\r\n        `Token refresh completed successfully. New token: ${accessToken.substring(0, 10)}...`,\r\n      );\r\n\r\n      return accessToken;\r\n    } catch (requestError) {\r\n      clearTimeout(timeoutId);\r\n      throw requestError;\r\n    }\r\n  } catch (error) {\r\n    console.error(\r\n      \"Error during token refresh:\",\r\n      error instanceof Error ? error.message : \"Unknown error\",\r\n    );\r\n\r\n    // Xử lý các loại lỗi khác nhau\r\n    if (axios.isAxiosError(error)) {\r\n      if (error.response?.status === 403) {\r\n        // Forbidden - e.g., refresh token blacklisted\r\n        console.log(\r\n          \"Logging out immediately due to forbidden error during token refresh\",\r\n        );\r\n        await useAuthStore.getState().logout();\r\n      } else if (error.response?.status === 401) {\r\n        // Unauthorized\r\n        console.log(\r\n          \"Logging out due to unauthorized error during token refresh\",\r\n        );\r\n        await useAuthStore.getState().logout();\r\n      } else if (error.code === \"ERR_CANCELED\") {\r\n        console.log(\"Token refresh request was aborted due to timeout\");\r\n      }\r\n    } else if (\r\n      error instanceof Error &&\r\n      (error.message.includes(\"refresh token\") ||\r\n        error.message.includes(\"device ID\"))\r\n    ) {\r\n      // Lỗi liên quan đến refresh token hoặc device ID\r\n      console.log(\"Logging out due to missing refresh token or device ID\");\r\n      await useAuthStore.getState().logout();\r\n    }\r\n\r\n    throw error;\r\n  } finally {\r\n    console.log(\"Token refresh process completed, resetting isRefreshing flag\");\r\n    isRefreshing = false;\r\n  }\r\n};\r\n\r\n// Handle response errors, including 401 Unauthorized with token refresh\r\naxiosInstance.interceptors.response.use(\r\n  (response) => response,\r\n  async (error: AxiosError) => {\r\n    // Handle network errors\r\n    if (error.code === \"ECONNABORTED\") {\r\n      return Promise.reject(new Error(\"Request timeout. Please try again.\"));\r\n    }\r\n\r\n    if (!error.response) {\r\n      return Promise.reject(\r\n        new Error(\"Network error. Please check your connection.\"),\r\n      );\r\n    }\r\n\r\n    const originalRequest = error.config as AxiosRequestConfig & {\r\n      _retry?: boolean;\r\n    };\r\n\r\n    // Handle 401 Unauthorized errors by refreshing the token\r\n    if (error.response?.status === 401 && !originalRequest._retry) {\r\n      console.log(\r\n        `Received 401 error for request to ${originalRequest.url}, attempting to refresh token...`,\r\n      );\r\n\r\n      // Mark this request as retried to prevent infinite loops\r\n      originalRequest._retry = true;\r\n\r\n      // Check if we have the necessary data to refresh the token\r\n      const authState = useAuthStore.getState();\r\n      const refreshToken = authState.refreshToken;\r\n      const deviceId = authState.deviceId;\r\n\r\n      console.log(\"Checking refresh token data:\", {\r\n        hasRefreshToken: !!refreshToken,\r\n        refreshTokenPrefix: refreshToken\r\n          ? refreshToken.substring(0, 10) + \"...\"\r\n          : \"none\",\r\n        hasDeviceId: !!deviceId,\r\n        deviceId: deviceId || \"none\",\r\n      });\r\n\r\n      if (!refreshToken || !deviceId) {\r\n        console.error(\r\n          \"Cannot refresh token: Missing refresh token or device ID\",\r\n        );\r\n\r\n        // Automatically logout the user when refresh token is missing\r\n        console.log(\"Logging out due to missing refresh token or device ID\");\r\n        setTimeout(() => {\r\n          useAuthStore\r\n            .getState()\r\n            .logout()\r\n            .catch((e) => console.error(\"Error during auto logout:\", e));\r\n        }, 500);\r\n\r\n        return Promise.reject(\r\n          new Error(\"Session expired. Please login again.\"),\r\n        );\r\n      }\r\n\r\n      // If a token refresh is already in progress, queue this request\r\n      if (isRefreshing) {\r\n        console.log(\r\n          `Token refresh already in progress, queuing request to ${originalRequest.url}`,\r\n        );\r\n        return new Promise((resolve, reject) => {\r\n          subscribeTokenRefresh((token) => {\r\n            console.log(\r\n              `Processing queued request to ${originalRequest.url} with refreshed token`,\r\n            );\r\n            if (originalRequest.headers) {\r\n              originalRequest.headers[\"Authorization\"] = `Bearer ${token}`;\r\n            } else {\r\n              originalRequest.headers = { Authorization: `Bearer ${token}` };\r\n            }\r\n            resolve(axiosInstance(originalRequest));\r\n          });\r\n\r\n          // Add timeout to avoid waiting indefinitely\r\n          setTimeout(() => {\r\n            reject(new Error(\"Token refresh timeout\"));\r\n          }, 15000); // 15 seconds timeout\r\n        });\r\n      }\r\n\r\n      // Start a new token refresh process\r\n      console.log(\"Starting new token refresh process\");\r\n      isRefreshing = true;\r\n\r\n      try {\r\n        const newToken = await refreshAuthToken();\r\n\r\n        // Notify all subscribers that the token has been refreshed\r\n        console.log(\r\n          `Notifying ${refreshSubscribers.length} queued requests about token refresh`,\r\n        );\r\n        onTokenRefreshed(newToken);\r\n\r\n        // Update the authorization header and retry the original request\r\n        console.log(\r\n          `Retrying original request to ${originalRequest.url} with new token`,\r\n        );\r\n        if (originalRequest.headers) {\r\n          originalRequest.headers[\"Authorization\"] = `Bearer ${newToken}`;\r\n        } else {\r\n          originalRequest.headers = { Authorization: `Bearer ${newToken}` };\r\n        }\r\n        return axiosInstance(originalRequest);\r\n      } catch (error) {\r\n        // If token refresh fails, notify all subscribers to prevent hanging requests\r\n        console.error(\r\n          \"Token refresh failed:\",\r\n          error instanceof Error ? error.message : \"Unknown error\",\r\n        );\r\n        console.log(\r\n          `Notifying ${refreshSubscribers.length} queued requests about token refresh failure`,\r\n        );\r\n        onTokenRefreshed(\"\"); // Empty token will cause subscribers to fail properly\r\n\r\n        // Only logout if the error is a 401 or network error\r\n        if (\r\n          axios.isAxiosError(error) &&\r\n          (error.response?.status === 401 || !error.response)\r\n        ) {\r\n          console.log(\r\n            \"Logging out due to authentication error during token refresh\",\r\n          );\r\n          // We'll logout after a short delay to allow current operations to complete\r\n          setTimeout(() => {\r\n            useAuthStore\r\n              .getState()\r\n              .logout()\r\n              .catch((e) => console.error(\"Error during delayed logout:\", e));\r\n          }, 500);\r\n        }\r\n\r\n        return Promise.reject(\r\n          new Error(\"Session expired. Please login again.\"),\r\n        );\r\n      }\r\n    }\r\n\r\n    // For all other errors, just reject with the original error\r\n    return Promise.reject(error);\r\n  },\r\n);\r\n\r\nexport default axiosInstance;\r\n"], "names": [], "mappings": ";;;;;AAAA;AAMA;;;AAEA,MAAM;AAEN,iCAAiC;AACjC,IAAI,eAAe;AACnB,IAAI,qBAAkD,EAAE;AAYxD,yDAAyD;AACzD,MAAM,0BAA0B;IAC9B,OAAO,qIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;QAClB,SAAS;QACT,SAAS;YAAE,gBAAgB;QAAmB;QAC9C,SAAS;IACX;AACF;AAGO,MAAM,oBAAoB;AAEjC,kEAAkE;AAClE,kBAAkB,YAAY,CAAC,QAAQ,CAAC,GAAG,CACzC,CAAC,WAAa,UACd,CAAC;IACC,IAAI,MAAM,IAAI,KAAK,gBAAgB;QACjC,OAAO,QAAQ,MAAM,CAAC,IAAI,MAAM;IAClC;IAEA,IAAI,CAAC,MAAM,QAAQ,EAAE;QACnB,OAAO,QAAQ,MAAM,CAAC,IAAI,MAAM;IAClC;IAEA,OAAO,QAAQ,MAAM,CAAC;AACxB;AAIK,MAAM,sBAAsB,CAAC;IAClC,MAAM,WAAW;IAEjB,mCAAmC;IACnC,IAAI,SAAS,MAAM,IAAI,OAAO,IAAI;QAChC,SAAS,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,gBAAgB,GAAG,CAAC,OAAO,EAAE,OAAO;IACvE;IAEA,6BAA6B;IAC7B,SAAS,YAAY,CAAC,QAAQ,CAAC,GAAG,CAChC,CAAC,WAAa,UACd,CAAC;QACC,IAAI,MAAM,IAAI,KAAK,gBAAgB;YACjC,OAAO,QAAQ,MAAM,CAAC,IAAI,MAAM;QAClC;QAEA,IAAI,CAAC,MAAM,QAAQ,EAAE;YACnB,OAAO,QAAQ,MAAM,CACnB,IAAI,MAAM;QAEd;QAEA,OAAO,QAAQ,MAAM,CAAC;IACxB;IAGF,OAAO;AACT;AAEA,wDAAwD;AACxD,MAAM,gBAAgB;AAEtB,4BAA4B;AAC5B,cAAc,YAAY,CAAC,OAAO,CAAC,GAAG,CACpC,CAAC;IACC,IAAI;QACF,MAAM,cAAc,0HAAA,CAAA,eAAY,CAAC,QAAQ,GAAG,WAAW;QAEvD,IAAI,aAAa;YACf,OAAO,OAAO,GAAG,OAAO,OAAO,IAAI,CAAC;YACpC,OAAO,OAAO,CAAC,aAAa,GAAG,CAAC,OAAO,EAAE,aAAa;QACxD;QACA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uCAAuC;QACrD,OAAO;IACT;AACF,GACA,CAAC,QAAU,QAAQ,MAAM,CAAC;AAG5B,mEAAmE;AACnE,MAAM,wBAAwB,CAAC;IAC7B,mBAAmB,IAAI,CAAC;AAC1B;AAEA,uEAAuE;AACvE,MAAM,mBAAmB,CAAC;IACxB,mBAAmB,OAAO,CAAC,CAAC,WAAa,SAAS;IAClD,qBAAqB,EAAE;AACzB;AAEA,+CAA+C;AAC/C,MAAM,mBAAmB;IACvB,IAAI;QACF,MAAM,YAAY,0HAAA,CAAA,eAAY,CAAC,QAAQ;QACvC,MAAM,eAAe,UAAU,YAAY;QAC3C,MAAM,WAAW,UAAU,QAAQ;QAEnC,QAAQ,GAAG,CAAC;QACZ,QAAQ,GAAG,CACT,0BACA,eAAe,GAAG,aAAa,SAAS,CAAC,GAAG,IAAI,GAAG,CAAC,GAAG;QAEzD,QAAQ,GAAG,CAAC,sBAAsB,YAAY;QAE9C,6CAA6C;QAC7C,IAAI,CAAC,gBAAgB,aAAa,IAAI,OAAO,IAAI;YAC/C,QAAQ,KAAK,CAAC;YACd,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI,CAAC,YAAY,SAAS,IAAI,OAAO,IAAI;YACvC,QAAQ,KAAK,CAAC;YACd,MAAM,IAAI,MAAM;QAClB;QAEA,oCAAoC;QACpC,MAAM,aAAa,IAAI;QACvB,MAAM,YAAY,WAAW,IAAM,WAAW,KAAK,IAAI,QAAQ,qBAAqB;QAEpF,IAAI;YACF,MAAM,WAAW,MAAM,kBAAkB,IAAI,CAC3C,iBACA;gBACE;gBACA;YACF,GACA;gBACE,QAAQ,WAAW,MAAM;YAC3B;YAGF,aAAa;YACb,QAAQ,GAAG,CAAC,2BAA2B,SAAS,IAAI;YAEpD,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,IAAI,CAAC,WAAW,EAAE;gBAChD,QAAQ,KAAK,CAAC;gBACd,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,EAAE,WAAW,EAAE,GAAG,SAAS,IAAI;YAErC,oEAAoE;YACpE,0HAAA,CAAA,eAAY,CAAC,QAAQ,GAAG,SAAS,CAAC,aAAa;YAE/C,QAAQ,GAAG,CACT,CAAC,iDAAiD,EAAE,YAAY,SAAS,CAAC,GAAG,IAAI,GAAG,CAAC;YAGvF,OAAO;QACT,EAAE,OAAO,cAAc;YACrB,aAAa;YACb,MAAM;QACR;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CACX,+BACA,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAG3C,+BAA+B;QAC/B,IAAI,qIAAA,CAAA,UAAK,CAAC,YAAY,CAAC,QAAQ;YAC7B,IAAI,MAAM,QAAQ,EAAE,WAAW,KAAK;gBAClC,8CAA8C;gBAC9C,QAAQ,GAAG,CACT;gBAEF,MAAM,0HAAA,CAAA,eAAY,CAAC,QAAQ,GAAG,MAAM;YACtC,OAAO,IAAI,MAAM,QAAQ,EAAE,WAAW,KAAK;gBACzC,eAAe;gBACf,QAAQ,GAAG,CACT;gBAEF,MAAM,0HAAA,CAAA,eAAY,CAAC,QAAQ,GAAG,MAAM;YACtC,OAAO,IAAI,MAAM,IAAI,KAAK,gBAAgB;gBACxC,QAAQ,GAAG,CAAC;YACd;QACF,OAAO,IACL,iBAAiB,SACjB,CAAC,MAAM,OAAO,CAAC,QAAQ,CAAC,oBACtB,MAAM,OAAO,CAAC,QAAQ,CAAC,YAAY,GACrC;YACA,iDAAiD;YACjD,QAAQ,GAAG,CAAC;YACZ,MAAM,0HAAA,CAAA,eAAY,CAAC,QAAQ,GAAG,MAAM;QACtC;QAEA,MAAM;IACR,SAAU;QACR,QAAQ,GAAG,CAAC;QACZ,eAAe;IACjB;AACF;AAEA,wEAAwE;AACxE,cAAc,YAAY,CAAC,QAAQ,CAAC,GAAG,CACrC,CAAC,WAAa,UACd,OAAO;IACL,wBAAwB;IACxB,IAAI,MAAM,IAAI,KAAK,gBAAgB;QACjC,OAAO,QAAQ,MAAM,CAAC,IAAI,MAAM;IAClC;IAEA,IAAI,CAAC,MAAM,QAAQ,EAAE;QACnB,OAAO,QAAQ,MAAM,CACnB,IAAI,MAAM;IAEd;IAEA,MAAM,kBAAkB,MAAM,MAAM;IAIpC,yDAAyD;IACzD,IAAI,MAAM,QAAQ,EAAE,WAAW,OAAO,CAAC,gBAAgB,MAAM,EAAE;QAC7D,QAAQ,GAAG,CACT,CAAC,kCAAkC,EAAE,gBAAgB,GAAG,CAAC,gCAAgC,CAAC;QAG5F,yDAAyD;QACzD,gBAAgB,MAAM,GAAG;QAEzB,2DAA2D;QAC3D,MAAM,YAAY,0HAAA,CAAA,eAAY,CAAC,QAAQ;QACvC,MAAM,eAAe,UAAU,YAAY;QAC3C,MAAM,WAAW,UAAU,QAAQ;QAEnC,QAAQ,GAAG,CAAC,gCAAgC;YAC1C,iBAAiB,CAAC,CAAC;YACnB,oBAAoB,eAChB,aAAa,SAAS,CAAC,GAAG,MAAM,QAChC;YACJ,aAAa,CAAC,CAAC;YACf,UAAU,YAAY;QACxB;QAEA,IAAI,CAAC,gBAAgB,CAAC,UAAU;YAC9B,QAAQ,KAAK,CACX;YAGF,8DAA8D;YAC9D,QAAQ,GAAG,CAAC;YACZ,WAAW;gBACT,0HAAA,CAAA,eAAY,CACT,QAAQ,GACR,MAAM,GACN,KAAK,CAAC,CAAC,IAAM,QAAQ,KAAK,CAAC,6BAA6B;YAC7D,GAAG;YAEH,OAAO,QAAQ,MAAM,CACnB,IAAI,MAAM;QAEd;QAEA,gEAAgE;QAChE,IAAI,cAAc;YAChB,QAAQ,GAAG,CACT,CAAC,sDAAsD,EAAE,gBAAgB,GAAG,EAAE;YAEhF,OAAO,IAAI,QAAQ,CAAC,SAAS;gBAC3B,sBAAsB,CAAC;oBACrB,QAAQ,GAAG,CACT,CAAC,6BAA6B,EAAE,gBAAgB,GAAG,CAAC,qBAAqB,CAAC;oBAE5E,IAAI,gBAAgB,OAAO,EAAE;wBAC3B,gBAAgB,OAAO,CAAC,gBAAgB,GAAG,CAAC,OAAO,EAAE,OAAO;oBAC9D,OAAO;wBACL,gBAAgB,OAAO,GAAG;4BAAE,eAAe,CAAC,OAAO,EAAE,OAAO;wBAAC;oBAC/D;oBACA,QAAQ,cAAc;gBACxB;gBAEA,4CAA4C;gBAC5C,WAAW;oBACT,OAAO,IAAI,MAAM;gBACnB,GAAG,QAAQ,qBAAqB;YAClC;QACF;QAEA,oCAAoC;QACpC,QAAQ,GAAG,CAAC;QACZ,eAAe;QAEf,IAAI;YACF,MAAM,WAAW,MAAM;YAEvB,2DAA2D;YAC3D,QAAQ,GAAG,CACT,CAAC,UAAU,EAAE,mBAAmB,MAAM,CAAC,oCAAoC,CAAC;YAE9E,iBAAiB;YAEjB,iEAAiE;YACjE,QAAQ,GAAG,CACT,CAAC,6BAA6B,EAAE,gBAAgB,GAAG,CAAC,eAAe,CAAC;YAEtE,IAAI,gBAAgB,OAAO,EAAE;gBAC3B,gBAAgB,OAAO,CAAC,gBAAgB,GAAG,CAAC,OAAO,EAAE,UAAU;YACjE,OAAO;gBACL,gBAAgB,OAAO,GAAG;oBAAE,eAAe,CAAC,OAAO,EAAE,UAAU;gBAAC;YAClE;YACA,OAAO,cAAc;QACvB,EAAE,OAAO,OAAO;YACd,6EAA6E;YAC7E,QAAQ,KAAK,CACX,yBACA,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAE3C,QAAQ,GAAG,CACT,CAAC,UAAU,EAAE,mBAAmB,MAAM,CAAC,4CAA4C,CAAC;YAEtF,iBAAiB,KAAK,sDAAsD;YAE5E,qDAAqD;YACrD,IACE,qIAAA,CAAA,UAAK,CAAC,YAAY,CAAC,UACnB,CAAC,MAAM,QAAQ,EAAE,WAAW,OAAO,CAAC,MAAM,QAAQ,GAClD;gBACA,QAAQ,GAAG,CACT;gBAEF,2EAA2E;gBAC3E,WAAW;oBACT,0HAAA,CAAA,eAAY,CACT,QAAQ,GACR,MAAM,GACN,KAAK,CAAC,CAAC,IAAM,QAAQ,KAAK,CAAC,gCAAgC;gBAChE,GAAG;YACL;YAEA,OAAO,QAAQ,MAAM,CACnB,IAAI,MAAM;QAEd;IACF;IAEA,4DAA4D;IAC5D,OAAO,QAAQ,MAAM,CAAC;AACxB;uCAGa", "debugId": null}}, {"offset": {"line": 1253, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/src/types/base.ts"], "sourcesContent": ["// Interfaces for specific Json fields\r\nexport interface MediaMetadata {\r\n  path: string;\r\n  size: number;\r\n  mimeType: string;\r\n  extension: string;\r\n  bucketName: string;\r\n  uploadedAt: string;\r\n  sizeFormatted: string;\r\n}\r\n\r\nexport interface Media {\r\n  url: string;\r\n  type: string; // e.g., \"IMAGE\", \"VIDEO\", \"DOCUMENT\"\r\n  fileId: string;\r\n  fileName: string;\r\n  metadata: MediaMetadata;\r\n  thumbnailUrl?: string;\r\n}\r\n\r\nexport interface MessageContent {\r\n  text?: string;\r\n  image?: string;\r\n  video?: string;\r\n  media?: Media[];\r\n}\r\n\r\nexport interface Reaction {\r\n  userId: string;\r\n  reactionType: ReactionType;\r\n}\r\n\r\nexport interface NotificationReference {\r\n  postId?: string;\r\n  commentId?: string;\r\n  messageId?: string;\r\n}\r\n\r\n// Model interfaces\r\nexport interface User {\r\n  id: string;\r\n  email?: string | null;\r\n  phoneNumber?: string | null;\r\n  passwordHash: string;\r\n  createdAt: Date;\r\n  updatedAt: Date;\r\n  userInfo?: UserInfo | null;\r\n  infoId?: string | null;\r\n  refreshTokens: RefreshToken[];\r\n  qrCodes: QrCode[];\r\n  posts: Post[];\r\n  stories: Story[];\r\n  groupMembers: GroupMember[];\r\n  cloudFiles: CloudStorage[];\r\n  pinnedItems: PinnedItem[];\r\n  sentFriends: Friend[];\r\n  receivedFriends: Friend[];\r\n  contacts: Contact[];\r\n  contactOf: Contact[];\r\n  settings: UserSetting[];\r\n  postReactions: PostReaction[];\r\n  hiddenPosts: HiddenPost[];\r\n  addedBy: GroupMember[];\r\n  notifications: Notification[];\r\n  sentMessages: Message[];\r\n  receivedMessages: Message[];\r\n  comments: Comment[];\r\n}\r\n\r\nexport interface UserInfo {\r\n  id: string;\r\n  fullName?: string | null;\r\n  dateOfBirth?: Date | null;\r\n  gender?: Gender | null;\r\n  bio?: string | null;\r\n  blockStrangers: boolean;\r\n  profilePictureUrl?: string | null;\r\n  statusMessage?: string | null;\r\n  lastSeen?: Date | null;\r\n  coverImgUrl?: string | null;\r\n  createdAt: Date;\r\n  updatedAt: Date;\r\n  userAuth: User;\r\n}\r\n\r\nexport interface Friend {\r\n  id: string;\r\n  userOne: User;\r\n  userOneId: string;\r\n  userTwo: User;\r\n  userTwoId: string;\r\n  status: FriendStatus;\r\n  createdAt: Date;\r\n  updatedAt: Date;\r\n}\r\n\r\nexport interface UserSetting {\r\n  id: string;\r\n  userId: string;\r\n  user: User;\r\n  notificationEnabled: boolean;\r\n  darkMode: boolean;\r\n  lastUpdated: Date;\r\n}\r\n\r\nexport interface Post {\r\n  id: string;\r\n  userId: string;\r\n  user: User;\r\n  content?: string | null;\r\n  media?: Media[] | null; // Specific Media interface\r\n  privacyLevel: string;\r\n  createdAt: Date;\r\n  updatedAt: Date;\r\n  reactions: PostReaction[];\r\n  hiddenBy: HiddenPost[];\r\n  comments: Comment[];\r\n}\r\n\r\nexport interface Story {\r\n  id: string;\r\n  userId: string;\r\n  user: User;\r\n  mediaUrl: string;\r\n  expiresAt: Date;\r\n  createdAt: Date;\r\n}\r\n\r\nexport interface Group {\r\n  id: string;\r\n  name: string;\r\n  creatorId: string;\r\n  avatarUrl?: string | null;\r\n  createdAt: Date;\r\n  members: GroupMember[];\r\n  messages: Message[];\r\n  // Thêm trường memberUsers để lưu trữ thông tin thành viên đã được xử lý\r\n  memberUsers?: Array<{\r\n    id: string;\r\n    fullName: string;\r\n    profilePictureUrl?: string | null;\r\n    role: GroupRole;\r\n  }>;\r\n}\r\n\r\nexport interface GroupMember {\r\n  id: string;\r\n  groupId: string;\r\n  group: Group;\r\n  userId: string;\r\n  user: User;\r\n  role: GroupRole;\r\n  joinedAt: Date;\r\n  addedBy: User;\r\n  addedById: string;\r\n}\r\n\r\nexport interface CloudStorage {\r\n  id: string;\r\n  userId: string;\r\n  user: User;\r\n  fileName: string;\r\n  fileUrl: string;\r\n  fileType?: string | null;\r\n  fileSize?: number | null;\r\n  uploadedAt: Date;\r\n}\r\n\r\nexport interface PinnedItem {\r\n  id: string;\r\n  userId: string;\r\n  user: User;\r\n  itemType: MessageType;\r\n  itemId: string;\r\n  pinnedAt: Date;\r\n}\r\n\r\nexport interface Contact {\r\n  id: string;\r\n  userId: string;\r\n  user: User;\r\n  contactUserId: string;\r\n  contactUser: User;\r\n  nickname?: string | null;\r\n  addedAt: Date;\r\n}\r\n\r\nexport interface PostReaction {\r\n  id: string;\r\n  postId: string;\r\n  post: Post;\r\n  userId: string;\r\n  user: User;\r\n  reactionType: ReactionType;\r\n  reactedAt: Date;\r\n}\r\n\r\nexport interface HiddenPost {\r\n  id: string;\r\n  userId: string;\r\n  user: User;\r\n  postId: string;\r\n  post: Post;\r\n  hiddenAt: Date;\r\n}\r\n\r\nexport interface RefreshToken {\r\n  id: string;\r\n  token: string;\r\n  userId: string;\r\n  user: User;\r\n  deviceName?: string | null;\r\n  deviceType?: DeviceType | null;\r\n  ipAddress?: string | null;\r\n  userAgent?: string | null;\r\n  isRevoked: boolean;\r\n  expiresAt: Date;\r\n  createdAt: Date;\r\n  updatedAt: Date;\r\n}\r\n\r\nexport interface QrCode {\r\n  id: string;\r\n  qrToken: string;\r\n  userId?: string | null;\r\n  user?: User | null;\r\n  status: QrCodeStatus;\r\n  expiresAt: Date;\r\n  createdAt: Date;\r\n  updatedAt: Date;\r\n}\r\n\r\nexport interface Notification {\r\n  id: string;\r\n  userId: string;\r\n  user: User;\r\n  type: string;\r\n  content: Record<string, string | number | boolean | null>; // Simple key-value object\r\n  read: boolean;\r\n  reference?: NotificationReference | null; // Specific NotificationReference interface\r\n  createdAt: Date;\r\n}\r\n\r\nexport interface Message {\r\n  id: string;\r\n  content: MessageContent; // Specific MessageContent interface\r\n  senderId: string;\r\n  sender: User;\r\n  receiverId?: string | null;\r\n  receiver?: User | null;\r\n  groupId?: string | null;\r\n  group?: Group | null;\r\n  recalled: boolean;\r\n  deletedBy: string[];\r\n  repliedTo?: string | null;\r\n  reactions: Reaction[]; // Specific Reaction interface\r\n  readBy: string[];\r\n  createdAt: Date;\r\n  updatedAt: Date;\r\n  messageType?: MessageType | null;\r\n  forwardedFrom?: string | null; // ID of the original message if this is a forwarded message\r\n}\r\n\r\nexport interface Comment {\r\n  id: string;\r\n  postId: string;\r\n  post: Post;\r\n  userId: string;\r\n  user: User;\r\n  content: string;\r\n  repliedTo?: string | null;\r\n  reactions: Reaction[]; // Specific Reaction interface\r\n}\r\n\r\n// Enums\r\nenum Gender {\r\n  MALE = \"MALE\",\r\n  FEMALE = \"FEMALE\",\r\n  OTHER = \"OTHER\",\r\n}\r\n\r\nenum GroupRole {\r\n  LEADER = \"LEADER\",\r\n  CO_LEADER = \"CO_LEADER\",\r\n  MEMBER = \"MEMBER\",\r\n}\r\n\r\nenum MessageType {\r\n  GROUP = \"GROUP\",\r\n  USER = \"USER\",\r\n}\r\n\r\nenum DeviceType {\r\n  MOBILE = \"MOBILE\",\r\n  TABLET = \"TABLET\",\r\n  DESKTOP = \"DESKTOP\",\r\n  OTHER = \"OTHER\",\r\n}\r\n\r\nenum FriendStatus {\r\n  PENDING = \"PENDING\",\r\n  ACCEPTED = \"ACCEPTED\",\r\n  DECLINED = \"DECLINED\",\r\n  BLOCKED = \"BLOCKED\",\r\n}\r\n\r\nenum QrCodeStatus {\r\n  PENDING = \"PENDING\",\r\n  SCANNED = \"SCANNED\",\r\n  CONFIRMED = \"CONFIRMED\",\r\n  EXPIRED = \"EXPIRED\",\r\n  CANCELLED = \"CANCELLED\",\r\n}\r\n\r\nenum ReactionType {\r\n  LIKE = \"LIKE\",\r\n  LOVE = \"LOVE\",\r\n  HAHA = \"HAHA\",\r\n  WOW = \"WOW\",\r\n  SAD = \"SAD\",\r\n  ANGRY = \"ANGRY\",\r\n}\r\n\r\nexport {\r\n  // Enums\r\n  Gender,\r\n  GroupRole,\r\n  MessageType,\r\n  DeviceType,\r\n  FriendStatus,\r\n  QrCodeStatus,\r\n  ReactionType,\r\n};\r\n"], "names": [], "mappings": "AAAA,sCAAsC;;;;;;;;;;AAkRtC,QAAQ;AACR,IAAA,AAAK,gCAAA;;;;WAAA;EAAA;AAML,IAAA,AAAK,mCAAA;;;;WAAA;EAAA;AAML,IAAA,AAAK,qCAAA;;;WAAA;EAAA;AAKL,IAAA,AAAK,oCAAA;;;;;WAAA;EAAA;AAOL,IAAA,AAAK,sCAAA;;;;;WAAA;EAAA;AAOL,IAAA,AAAK,sCAAA;;;;;;WAAA;EAAA;AAQL,IAAA,AAAK,sCAAA;;;;;;;WAAA;EAAA", "debugId": null}}, {"offset": {"line": 1319, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/src/utils/helpers.ts"], "sourcesContent": ["import { DeviceType } from \"@/types/base\";\r\nimport * as U<PERSON>ars<PERSON> from \"ua-parser-js\";\r\n\r\n/**\r\n * <PERSON><PERSON><PERSON> định thông tin thiết bị dựa trên userAgent\r\n * @returns Thông tin về loại thiết bị và tên thiết bị\r\n */\r\nexport const getDeviceInfo = () => {\r\n  if (typeof window === \"undefined\") {\r\n    return { deviceType: DeviceType.OTHER, deviceName: \"Dell Latitude 5290\" };\r\n  }\r\n\r\n  const parser = new UAParser.UAParser();\r\n  const result = parser.getResult();\r\n\r\n  // Xác định deviceType\r\n  let deviceType: DeviceType;\r\n  const device = result.device.type?.toLowerCase();\r\n  const os = result.os.name?.toLowerCase();\r\n\r\n  if (device === \"mobile\" || /iphone|android/.test(result.ua.toLowerCase())) {\r\n    deviceType = DeviceType.MOBILE;\r\n  } else if (device === \"tablet\" || /ipad/.test(result.ua.toLowerCase())) {\r\n    deviceType = DeviceType.TABLET;\r\n  } else if (os && /mac|win|linux/.test(os)) {\r\n    deviceType = DeviceType.DESKTOP;\r\n  } else {\r\n    deviceType = DeviceType.OTHER;\r\n  }\r\n\r\n  // Lấy deviceName\r\n  const deviceName =\r\n    result.device.model || result.os.name || \"Dell Latitude 5290\";\r\n\r\n  return { deviceType, deviceName };\r\n};\r\n\r\n/**\r\n * Kiểm tra xem một chuỗi có phải là email hợp lệ hay không\r\n * @param input Chuỗi cần kiểm tra\r\n * @returns true nếu là email hợp lệ, false nếu không phải\r\n */\r\nexport const isEmail = (input: string): boolean => {\r\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\r\n  return emailRegex.test(input);\r\n};\r\n\r\n/**\r\n * Kiểm tra xem một chuỗi có phải là số điện thoại hợp lệ hay không\r\n * @param input Chuỗi cần kiểm tra\r\n * @returns true nếu là số điện thoại hợp lệ, false nếu không phải\r\n */\r\nexport const isPhoneNumber = (input: string): boolean => {\r\n  const phoneRegex = /^\\d{10,11}$/; // Giả sử số điện thoại Việt Nam có 10-11 chữ số\r\n  return phoneRegex.test(input);\r\n};\r\n\r\n/**\r\n * Định dạng số điện thoại theo định dạng Việt Nam\r\n * @param phone Số điện thoại cần định dạng\r\n * @returns Số điện thoại đã được định dạng\r\n */\r\nexport const formatPhoneNumber = (phone: string): string => {\r\n  if (!phone) return \"\";\r\n\r\n  // Loại bỏ tất cả các ký tự không phải số\r\n  const cleaned = phone.replace(/\\D/g, \"\");\r\n\r\n  // Kiểm tra độ dài và định dạng theo quy tắc Việt Nam\r\n  if (cleaned.length === 10) {\r\n    return `${cleaned.slice(0, 3)} ${cleaned.slice(3, 6)} ${cleaned.slice(6)}`;\r\n  } else if (cleaned.length === 11) {\r\n    return `${cleaned.slice(0, 4)} ${cleaned.slice(4, 7)} ${cleaned.slice(7)}`;\r\n  }\r\n\r\n  return cleaned;\r\n};\r\n\r\n/**\r\n * Định dạng ngày tháng theo định dạng dd/mm/yyyy\r\n * @param date Đối tượng Date cần định dạng\r\n * @returns Chuỗi ngày tháng đã được định dạng\r\n */\r\nexport const formatDate = (date: Date): string => {\r\n  const day = date.getDate().toString().padStart(2, \"0\");\r\n  const month = (date.getMonth() + 1).toString().padStart(2, \"0\");\r\n  const year = date.getFullYear();\r\n\r\n  return `${day}/${month}/${year}`;\r\n};\r\n\r\n/**\r\n * Chuyển đổi giới tính từ tiếng Anh sang tiếng Việt\r\n * @param gender Giới tính bằng tiếng Anh (\"male\" hoặc \"female\")\r\n * @returns Giới tính bằng tiếng Việt\r\n */\r\nexport const translateGender = (gender: string): string => {\r\n  if (gender.toLowerCase() === \"male\") return \"Nam\";\r\n  if (gender.toLowerCase() === \"female\") return \"Nữ\";\r\n  return gender;\r\n};\r\n\r\n/**\r\n * Kiểm tra xem một chuỗi có phải là họ tên tiếng Việt hợp lệ hay không\r\n * @param input Chuỗi cần kiểm tra\r\n * @returns true nếu là họ tên tiếng Việt hợp lệ, false nếu không phải\r\n */\r\nexport const isVietnameseName = (input: string): boolean => {\r\n  // Regex cho tên tiếng Việt có dấu hoặc không dấu\r\n  // Cho phép chữ cái, dấu cách và dấu tiếng Việt\r\n  // Yêu cầu ít nhất 2 từ (họ và tên)\r\n  const vietnameseNameRegex =\r\n    /^[A-Za-zÀÁÂÃÈÉÊÌÍÒÓÔÕÙÚĂĐĨŨƠàáâãèéêìíòóôõùúăđĩũơƯĂẠẢẤẦẨẪẬẮẰẲẴẶẸẺẼỀỀỂưăạảấầẩẫậắằẳẵặẹẻẽềềểỄỆỈỊỌỎỐỒỔỖỘỚỜỞỠỢỤỦỨỪễệỉịọỏốồổỗộớờởỡợụủứừỬỮỰỲỴÝỶỸửữựỳỵỷỹ]+(\\s[A-Za-zÀÁÂÃÈÉÊÌÍÒÓÔÕÙÚĂĐĨŨƠàáâãèéêìíòóôõùúăđĩũơƯĂẠẢẤẦẨẪẬẮẰẲẴẶẸẺẼỀỀỂưăạảấầẩẫậắằẳẵặẹẻẽềềểỄỆỈỊỌỎỐỒỔỖỘỚỜỞỠỢỤỦỨỪễệỉịọỏốồổỗộớờởỡợụủứừỬỮỰỲỴÝỶỸửữựỳỵỷỹ]+)+$/;\r\n  return vietnameseNameRegex.test(input);\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;AAMO,MAAM,gBAAgB;IAC3B,wCAAmC;QACjC,OAAO;YAAE,YAAY,oHAAA,CAAA,aAAU,CAAC,KAAK;YAAE,YAAY;QAAqB;IAC1E;;IAEA,MAAM;IACN,MAAM;IAEN,sBAAsB;IACtB,IAAI;IACJ,MAAM;IACN,MAAM;IAYN,iBAAiB;IACjB,MAAM;AAIR;AAOO,MAAM,UAAU,CAAC;IACtB,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAOO,MAAM,gBAAgB,CAAC;IAC5B,MAAM,aAAa,eAAe,gDAAgD;IAClF,OAAO,WAAW,IAAI,CAAC;AACzB;AAOO,MAAM,oBAAoB,CAAC;IAChC,IAAI,CAAC,OAAO,OAAO;IAEnB,yCAAyC;IACzC,MAAM,UAAU,MAAM,OAAO,CAAC,OAAO;IAErC,qDAAqD;IACrD,IAAI,QAAQ,MAAM,KAAK,IAAI;QACzB,OAAO,GAAG,QAAQ,KAAK,CAAC,GAAG,GAAG,CAAC,EAAE,QAAQ,KAAK,CAAC,GAAG,GAAG,CAAC,EAAE,QAAQ,KAAK,CAAC,IAAI;IAC5E,OAAO,IAAI,QAAQ,MAAM,KAAK,IAAI;QAChC,OAAO,GAAG,QAAQ,KAAK,CAAC,GAAG,GAAG,CAAC,EAAE,QAAQ,KAAK,CAAC,GAAG,GAAG,CAAC,EAAE,QAAQ,KAAK,CAAC,IAAI;IAC5E;IAEA,OAAO;AACT;AAOO,MAAM,aAAa,CAAC;IACzB,MAAM,MAAM,KAAK,OAAO,GAAG,QAAQ,GAAG,QAAQ,CAAC,GAAG;IAClD,MAAM,QAAQ,CAAC,KAAK,QAAQ,KAAK,CAAC,EAAE,QAAQ,GAAG,QAAQ,CAAC,GAAG;IAC3D,MAAM,OAAO,KAAK,WAAW;IAE7B,OAAO,GAAG,IAAI,CAAC,EAAE,MAAM,CAAC,EAAE,MAAM;AAClC;AAOO,MAAM,kBAAkB,CAAC;IAC9B,IAAI,OAAO,WAAW,OAAO,QAAQ,OAAO;IAC5C,IAAI,OAAO,WAAW,OAAO,UAAU,OAAO;IAC9C,OAAO;AACT;AAOO,MAAM,mBAAmB,CAAC;IAC/B,iDAAiD;IACjD,+CAA+C;IAC/C,mCAAmC;IACnC,MAAM,sBACJ;IACF,OAAO,oBAAoB,IAAI,CAAC;AAClC", "debugId": null}}, {"offset": {"line": 1393, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/src/actions/auth.action.ts"], "sourcesContent": ["\"use server\";\r\nimport axios from \"axios\";\r\nimport axiosInstance, {\r\n  createAxiosInstance,\r\n  refreshTokenAxios,\r\n} from \"@/lib/axios\";\r\nimport { useAuthStore } from \"@/stores/authStore\";\r\nimport { DeviceType } from \"@/types/base\";\r\nimport { isEmail } from \"@/utils/helpers\";\r\n\r\nexport async function initiateRegistration(identifier: string) {\r\n  try {\r\n    // Determine if the identifier is an email or phone number\r\n    const isEmailFormat = isEmail(identifier);\r\n\r\n    const serverAxios = createAxiosInstance();\r\n    const response = await serverAxios.post(\"/auth/register/initiate\", {\r\n      [isEmailFormat ? \"email\" : \"phoneNumber\"]: identifier,\r\n    });\r\n    const { registrationId } = response.data;\r\n\r\n    return { success: true, registrationId };\r\n  } catch (error) {\r\n    console.error(\"Initiate registration failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function verifyOtp(registrationId: string, otp: string) {\r\n  try {\r\n    const serverAxios = createAxiosInstance();\r\n    const response = await serverAxios.post(\"/auth/register/verify\", {\r\n      registrationId,\r\n      otp,\r\n    });\r\n    return { success: true, data: response.data };\r\n  } catch (error) {\r\n    console.error(\"OTP verification failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function completeRegistration(\r\n  registrationId: string,\r\n  password: string,\r\n  fullName: string,\r\n  dateOfBirth: string,\r\n  gender: string,\r\n) {\r\n  try {\r\n    const serverAxios = createAxiosInstance();\r\n    const response = await serverAxios.post(\"/auth/register/complete\", {\r\n      registrationId,\r\n      password,\r\n      fullName,\r\n      dateOfBirth,\r\n      gender,\r\n    });\r\n    const { user } = response.data;\r\n\r\n    return { success: true, user };\r\n  } catch (error) {\r\n    console.error(\"Complete registration failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\nexport async function login(\r\n  identifier: string,\r\n  password: string,\r\n  deviceName: string,\r\n  deviceType: DeviceType,\r\n) {\r\n  try {\r\n    // Create a clean axios instance for login (no token needed)\r\n    const serverAxios = createAxiosInstance();\r\n\r\n    // Determine if identifier is email or phone number\r\n    const response = await serverAxios.post(\"/auth/login\", {\r\n      [isEmail(identifier) ? \"email\" : \"phoneNumber\"]: identifier,\r\n      password,\r\n      deviceName,\r\n      deviceType,\r\n    });\r\n\r\n    // Extract response data\r\n    const { user, accessToken, refreshToken, deviceId } = response.data;\r\n\r\n    // Validate required fields\r\n    if (!accessToken || !refreshToken || !deviceId) {\r\n      throw new Error(\"Invalid login response: missing required tokens\");\r\n    }\r\n\r\n    return { success: true, user, accessToken, refreshToken, deviceId };\r\n  } catch (error) {\r\n    console.error(\"Login failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function logout() {\r\n  try {\r\n    const authState = useAuthStore.getState();\r\n    const refreshToken = authState.refreshToken;\r\n    const accessToken = authState.accessToken || \"\";\r\n\r\n    // Only attempt to call the logout API if we have a refresh token\r\n    if (refreshToken) {\r\n      try {\r\n        const serverAxios = createAxiosInstance(accessToken);\r\n        await serverAxios.post(\r\n          \"/auth/logout\",\r\n          {},\r\n          {\r\n            headers: { \"refresh-token\": refreshToken },\r\n          },\r\n        );\r\n      } catch (apiError) {\r\n        // Log but continue with local logout even if API call fails\r\n        console.error(\r\n          \"API logout failed, continuing with local logout:\",\r\n          apiError,\r\n        );\r\n      }\r\n    }\r\n\r\n    // Always return success to ensure UI updates\r\n    return { success: true };\r\n  } catch (error) {\r\n    console.error(\"Logout failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\n// Làm mới token - This function is now primarily handled by the axios interceptors\r\n// but we keep this for explicit token refresh calls if needed\r\nexport async function refreshToken() {\r\n  try {\r\n    const authState = useAuthStore.getState();\r\n    const refreshToken = authState.refreshToken;\r\n    const deviceId = authState.deviceId;\r\n\r\n    if (!refreshToken) {\r\n      throw new Error(\"No refresh token available\");\r\n    }\r\n\r\n    if (!deviceId) {\r\n      throw new Error(\"No device ID available\");\r\n    }\r\n\r\n    // Use the dedicated refresh token axios instance\r\n    const response = await refreshTokenAxios.post(\"/auth/refresh\", {\r\n      refreshToken,\r\n      deviceId,\r\n    });\r\n\r\n    if (!response.data || !response.data.accessToken) {\r\n      throw new Error(\"Invalid response from refresh token API\");\r\n    }\r\n\r\n    const { accessToken } = response.data;\r\n    const device = response.data.device;\r\n\r\n    // Update tokens in the store if in browser environment\r\n    if (typeof window !== \"undefined\") {\r\n      useAuthStore.getState().setTokens(accessToken, refreshToken);\r\n    }\r\n\r\n    // Update cookie if running on server\r\n    if (typeof window === \"undefined\") {\r\n      try {\r\n        const { cookies } = await import(\"next/headers\");\r\n        (await cookies()).set(\"access_token\", accessToken, {\r\n          httpOnly: true,\r\n          secure: process.env.NODE_ENV === \"production\",\r\n          maxAge: 60 * 60 * 24 * 7, // 7 days\r\n          path: \"/\",\r\n        });\r\n      } catch (cookieError) {\r\n        console.error(\"Failed to set cookie:\", cookieError);\r\n        // Continue even if cookie setting fails\r\n      }\r\n    }\r\n\r\n    return { success: true, accessToken, device };\r\n  } catch (error) {\r\n    // Only logout if we're in the browser\r\n    if (typeof window !== \"undefined\") {\r\n      try {\r\n        await useAuthStore.getState().logout();\r\n      } catch (logoutError) {\r\n        console.error(\"Error during logout:\", logoutError);\r\n      }\r\n    }\r\n\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function initiateForgotPassword(identifier: string) {\r\n  try {\r\n    // Kiểm tra xem identifier là email hay số điện thoại\r\n    const isEmailFormat = isEmail(identifier);\r\n\r\n    const serverAxios = createAxiosInstance();\r\n    const response = await serverAxios.post(\"/auth/forgot-password\", {\r\n      [isEmailFormat ? \"email\" : \"phoneNumber\"]: identifier,\r\n    });\r\n    const { resetId } = response.data;\r\n\r\n    return { success: true, resetId };\r\n  } catch (error) {\r\n    console.error(\"Initiate forgot password failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function verifyForgotPasswordOtp(resetId: string, otp: string) {\r\n  try {\r\n    const serverAxios = createAxiosInstance();\r\n    const response = await serverAxios.post(\"/auth/forgot-password/verify\", {\r\n      resetId,\r\n      otp,\r\n    });\r\n    return { success: true, data: response.data };\r\n  } catch (error) {\r\n    console.error(\"Verify forgot password OTP failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function resetPassword(resetId: string, newPassword: string) {\r\n  try {\r\n    const serverAxios = createAxiosInstance();\r\n    const response = await serverAxios.post(\"/auth/forgot-password/reset\", {\r\n      resetId,\r\n      newPassword,\r\n    });\r\n    return { success: true, data: response.data };\r\n  } catch (error) {\r\n    console.error(\"Reset password failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\n// Thay đổi mật khẩu (khi đã đăng nhập)\r\nexport async function changePassword(\r\n  currentPassword: string,\r\n  newPassword: string,\r\n  accessToken: string,\r\n) {\r\n  try {\r\n    if (!accessToken) {\r\n      return {\r\n        success: false,\r\n        error: \"Bạn cần đăng nhập lại để thực hiện thao tác này\",\r\n      };\r\n    }\r\n\r\n    const serverAxios = createAxiosInstance(accessToken);\r\n    const response = await serverAxios.put(\"/auth/change-password\", {\r\n      currentPassword,\r\n      newPassword,\r\n    });\r\n\r\n    return {\r\n      success: true,\r\n      message: response.data.message || \"Đổi mật khẩu thành công\",\r\n    };\r\n  } catch (error) {\r\n    console.error(\"Change password failed:\", error);\r\n\r\n    // Xử lý lỗi từ API\r\n    if (axios.isAxiosError(error) && error.response) {\r\n      console.error(\"Error response:\", {\r\n        status: error.response.status,\r\n        data: error.response.data,\r\n      });\r\n\r\n      // Kiểm tra lỗi mật khẩu cũ không đúng\r\n      if (error.response.status === 400 || error.response.status === 401) {\r\n        if (error.response.data?.message) {\r\n          // Nếu server trả về thông báo lỗi cụ thể\r\n          return {\r\n            success: false,\r\n            error: error.response.data.message,\r\n          };\r\n        }\r\n\r\n        // Nếu không có thông báo cụ thể, kiểm tra nếu là lỗi mật khẩu cũ không đúng\r\n        if (error.response.data?.error === \"INVALID_CURRENT_PASSWORD\") {\r\n          return {\r\n            success: false,\r\n            error: \"Mật khẩu hiện tại không đúng\",\r\n          };\r\n        }\r\n      }\r\n    }\r\n\r\n    return {\r\n      success: false,\r\n      error:\r\n        error instanceof Error\r\n          ? error.message\r\n          : \"Đã xảy ra lỗi khi đổi mật khẩu\",\r\n    };\r\n  }\r\n}\r\n\r\n// Xác nhận đặt lại mật khẩu với token (qua email)\r\nexport async function confirmResetPassword(token: string, newPassword: string) {\r\n  try {\r\n    const response = await axiosInstance.post(\"/auth/reset-password/confirm\", {\r\n      token,\r\n      newPassword,\r\n    });\r\n    return {\r\n      success: true,\r\n      message: response.data.message || \"Password has been reset successfully\",\r\n    };\r\n  } catch (error) {\r\n    console.error(\"Confirm reset password failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;AACA;AACA;AAIA;AAEA;;;;;;;;AAEO,eAAe,qBAAqB,UAAkB;IAC3D,IAAI;QACF,0DAA0D;QAC1D,MAAM,gBAAgB,CAAA,GAAA,uHAAA,CAAA,UAAO,AAAD,EAAE;QAE9B,MAAM,cAAc,CAAA,GAAA,mHAAA,CAAA,sBAAmB,AAAD;QACtC,MAAM,WAAW,MAAM,YAAY,IAAI,CAAC,2BAA2B;YACjE,CAAC,gBAAgB,UAAU,cAAc,EAAE;QAC7C;QACA,MAAM,EAAE,cAAc,EAAE,GAAG,SAAS,IAAI;QAExC,OAAO;YAAE,SAAS;YAAM;QAAe;IACzC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iCAAiC;QAC/C,OAAO;YACL,SAAS;YACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAClD;IACF;AACF;AAEO,eAAe,UAAU,cAAsB,EAAE,GAAW;IACjE,IAAI;QACF,MAAM,cAAc,CAAA,GAAA,mHAAA,CAAA,sBAAmB,AAAD;QACtC,MAAM,WAAW,MAAM,YAAY,IAAI,CAAC,yBAAyB;YAC/D;YACA;QACF;QACA,OAAO;YAAE,SAAS;YAAM,MAAM,SAAS,IAAI;QAAC;IAC9C,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,OAAO;YACL,SAAS;YACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAClD;IACF;AACF;AAEO,eAAe,qBACpB,cAAsB,EACtB,QAAgB,EAChB,QAAgB,EAChB,WAAmB,EACnB,MAAc;IAEd,IAAI;QACF,MAAM,cAAc,CAAA,GAAA,mHAAA,CAAA,sBAAmB,AAAD;QACtC,MAAM,WAAW,MAAM,YAAY,IAAI,CAAC,2BAA2B;YACjE;YACA;YACA;YACA;YACA;QACF;QACA,MAAM,EAAE,IAAI,EAAE,GAAG,SAAS,IAAI;QAE9B,OAAO;YAAE,SAAS;YAAM;QAAK;IAC/B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iCAAiC;QAC/C,OAAO;YACL,SAAS;YACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAClD;IACF;AACF;AACO,eAAe,MACpB,UAAkB,EAClB,QAAgB,EAChB,UAAkB,EAClB,UAAsB;IAEtB,IAAI;QACF,4DAA4D;QAC5D,MAAM,cAAc,CAAA,GAAA,mHAAA,CAAA,sBAAmB,AAAD;QAEtC,mDAAmD;QACnD,MAAM,WAAW,MAAM,YAAY,IAAI,CAAC,eAAe;YACrD,CAAC,CAAA,GAAA,uHAAA,CAAA,UAAO,AAAD,EAAE,cAAc,UAAU,cAAc,EAAE;YACjD;YACA;YACA;QACF;QAEA,wBAAwB;QACxB,MAAM,EAAE,IAAI,EAAE,WAAW,EAAE,YAAY,EAAE,QAAQ,EAAE,GAAG,SAAS,IAAI;QAEnE,2BAA2B;QAC3B,IAAI,CAAC,eAAe,CAAC,gBAAgB,CAAC,UAAU;YAC9C,MAAM,IAAI,MAAM;QAClB;QAEA,OAAO;YAAE,SAAS;YAAM;YAAM;YAAa;YAAc;QAAS;IACpE,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iBAAiB;QAC/B,OAAO;YACL,SAAS;YACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAClD;IACF;AACF;AAEO,eAAe;IACpB,IAAI;QACF,MAAM,YAAY,0HAAA,CAAA,eAAY,CAAC,QAAQ;QACvC,MAAM,eAAe,UAAU,YAAY;QAC3C,MAAM,cAAc,UAAU,WAAW,IAAI;QAE7C,iEAAiE;QACjE,IAAI,cAAc;YAChB,IAAI;gBACF,MAAM,cAAc,CAAA,GAAA,mHAAA,CAAA,sBAAmB,AAAD,EAAE;gBACxC,MAAM,YAAY,IAAI,CACpB,gBACA,CAAC,GACD;oBACE,SAAS;wBAAE,iBAAiB;oBAAa;gBAC3C;YAEJ,EAAE,OAAO,UAAU;gBACjB,4DAA4D;gBAC5D,QAAQ,KAAK,CACX,oDACA;YAEJ;QACF;QAEA,6CAA6C;QAC7C,OAAO;YAAE,SAAS;QAAK;IACzB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kBAAkB;QAChC,OAAO;YACL,SAAS;YACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAClD;IACF;AACF;AAIO,eAAe;IACpB,IAAI;QACF,MAAM,YAAY,0HAAA,CAAA,eAAY,CAAC,QAAQ;QACvC,MAAM,eAAe,UAAU,YAAY;QAC3C,MAAM,WAAW,UAAU,QAAQ;QAEnC,IAAI,CAAC,cAAc;YACjB,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI,CAAC,UAAU;YACb,MAAM,IAAI,MAAM;QAClB;QAEA,iDAAiD;QACjD,MAAM,WAAW,MAAM,mHAAA,CAAA,oBAAiB,CAAC,IAAI,CAAC,iBAAiB;YAC7D;YACA;QACF;QAEA,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,IAAI,CAAC,WAAW,EAAE;YAChD,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,EAAE,WAAW,EAAE,GAAG,SAAS,IAAI;QACrC,MAAM,SAAS,SAAS,IAAI,CAAC,MAAM;QAEnC,uDAAuD;QACvD,uCAAmC;;QAEnC;QAEA,qCAAqC;QACrC,wCAAmC;YACjC,IAAI;gBACF,MAAM,EAAE,OAAO,EAAE,GAAG;gBACpB,CAAC,MAAM,SAAS,EAAE,GAAG,CAAC,gBAAgB,aAAa;oBACjD,UAAU;oBACV,QAAQ,oDAAyB;oBACjC,QAAQ,KAAK,KAAK,KAAK;oBACvB,MAAM;gBACR;YACF,EAAE,OAAO,aAAa;gBACpB,QAAQ,KAAK,CAAC,yBAAyB;YACvC,wCAAwC;YAC1C;QACF;QAEA,OAAO;YAAE,SAAS;YAAM;YAAa;QAAO;IAC9C,EAAE,OAAO,OAAO;QACd,sCAAsC;QACtC,uCAAmC;;QAMnC;QAEA,OAAO;YACL,SAAS;YACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAClD;IACF;AACF;AAEO,eAAe,uBAAuB,UAAkB;IAC7D,IAAI;QACF,qDAAqD;QACrD,MAAM,gBAAgB,CAAA,GAAA,uHAAA,CAAA,UAAO,AAAD,EAAE;QAE9B,MAAM,cAAc,CAAA,GAAA,mHAAA,CAAA,sBAAmB,AAAD;QACtC,MAAM,WAAW,MAAM,YAAY,IAAI,CAAC,yBAAyB;YAC/D,CAAC,gBAAgB,UAAU,cAAc,EAAE;QAC7C;QACA,MAAM,EAAE,OAAO,EAAE,GAAG,SAAS,IAAI;QAEjC,OAAO;YAAE,SAAS;YAAM;QAAQ;IAClC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oCAAoC;QAClD,OAAO;YACL,SAAS;YACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAClD;IACF;AACF;AAEO,eAAe,wBAAwB,OAAe,EAAE,GAAW;IACxE,IAAI;QACF,MAAM,cAAc,CAAA,GAAA,mHAAA,CAAA,sBAAmB,AAAD;QACtC,MAAM,WAAW,MAAM,YAAY,IAAI,CAAC,gCAAgC;YACtE;YACA;QACF;QACA,OAAO;YAAE,SAAS;YAAM,MAAM,SAAS,IAAI;QAAC;IAC9C,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,sCAAsC;QACpD,OAAO;YACL,SAAS;YACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAClD;IACF;AACF;AAEO,eAAe,cAAc,OAAe,EAAE,WAAmB;IACtE,IAAI;QACF,MAAM,cAAc,CAAA,GAAA,mHAAA,CAAA,sBAAmB,AAAD;QACtC,MAAM,WAAW,MAAM,YAAY,IAAI,CAAC,+BAA+B;YACrE;YACA;QACF;QACA,OAAO;YAAE,SAAS;YAAM,MAAM,SAAS,IAAI;QAAC;IAC9C,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0BAA0B;QACxC,OAAO;YACL,SAAS;YACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAClD;IACF;AACF;AAGO,eAAe,eACpB,eAAuB,EACvB,WAAmB,EACnB,WAAmB;IAEnB,IAAI;QACF,IAAI,CAAC,aAAa;YAChB,OAAO;gBACL,SAAS;gBACT,OAAO;YACT;QACF;QAEA,MAAM,cAAc,CAAA,GAAA,mHAAA,CAAA,sBAAmB,AAAD,EAAE;QACxC,MAAM,WAAW,MAAM,YAAY,GAAG,CAAC,yBAAyB;YAC9D;YACA;QACF;QAEA,OAAO;YACL,SAAS;YACT,SAAS,SAAS,IAAI,CAAC,OAAO,IAAI;QACpC;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QAEzC,mBAAmB;QACnB,IAAI,qIAAA,CAAA,UAAK,CAAC,YAAY,CAAC,UAAU,MAAM,QAAQ,EAAE;YAC/C,QAAQ,KAAK,CAAC,mBAAmB;gBAC/B,QAAQ,MAAM,QAAQ,CAAC,MAAM;gBAC7B,MAAM,MAAM,QAAQ,CAAC,IAAI;YAC3B;YAEA,sCAAsC;YACtC,IAAI,MAAM,QAAQ,CAAC,MAAM,KAAK,OAAO,MAAM,QAAQ,CAAC,MAAM,KAAK,KAAK;gBAClE,IAAI,MAAM,QAAQ,CAAC,IAAI,EAAE,SAAS;oBAChC,yCAAyC;oBACzC,OAAO;wBACL,SAAS;wBACT,OAAO,MAAM,QAAQ,CAAC,IAAI,CAAC,OAAO;oBACpC;gBACF;gBAEA,4EAA4E;gBAC5E,IAAI,MAAM,QAAQ,CAAC,IAAI,EAAE,UAAU,4BAA4B;oBAC7D,OAAO;wBACL,SAAS;wBACT,OAAO;oBACT;gBACF;YACF;QACF;QAEA,OAAO;YACL,SAAS;YACT,OACE,iBAAiB,QACb,MAAM,OAAO,GACb;QACR;IACF;AACF;AAGO,eAAe,qBAAqB,KAAa,EAAE,WAAmB;IAC3E,IAAI;QACF,MAAM,WAAW,MAAM,mHAAA,CAAA,UAAa,CAAC,IAAI,CAAC,gCAAgC;YACxE;YACA;QACF;QACA,OAAO;YACL,SAAS;YACT,SAAS,SAAS,IAAI,CAAC,OAAO,IAAI;QACpC;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kCAAkC;QAChD,OAAO;YACL,SAAS;YACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAClD;IACF;AACF;;;IAvVsB;IAqBA;IAiBA;IA2BA;IAoCA;IAuCA;IAkEA;IAqBA;IAiBA;IAkBA;IAgEA;;AAtUA,+OAAA;AAqBA,+OAAA;AAiBA,+OAAA;AA2BA,+OAAA;AAoCA,+OAAA;AAuCA,+OAAA;AAkEA,+OAAA;AAqBA,+OAAA;AAiBA,+OAAA;AAkBA,+OAAA;AAgEA,+OAAA", "debugId": null}}, {"offset": {"line": 1758, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/src/utils/relationshipCache.ts"], "sourcesContent": ["// Define a type for relationship data\r\ninterface RelationshipData {\r\n  status: string;\r\n  [key: string]: unknown;\r\n}\r\n\r\n// Cache for relationship data\r\nconst relationshipCache: Record<\r\n  string,\r\n  { data: RelationshipData; timestamp: number }\r\n> = {};\r\n\r\n// Cache expiration time in milliseconds (5 minutes)\r\nconst RELATIONSHIP_CACHE_EXPIRATION = 5 * 60 * 1000;\r\n\r\n// Function to check if cached relationship data is still valid\r\nexport function isRelationshipCacheValid(targetId: string): boolean {\r\n  if (!relationshipCache[targetId]) return false;\r\n\r\n  const now = Date.now();\r\n  const cacheTime = relationshipCache[targetId].timestamp;\r\n\r\n  return now - cacheTime < RELATIONSHIP_CACHE_EXPIRATION;\r\n}\r\n\r\n// Function to get relationship data from cache\r\nexport function getCachedRelationship(\r\n  targetId: string,\r\n): RelationshipData | null {\r\n  if (isRelationshipCacheValid(targetId)) {\r\n    return relationshipCache[targetId].data;\r\n  }\r\n  return null;\r\n}\r\n\r\n// Function to store relationship data in cache\r\nexport function cacheRelationship(\r\n  targetId: string,\r\n  data: RelationshipData,\r\n): void {\r\n  relationshipCache[targetId] = {\r\n    data,\r\n    timestamp: Date.now(),\r\n  };\r\n}\r\n\r\n// Function to remove relationship data from cache\r\nexport function removeCachedRelationship(targetId: string): void {\r\n  if (relationshipCache[targetId]) {\r\n    delete relationshipCache[targetId];\r\n  }\r\n}\r\n\r\n// Function to clear the entire relationship cache\r\nexport function clearRelationshipCache(): void {\r\n  Object.keys(relationshipCache).forEach((key) => {\r\n    delete relationshipCache[key];\r\n  });\r\n}\r\n"], "names": [], "mappings": "AAAA,sCAAsC;;;;;;;;AAMtC,8BAA8B;AAC9B,MAAM,oBAGF,CAAC;AAEL,oDAAoD;AACpD,MAAM,gCAAgC,IAAI,KAAK;AAGxC,SAAS,yBAAyB,QAAgB;IACvD,IAAI,CAAC,iBAAiB,CAAC,SAAS,EAAE,OAAO;IAEzC,MAAM,MAAM,KAAK,GAAG;IACpB,MAAM,YAAY,iBAAiB,CAAC,SAAS,CAAC,SAAS;IAEvD,OAAO,MAAM,YAAY;AAC3B;AAGO,SAAS,sBACd,QAAgB;IAEhB,IAAI,yBAAyB,WAAW;QACtC,OAAO,iBAAiB,CAAC,SAAS,CAAC,IAAI;IACzC;IACA,OAAO;AACT;AAGO,SAAS,kBACd,QAAgB,EAChB,IAAsB;IAEtB,iBAAiB,CAAC,SAAS,GAAG;QAC5B;QACA,WAAW,KAAK,GAAG;IACrB;AACF;AAGO,SAAS,yBAAyB,QAAgB;IACvD,IAAI,iBAAiB,CAAC,SAAS,EAAE;QAC/B,OAAO,iBAAiB,CAAC,SAAS;IACpC;AACF;AAGO,SAAS;IACd,OAAO,IAAI,CAAC,mBAAmB,OAAO,CAAC,CAAC;QACtC,OAAO,iBAAiB,CAAC,IAAI;IAC/B;AACF", "debugId": null}}, {"offset": {"line": 1804, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/src/actions/friend.action.ts"], "sourcesContent": ["\"use server\";\r\nimport axios from \"axios\";\r\nimport { createAxiosInstance } from \"@/lib/axios\";\r\n// No need to import Friend from @/types/base\r\nimport {\r\n  getCachedRelationship,\r\n  cacheRelationship,\r\n  removeCachedRelationship,\r\n  clearRelationshipCache,\r\n} from \"@/utils/relationshipCache\";\r\n\r\n// Define types based on the API response\r\ninterface UserInfo {\r\n  fullName: string;\r\n  profilePictureUrl: string;\r\n  statusMessage?: string;\r\n  lastSeen?: string;\r\n  gender?: string;\r\n  bio?: string;\r\n  dateOfBirth?: string;\r\n}\r\n\r\ninterface FriendInfo {\r\n  id: string;\r\n  email: string;\r\n  phoneNumber: string;\r\n  userInfo: UserInfo;\r\n}\r\n\r\ninterface FriendshipResponse {\r\n  friendshipId: string;\r\n  friend: FriendInfo;\r\n  since: string;\r\n}\r\n\r\ninterface FriendRequest {\r\n  id: string;\r\n  senderId: string;\r\n  receiverId: string;\r\n  status: string;\r\n  createdAt: string;\r\n  updatedAt: string;\r\n  sender: FriendInfo;\r\n  receiver: FriendInfo;\r\n  introduce?: string;\r\n}\r\n\r\n// Simplified Friend type for UI components\r\nexport interface SimpleFriend {\r\n  id: string;\r\n  fullName: string;\r\n  profilePictureUrl: string;\r\n  statusMessage?: string;\r\n  lastSeen?: string;\r\n  email?: string;\r\n  phoneNumber?: string;\r\n  gender?: string;\r\n  bio?: string;\r\n  dateOfBirth?: string;\r\n}\r\n\r\n// Define types based on the API response\r\n\r\ninterface FriendInfo {\r\n  id: string;\r\n  email: string;\r\n  phoneNumber: string;\r\n  userInfo: UserInfo;\r\n}\r\n\r\ninterface FriendshipResponse {\r\n  friendshipId: string;\r\n  friend: FriendInfo;\r\n  since: string;\r\n}\r\n\r\ninterface FriendRequest {\r\n  id: string;\r\n  senderId: string;\r\n  receiverId: string;\r\n  status: string;\r\n  createdAt: string;\r\n  updatedAt: string;\r\n  sender: FriendInfo;\r\n  receiver: FriendInfo;\r\n  introduce?: string;\r\n}\r\n\r\n// Simplified Friend type for UI components\r\nexport interface SimpleFriend {\r\n  id: string;\r\n  fullName: string;\r\n  profilePictureUrl: string;\r\n  statusMessage?: string;\r\n  lastSeen?: string;\r\n  email?: string;\r\n  phoneNumber?: string;\r\n  gender?: string;\r\n  bio?: string;\r\n  dateOfBirth?: string;\r\n}\r\n\r\n// Lấy danh sách bạn bè của người dùng hiện tại\r\nexport async function getFriendsList(token?: string) {\r\n  try {\r\n    console.log(\r\n      \"Token received in getFriendsList:\",\r\n      token ? `Token exists: ${token.substring(0, 10)}...` : \"No token\",\r\n    );\r\n    const serverAxios = createAxiosInstance(token);\r\n    console.log(\r\n      \"Authorization header:\",\r\n      serverAxios.defaults.headers.common[\"Authorization\"],\r\n    );\r\n    const response = await serverAxios.get(\"/friends/list\");\r\n    const friendships: FriendshipResponse[] = response.data;\r\n\r\n    // Transform the API response to the format expected by UI components\r\n    const friends: SimpleFriend[] = friendships.map((friendship) => ({\r\n      id: friendship.friend.id,\r\n      fullName: friendship.friend.userInfo.fullName,\r\n      profilePictureUrl: friendship.friend.userInfo.profilePictureUrl,\r\n      statusMessage: friendship.friend.userInfo.statusMessage,\r\n      lastSeen: friendship.friend.userInfo.lastSeen,\r\n      email: friendship.friend.email,\r\n      phoneNumber: friendship.friend.phoneNumber,\r\n      gender: friendship.friend.userInfo.gender,\r\n      bio: friendship.friend.userInfo.bio,\r\n      dateOfBirth: friendship.friend.userInfo.dateOfBirth,\r\n    }));\r\n\r\n    return { success: true, friends };\r\n  } catch (error) {\r\n    console.error(\"Get friends list failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\n// Lấy danh sách lời mời kết bạn đã nhận\r\nexport async function getReceivedFriendRequests(token?: string) {\r\n  try {\r\n    const serverAxios = createAxiosInstance(token);\r\n    const response = await serverAxios.get(\"/friends/requests/received\");\r\n\r\n    // Log raw API response\r\n    console.log(\"Raw received friend requests:\", response.data);\r\n\r\n    // Transform the API response to the format expected by UI components\r\n    const requests = response.data.map((request: FriendRequest) => ({\r\n      id: request.id,\r\n      fullName: request.sender.userInfo.fullName,\r\n      profilePictureUrl: request.sender.userInfo.profilePictureUrl,\r\n      // Get the introduce message from the API response\r\n      message: request.introduce || \"\",\r\n      timeAgo: new Date(request.createdAt).toLocaleDateString(),\r\n      // Add senderId for fetching complete user data\r\n      senderId: request.sender.id,\r\n    }));\r\n\r\n    return { success: true, requests };\r\n  } catch (error) {\r\n    console.error(\"Get received friend requests failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\n// Lấy danh sách lời mời kết bạn đã gửi\r\nexport async function getSentFriendRequests(token?: string) {\r\n  try {\r\n    const serverAxios = createAxiosInstance(token);\r\n    const response = await serverAxios.get(\"/friends/requests/sent\");\r\n\r\n    // Log raw API response\r\n    console.log(\"Raw sent friend requests:\", response.data);\r\n\r\n    // Transform the API response to the format expected by UI components\r\n    const requests = response.data.map((request: FriendRequest) => ({\r\n      id: request.id,\r\n      fullName: request.receiver.userInfo.fullName,\r\n      profilePictureUrl: request.receiver.userInfo.profilePictureUrl,\r\n      timeAgo: new Date(request.createdAt).toLocaleDateString(),\r\n      // Add receiverId for fetching complete user data\r\n      receiverId: request.receiver.id,\r\n    }));\r\n\r\n    return { success: true, requests };\r\n  } catch (error) {\r\n    console.error(\"Get sent friend requests failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\n// Gửi lời mời kết bạn\r\nexport async function sendFriendRequest(\r\n  userId: string,\r\n  introduce?: string,\r\n  token?: string,\r\n) {\r\n  try {\r\n    console.log(\r\n      `Sending friend request to user ${userId} with token: ${!!token}`,\r\n    );\r\n    const serverAxios = createAxiosInstance(token);\r\n\r\n    // Tạo payload theo đúng format API yêu cầu\r\n    const payload: { receiverId: string; introduce?: string } = {\r\n      receiverId: userId,\r\n    };\r\n\r\n    // Thêm introduce nếu có\r\n    if (introduce && introduce.trim()) {\r\n      payload.introduce = introduce.trim();\r\n    }\r\n\r\n    console.log(\"Friend request payload:\", payload);\r\n    const response = await serverAxios.post(\"/friends/request\", payload);\r\n    console.log(\"Friend request response:\", response.data);\r\n\r\n    // Update relationship cache to reflect the new pending status\r\n    removeCachedRelationship(userId);\r\n\r\n    return { success: true, data: response.data };\r\n  } catch (error) {\r\n    console.error(\"Send friend request failed:\", error);\r\n\r\n    // Log chi tiết hơn về lỗi\r\n    if (axios.isAxiosError(error) && error.response) {\r\n      console.error(\"Error response:\", {\r\n        status: error.response.status,\r\n        data: error.response.data,\r\n        headers: error.response.headers,\r\n      });\r\n\r\n      // Return specific error message based on status code\r\n      if (error.response.status === 401) {\r\n        return {\r\n          success: false,\r\n          error: \"Bạn cần đăng nhập để thực hiện hành động này\",\r\n        };\r\n      }\r\n\r\n      if (error.response.status === 403) {\r\n        return {\r\n          success: false,\r\n          error: \"Bạn không có quyền thực hiện hành động này\",\r\n        };\r\n      }\r\n\r\n      if (error.response.status === 404) {\r\n        return {\r\n          success: false,\r\n          error: \"Không tìm thấy người dùng\",\r\n        };\r\n      }\r\n\r\n      if (error.response.data?.message) {\r\n        return {\r\n          success: false,\r\n          error: error.response.data.message,\r\n        };\r\n      }\r\n    }\r\n\r\n    // Network errors or other errors\r\n    if (error instanceof Error) {\r\n      return {\r\n        success: false,\r\n        error: error.message,\r\n      };\r\n    }\r\n\r\n    return {\r\n      success: false,\r\n      error: \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\n// Phản hồi lời mời kết bạn (chấp nhận, từ chối, block)\r\nexport async function respondToFriendRequest(\r\n  requestId: string,\r\n  status: \"ACCEPTED\" | \"DECLINED\" | \"BLOCKED\",\r\n  token?: string,\r\n) {\r\n  try {\r\n    console.log(\r\n      `respondToFriendRequest: requestId=${requestId}, status=${status}, hasToken=${!!token}`,\r\n    );\r\n    const serverAxios = createAxiosInstance(token);\r\n    const payload = {\r\n      requestId,\r\n      status,\r\n    };\r\n    console.log(\"Request payload:\", payload);\r\n    const response = await serverAxios.put(\"/friends/respond\", payload);\r\n    console.log(\"API response:\", response.data);\r\n\r\n    // Clear all relationship caches since we don't know which user this affects\r\n    // This is a simple approach - a more sophisticated one would track which user's request this is\r\n    clearRelationshipCache();\r\n\r\n    return { success: true, data: response.data };\r\n  } catch (error) {\r\n    console.error(\"Respond to friend request failed:\", error);\r\n\r\n    // Log chi tiết hơn về lỗi\r\n    if (axios.isAxiosError(error) && error.response) {\r\n      console.error(\"Error response:\", {\r\n        status: error.response.status,\r\n        data: error.response.data,\r\n        headers: error.response.headers,\r\n      });\r\n\r\n      // Return specific error message based on status code\r\n      if (error.response.status === 401) {\r\n        return {\r\n          success: false,\r\n          error: \"Bạn cần đăng nhập để thực hiện hành động này\",\r\n        };\r\n      }\r\n\r\n      if (error.response.status === 403) {\r\n        return {\r\n          success: false,\r\n          error: \"Bạn không có quyền thực hiện hành động này\",\r\n        };\r\n      }\r\n\r\n      if (error.response.status === 404) {\r\n        return {\r\n          success: false,\r\n          error: \"Không tìm thấy lời mời kết bạn\",\r\n        };\r\n      }\r\n\r\n      if (error.response.data?.message) {\r\n        return {\r\n          success: false,\r\n          error: error.response.data.message,\r\n        };\r\n      }\r\n    }\r\n\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\n// Chấp nhận lời mời kết bạn (wrapper function for backward compatibility)\r\nexport async function acceptFriendRequest(requestId: string, token?: string) {\r\n  return respondToFriendRequest(requestId, \"ACCEPTED\", token);\r\n}\r\n\r\n// Từ chối lời mời kết bạn (wrapper function for backward compatibility)\r\nexport async function rejectFriendRequest(requestId: string, token?: string) {\r\n  console.log(\r\n    \"rejectFriendRequest called with requestId:\",\r\n    requestId,\r\n    \"and token:\",\r\n    !!token,\r\n  );\r\n  const result = await respondToFriendRequest(requestId, \"DECLINED\", token);\r\n  console.log(\"respondToFriendRequest result:\", result);\r\n  return result;\r\n}\r\n\r\n// Xóa bạn bè\r\nexport async function removeFriend(friendId: string, token?: string) {\r\n  try {\r\n    const serverAxios = createAxiosInstance(token);\r\n    const response = await serverAxios.delete(`/friends/${friendId}`);\r\n\r\n    // Update relationship cache\r\n    removeCachedRelationship(friendId);\r\n\r\n    return { success: true, data: response.data };\r\n  } catch (error) {\r\n    console.error(\"Remove friend failed:\", error);\r\n\r\n    // Log chi tiết hơn về lỗi\r\n    if (axios.isAxiosError(error) && error.response) {\r\n      console.error(\"Error response:\", {\r\n        status: error.response.status,\r\n        data: error.response.data,\r\n        headers: error.response.headers,\r\n      });\r\n\r\n      // Return specific error message based on status code\r\n      if (error.response.status === 401) {\r\n        return {\r\n          success: false,\r\n          error: \"Bạn cần đăng nhập để thực hiện hành động này\",\r\n        };\r\n      }\r\n\r\n      if (error.response.status === 403) {\r\n        return {\r\n          success: false,\r\n          error: \"Bạn không có quyền thực hiện hành động này\",\r\n        };\r\n      }\r\n\r\n      if (error.response.status === 404) {\r\n        return {\r\n          success: false,\r\n          error: \"Không tìm thấy người dùng\",\r\n        };\r\n      }\r\n\r\n      if (error.response.data?.message) {\r\n        return {\r\n          success: false,\r\n          error: error.response.data.message,\r\n        };\r\n      }\r\n    }\r\n\r\n    // Network errors or other errors\r\n    if (error instanceof Error) {\r\n      return {\r\n        success: false,\r\n        error: error.message,\r\n      };\r\n    }\r\n\r\n    return {\r\n      success: false,\r\n      error: \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\n// Hủy lời mời kết bạn đã gửi\r\nexport async function cancelFriendRequest(requestId: string, token?: string) {\r\n  try {\r\n    const serverAxios = createAxiosInstance(token);\r\n    const response = await serverAxios.delete(`/friends/request/${requestId}`);\r\n\r\n    // Clear all relationship caches since we don't know which user this affects\r\n    clearRelationshipCache();\r\n\r\n    return { success: true, data: response.data };\r\n  } catch (error) {\r\n    console.error(\"Cancel friend request failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\n// Chặn người dùng\r\nexport async function blockUser(userId: string, token?: string) {\r\n  try {\r\n    const serverAxios = createAxiosInstance(token);\r\n    const response = await serverAxios.post(`/friends/block/${userId}`);\r\n\r\n    // Update relationship cache\r\n    removeCachedRelationship(userId);\r\n\r\n    return { success: true, data: response.data };\r\n  } catch (error) {\r\n    console.error(\"Block user failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\n// Bỏ chặn người dùng\r\nexport async function unblockUser(userId: string, token?: string) {\r\n  try {\r\n    const serverAxios = createAxiosInstance(token);\r\n    const response = await serverAxios.delete(`/friends/block/${userId}`);\r\n\r\n    // Update relationship cache\r\n    removeCachedRelationship(userId);\r\n\r\n    return { success: true, data: response.data };\r\n  } catch (error) {\r\n    console.error(\"Unblock user failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\n// Define type for blocked user response\r\ninterface BlockedUserResponse {\r\n  id: string;\r\n  senderId: string;\r\n  receiverId: string;\r\n  status: string;\r\n  createdAt: string;\r\n  updatedAt: string;\r\n  receiver: FriendInfo;\r\n}\r\n\r\n// Lấy danh sách người dùng đã chặn\r\nexport async function getBlockedUsers(token?: string) {\r\n  try {\r\n    const serverAxios = createAxiosInstance(token);\r\n    const response = await serverAxios.get(\"/friends/blocked\");\r\n\r\n    // Transform the API response to the format expected by UI components\r\n    const users: SimpleFriend[] = response.data.map(\r\n      (item: BlockedUserResponse) => ({\r\n        id: item.receiver.id,\r\n        fullName: item.receiver.userInfo.fullName,\r\n        profilePictureUrl: item.receiver.userInfo.profilePictureUrl,\r\n        email: item.receiver.email,\r\n        phoneNumber: item.receiver.phoneNumber,\r\n      }),\r\n    );\r\n\r\n    return { success: true, users };\r\n  } catch (error) {\r\n    console.error(\"Get blocked users failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\n// Batch fetch relationships for multiple users is implemented here\r\n// The cache functions are imported from utils/relationshipCache.ts\r\n\r\n// Batch fetch relationships for multiple users\r\nexport async function batchGetRelationships(userIds: string[], token?: string) {\r\n  // Filter out duplicate IDs\r\n  const uniqueIds = [...new Set(userIds)];\r\n\r\n  // Check which relationships are already in cache\r\n  const cachedRelationships: Record<string, { status: string }> = {};\r\n  const idsToFetch: string[] = [];\r\n\r\n  uniqueIds.forEach((id) => {\r\n    const cachedData = getCachedRelationship(id);\r\n    if (cachedData) {\r\n      cachedRelationships[id] = cachedData;\r\n    } else {\r\n      idsToFetch.push(id);\r\n    }\r\n  });\r\n\r\n  // If all relationships are in cache, return immediately\r\n  if (idsToFetch.length === 0) {\r\n    console.log(`All ${uniqueIds.length} relationships found in cache`);\r\n    return { success: true, relationships: cachedRelationships };\r\n  }\r\n\r\n  // Otherwise, fetch the remaining relationships\r\n  try {\r\n    console.log(`Batch fetching ${idsToFetch.length} relationships`);\r\n\r\n    // Fetch each relationship individually (could be optimized with a batch API endpoint)\r\n    const fetchPromises = idsToFetch.map((id) => getRelationship(id, token));\r\n    const results = await Promise.all(fetchPromises);\r\n\r\n    // Process results\r\n    results.forEach((result, index) => {\r\n      if (result.success && result.data) {\r\n        const userId = idsToFetch[index];\r\n        cachedRelationships[userId] = result.data;\r\n      }\r\n    });\r\n\r\n    return { success: true, relationships: cachedRelationships };\r\n  } catch (error) {\r\n    console.error(\"Batch get relationships failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n      relationships: cachedRelationships, // Return any cached relationships we did find\r\n    };\r\n  }\r\n}\r\n\r\n// Lấy mối quan hệ với một người dùng cụ thể\r\nexport async function getRelationship(targetId: string, token?: string) {\r\n  try {\r\n    // Check if relationship data is in cache and still valid\r\n    const cachedData = getCachedRelationship(targetId);\r\n    if (cachedData) {\r\n      console.log(`Using cached relationship data for user ID: ${targetId}`);\r\n      return { success: true, data: cachedData };\r\n    }\r\n\r\n    // Sử dụng serverAxios để gửi token xác thực\r\n    const serverAxios = createAxiosInstance(token);\r\n    const response = await serverAxios.get(`/friends/relationship/${targetId}`);\r\n    console.log(\"Relationship response:\", response.data);\r\n\r\n    // Store relationship data in cache\r\n    cacheRelationship(targetId, response.data);\r\n\r\n    return { success: true, data: response.data };\r\n  } catch (error) {\r\n    console.error(\"Get relationship failed:\", error);\r\n\r\n    // Log chi tiết hơn về lỗi\r\n    if (axios.isAxiosError(error) && error.response) {\r\n      console.error(\"Error response:\", {\r\n        status: error.response.status,\r\n        data: error.response.data,\r\n        headers: error.response.headers,\r\n      });\r\n\r\n      // Return specific error message based on status code\r\n      if (error.response.status === 401) {\r\n        return {\r\n          success: false,\r\n          error: \"Bạn cần đăng nhập để thực hiện hành động này\",\r\n        };\r\n      }\r\n\r\n      if (error.response.status === 403) {\r\n        return {\r\n          success: false,\r\n          error: \"Bạn không có quyền thực hiện hành động này\",\r\n        };\r\n      }\r\n\r\n      if (error.response.status === 404) {\r\n        return {\r\n          success: false,\r\n          error: \"Không tìm thấy người dùng\",\r\n        };\r\n      }\r\n\r\n      if (error.response.data?.message) {\r\n        return {\r\n          success: false,\r\n          error: error.response.data.message,\r\n        };\r\n      }\r\n    }\r\n\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AACA;AACA;AACA,6CAA6C;AAC7C;;;;;;;AAmGO,eAAe,eAAe,KAAc;IACjD,IAAI;QACF,QAAQ,GAAG,CACT,qCACA,QAAQ,CAAC,cAAc,EAAE,MAAM,SAAS,CAAC,GAAG,IAAI,GAAG,CAAC,GAAG;QAEzD,MAAM,cAAc,CAAA,GAAA,mHAAA,CAAA,sBAAmB,AAAD,EAAE;QACxC,QAAQ,GAAG,CACT,yBACA,YAAY,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,gBAAgB;QAEtD,MAAM,WAAW,MAAM,YAAY,GAAG,CAAC;QACvC,MAAM,cAAoC,SAAS,IAAI;QAEvD,qEAAqE;QACrE,MAAM,UAA0B,YAAY,GAAG,CAAC,CAAC,aAAe,CAAC;gBAC/D,IAAI,WAAW,MAAM,CAAC,EAAE;gBACxB,UAAU,WAAW,MAAM,CAAC,QAAQ,CAAC,QAAQ;gBAC7C,mBAAmB,WAAW,MAAM,CAAC,QAAQ,CAAC,iBAAiB;gBAC/D,eAAe,WAAW,MAAM,CAAC,QAAQ,CAAC,aAAa;gBACvD,UAAU,WAAW,MAAM,CAAC,QAAQ,CAAC,QAAQ;gBAC7C,OAAO,WAAW,MAAM,CAAC,KAAK;gBAC9B,aAAa,WAAW,MAAM,CAAC,WAAW;gBAC1C,QAAQ,WAAW,MAAM,CAAC,QAAQ,CAAC,MAAM;gBACzC,KAAK,WAAW,MAAM,CAAC,QAAQ,CAAC,GAAG;gBACnC,aAAa,WAAW,MAAM,CAAC,QAAQ,CAAC,WAAW;YACrD,CAAC;QAED,OAAO;YAAE,SAAS;YAAM;QAAQ;IAClC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,OAAO;YACL,SAAS;YACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAClD;IACF;AACF;AAGO,eAAe,0BAA0B,KAAc;IAC5D,IAAI;QACF,MAAM,cAAc,CAAA,GAAA,mHAAA,CAAA,sBAAmB,AAAD,EAAE;QACxC,MAAM,WAAW,MAAM,YAAY,GAAG,CAAC;QAEvC,uBAAuB;QACvB,QAAQ,GAAG,CAAC,iCAAiC,SAAS,IAAI;QAE1D,qEAAqE;QACrE,MAAM,WAAW,SAAS,IAAI,CAAC,GAAG,CAAC,CAAC,UAA2B,CAAC;gBAC9D,IAAI,QAAQ,EAAE;gBACd,UAAU,QAAQ,MAAM,CAAC,QAAQ,CAAC,QAAQ;gBAC1C,mBAAmB,QAAQ,MAAM,CAAC,QAAQ,CAAC,iBAAiB;gBAC5D,kDAAkD;gBAClD,SAAS,QAAQ,SAAS,IAAI;gBAC9B,SAAS,IAAI,KAAK,QAAQ,SAAS,EAAE,kBAAkB;gBACvD,+CAA+C;gBAC/C,UAAU,QAAQ,MAAM,CAAC,EAAE;YAC7B,CAAC;QAED,OAAO;YAAE,SAAS;YAAM;QAAS;IACnC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wCAAwC;QACtD,OAAO;YACL,SAAS;YACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAClD;IACF;AACF;AAGO,eAAe,sBAAsB,KAAc;IACxD,IAAI;QACF,MAAM,cAAc,CAAA,GAAA,mHAAA,CAAA,sBAAmB,AAAD,EAAE;QACxC,MAAM,WAAW,MAAM,YAAY,GAAG,CAAC;QAEvC,uBAAuB;QACvB,QAAQ,GAAG,CAAC,6BAA6B,SAAS,IAAI;QAEtD,qEAAqE;QACrE,MAAM,WAAW,SAAS,IAAI,CAAC,GAAG,CAAC,CAAC,UAA2B,CAAC;gBAC9D,IAAI,QAAQ,EAAE;gBACd,UAAU,QAAQ,QAAQ,CAAC,QAAQ,CAAC,QAAQ;gBAC5C,mBAAmB,QAAQ,QAAQ,CAAC,QAAQ,CAAC,iBAAiB;gBAC9D,SAAS,IAAI,KAAK,QAAQ,SAAS,EAAE,kBAAkB;gBACvD,iDAAiD;gBACjD,YAAY,QAAQ,QAAQ,CAAC,EAAE;YACjC,CAAC;QAED,OAAO;YAAE,SAAS;YAAM;QAAS;IACnC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oCAAoC;QAClD,OAAO;YACL,SAAS;YACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAClD;IACF;AACF;AAGO,eAAe,kBACpB,MAAc,EACd,SAAkB,EAClB,KAAc;IAEd,IAAI;QACF,QAAQ,GAAG,CACT,CAAC,+BAA+B,EAAE,OAAO,aAAa,EAAE,CAAC,CAAC,OAAO;QAEnE,MAAM,cAAc,CAAA,GAAA,mHAAA,CAAA,sBAAmB,AAAD,EAAE;QAExC,2CAA2C;QAC3C,MAAM,UAAsD;YAC1D,YAAY;QACd;QAEA,wBAAwB;QACxB,IAAI,aAAa,UAAU,IAAI,IAAI;YACjC,QAAQ,SAAS,GAAG,UAAU,IAAI;QACpC;QAEA,QAAQ,GAAG,CAAC,2BAA2B;QACvC,MAAM,WAAW,MAAM,YAAY,IAAI,CAAC,oBAAoB;QAC5D,QAAQ,GAAG,CAAC,4BAA4B,SAAS,IAAI;QAErD,8DAA8D;QAC9D,CAAA,GAAA,iIAAA,CAAA,2BAAwB,AAAD,EAAE;QAEzB,OAAO;YAAE,SAAS;YAAM,MAAM,SAAS,IAAI;QAAC;IAC9C,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,+BAA+B;QAE7C,0BAA0B;QAC1B,IAAI,qIAAA,CAAA,UAAK,CAAC,YAAY,CAAC,UAAU,MAAM,QAAQ,EAAE;YAC/C,QAAQ,KAAK,CAAC,mBAAmB;gBAC/B,QAAQ,MAAM,QAAQ,CAAC,MAAM;gBAC7B,MAAM,MAAM,QAAQ,CAAC,IAAI;gBACzB,SAAS,MAAM,QAAQ,CAAC,OAAO;YACjC;YAEA,qDAAqD;YACrD,IAAI,MAAM,QAAQ,CAAC,MAAM,KAAK,KAAK;gBACjC,OAAO;oBACL,SAAS;oBACT,OAAO;gBACT;YACF;YAEA,IAAI,MAAM,QAAQ,CAAC,MAAM,KAAK,KAAK;gBACjC,OAAO;oBACL,SAAS;oBACT,OAAO;gBACT;YACF;YAEA,IAAI,MAAM,QAAQ,CAAC,MAAM,KAAK,KAAK;gBACjC,OAAO;oBACL,SAAS;oBACT,OAAO;gBACT;YACF;YAEA,IAAI,MAAM,QAAQ,CAAC,IAAI,EAAE,SAAS;gBAChC,OAAO;oBACL,SAAS;oBACT,OAAO,MAAM,QAAQ,CAAC,IAAI,CAAC,OAAO;gBACpC;YACF;QACF;QAEA,iCAAiC;QACjC,IAAI,iBAAiB,OAAO;YAC1B,OAAO;gBACL,SAAS;gBACT,OAAO,MAAM,OAAO;YACtB;QACF;QAEA,OAAO;YACL,SAAS;YACT,OAAO;QACT;IACF;AACF;AAGO,eAAe,uBACpB,SAAiB,EACjB,MAA2C,EAC3C,KAAc;IAEd,IAAI;QACF,QAAQ,GAAG,CACT,CAAC,kCAAkC,EAAE,UAAU,SAAS,EAAE,OAAO,WAAW,EAAE,CAAC,CAAC,OAAO;QAEzF,MAAM,cAAc,CAAA,GAAA,mHAAA,CAAA,sBAAmB,AAAD,EAAE;QACxC,MAAM,UAAU;YACd;YACA;QACF;QACA,QAAQ,GAAG,CAAC,oBAAoB;QAChC,MAAM,WAAW,MAAM,YAAY,GAAG,CAAC,oBAAoB;QAC3D,QAAQ,GAAG,CAAC,iBAAiB,SAAS,IAAI;QAE1C,4EAA4E;QAC5E,gGAAgG;QAChG,CAAA,GAAA,iIAAA,CAAA,yBAAsB,AAAD;QAErB,OAAO;YAAE,SAAS;YAAM,MAAM,SAAS,IAAI;QAAC;IAC9C,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qCAAqC;QAEnD,0BAA0B;QAC1B,IAAI,qIAAA,CAAA,UAAK,CAAC,YAAY,CAAC,UAAU,MAAM,QAAQ,EAAE;YAC/C,QAAQ,KAAK,CAAC,mBAAmB;gBAC/B,QAAQ,MAAM,QAAQ,CAAC,MAAM;gBAC7B,MAAM,MAAM,QAAQ,CAAC,IAAI;gBACzB,SAAS,MAAM,QAAQ,CAAC,OAAO;YACjC;YAEA,qDAAqD;YACrD,IAAI,MAAM,QAAQ,CAAC,MAAM,KAAK,KAAK;gBACjC,OAAO;oBACL,SAAS;oBACT,OAAO;gBACT;YACF;YAEA,IAAI,MAAM,QAAQ,CAAC,MAAM,KAAK,KAAK;gBACjC,OAAO;oBACL,SAAS;oBACT,OAAO;gBACT;YACF;YAEA,IAAI,MAAM,QAAQ,CAAC,MAAM,KAAK,KAAK;gBACjC,OAAO;oBACL,SAAS;oBACT,OAAO;gBACT;YACF;YAEA,IAAI,MAAM,QAAQ,CAAC,IAAI,EAAE,SAAS;gBAChC,OAAO;oBACL,SAAS;oBACT,OAAO,MAAM,QAAQ,CAAC,IAAI,CAAC,OAAO;gBACpC;YACF;QACF;QAEA,OAAO;YACL,SAAS;YACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAClD;IACF;AACF;AAGO,eAAe,oBAAoB,SAAiB,EAAE,KAAc;IACzE,OAAO,uBAAuB,WAAW,YAAY;AACvD;AAGO,eAAe,oBAAoB,SAAiB,EAAE,KAAc;IACzE,QAAQ,GAAG,CACT,8CACA,WACA,cACA,CAAC,CAAC;IAEJ,MAAM,SAAS,MAAM,uBAAuB,WAAW,YAAY;IACnE,QAAQ,GAAG,CAAC,kCAAkC;IAC9C,OAAO;AACT;AAGO,eAAe,aAAa,QAAgB,EAAE,KAAc;IACjE,IAAI;QACF,MAAM,cAAc,CAAA,GAAA,mHAAA,CAAA,sBAAmB,AAAD,EAAE;QACxC,MAAM,WAAW,MAAM,YAAY,MAAM,CAAC,CAAC,SAAS,EAAE,UAAU;QAEhE,4BAA4B;QAC5B,CAAA,GAAA,iIAAA,CAAA,2BAAwB,AAAD,EAAE;QAEzB,OAAO;YAAE,SAAS;YAAM,MAAM,SAAS,IAAI;QAAC;IAC9C,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yBAAyB;QAEvC,0BAA0B;QAC1B,IAAI,qIAAA,CAAA,UAAK,CAAC,YAAY,CAAC,UAAU,MAAM,QAAQ,EAAE;YAC/C,QAAQ,KAAK,CAAC,mBAAmB;gBAC/B,QAAQ,MAAM,QAAQ,CAAC,MAAM;gBAC7B,MAAM,MAAM,QAAQ,CAAC,IAAI;gBACzB,SAAS,MAAM,QAAQ,CAAC,OAAO;YACjC;YAEA,qDAAqD;YACrD,IAAI,MAAM,QAAQ,CAAC,MAAM,KAAK,KAAK;gBACjC,OAAO;oBACL,SAAS;oBACT,OAAO;gBACT;YACF;YAEA,IAAI,MAAM,QAAQ,CAAC,MAAM,KAAK,KAAK;gBACjC,OAAO;oBACL,SAAS;oBACT,OAAO;gBACT;YACF;YAEA,IAAI,MAAM,QAAQ,CAAC,MAAM,KAAK,KAAK;gBACjC,OAAO;oBACL,SAAS;oBACT,OAAO;gBACT;YACF;YAEA,IAAI,MAAM,QAAQ,CAAC,IAAI,EAAE,SAAS;gBAChC,OAAO;oBACL,SAAS;oBACT,OAAO,MAAM,QAAQ,CAAC,IAAI,CAAC,OAAO;gBACpC;YACF;QACF;QAEA,iCAAiC;QACjC,IAAI,iBAAiB,OAAO;YAC1B,OAAO;gBACL,SAAS;gBACT,OAAO,MAAM,OAAO;YACtB;QACF;QAEA,OAAO;YACL,SAAS;YACT,OAAO;QACT;IACF;AACF;AAGO,eAAe,oBAAoB,SAAiB,EAAE,KAAc;IACzE,IAAI;QACF,MAAM,cAAc,CAAA,GAAA,mHAAA,CAAA,sBAAmB,AAAD,EAAE;QACxC,MAAM,WAAW,MAAM,YAAY,MAAM,CAAC,CAAC,iBAAiB,EAAE,WAAW;QAEzE,4EAA4E;QAC5E,CAAA,GAAA,iIAAA,CAAA,yBAAsB,AAAD;QAErB,OAAO;YAAE,SAAS;YAAM,MAAM,SAAS,IAAI;QAAC;IAC9C,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iCAAiC;QAC/C,OAAO;YACL,SAAS;YACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAClD;IACF;AACF;AAGO,eAAe,UAAU,MAAc,EAAE,KAAc;IAC5D,IAAI;QACF,MAAM,cAAc,CAAA,GAAA,mHAAA,CAAA,sBAAmB,AAAD,EAAE;QACxC,MAAM,WAAW,MAAM,YAAY,IAAI,CAAC,CAAC,eAAe,EAAE,QAAQ;QAElE,4BAA4B;QAC5B,CAAA,GAAA,iIAAA,CAAA,2BAAwB,AAAD,EAAE;QAEzB,OAAO;YAAE,SAAS;YAAM,MAAM,SAAS,IAAI;QAAC;IAC9C,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,sBAAsB;QACpC,OAAO;YACL,SAAS;YACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAClD;IACF;AACF;AAGO,eAAe,YAAY,MAAc,EAAE,KAAc;IAC9D,IAAI;QACF,MAAM,cAAc,CAAA,GAAA,mHAAA,CAAA,sBAAmB,AAAD,EAAE;QACxC,MAAM,WAAW,MAAM,YAAY,MAAM,CAAC,CAAC,eAAe,EAAE,QAAQ;QAEpE,4BAA4B;QAC5B,CAAA,GAAA,iIAAA,CAAA,2BAAwB,AAAD,EAAE;QAEzB,OAAO;YAAE,SAAS;YAAM,MAAM,SAAS,IAAI;QAAC;IAC9C,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wBAAwB;QACtC,OAAO;YACL,SAAS;YACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAClD;IACF;AACF;AAcO,eAAe,gBAAgB,KAAc;IAClD,IAAI;QACF,MAAM,cAAc,CAAA,GAAA,mHAAA,CAAA,sBAAmB,AAAD,EAAE;QACxC,MAAM,WAAW,MAAM,YAAY,GAAG,CAAC;QAEvC,qEAAqE;QACrE,MAAM,QAAwB,SAAS,IAAI,CAAC,GAAG,CAC7C,CAAC,OAA8B,CAAC;gBAC9B,IAAI,KAAK,QAAQ,CAAC,EAAE;gBACpB,UAAU,KAAK,QAAQ,CAAC,QAAQ,CAAC,QAAQ;gBACzC,mBAAmB,KAAK,QAAQ,CAAC,QAAQ,CAAC,iBAAiB;gBAC3D,OAAO,KAAK,QAAQ,CAAC,KAAK;gBAC1B,aAAa,KAAK,QAAQ,CAAC,WAAW;YACxC,CAAC;QAGH,OAAO;YAAE,SAAS;YAAM;QAAM;IAChC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,OAAO;YACL,SAAS;YACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAClD;IACF;AACF;AAMO,eAAe,sBAAsB,OAAiB,EAAE,KAAc;IAC3E,2BAA2B;IAC3B,MAAM,YAAY;WAAI,IAAI,IAAI;KAAS;IAEvC,iDAAiD;IACjD,MAAM,sBAA0D,CAAC;IACjE,MAAM,aAAuB,EAAE;IAE/B,UAAU,OAAO,CAAC,CAAC;QACjB,MAAM,aAAa,CAAA,GAAA,iIAAA,CAAA,wBAAqB,AAAD,EAAE;QACzC,IAAI,YAAY;YACd,mBAAmB,CAAC,GAAG,GAAG;QAC5B,OAAO;YACL,WAAW,IAAI,CAAC;QAClB;IACF;IAEA,wDAAwD;IACxD,IAAI,WAAW,MAAM,KAAK,GAAG;QAC3B,QAAQ,GAAG,CAAC,CAAC,IAAI,EAAE,UAAU,MAAM,CAAC,6BAA6B,CAAC;QAClE,OAAO;YAAE,SAAS;YAAM,eAAe;QAAoB;IAC7D;IAEA,+CAA+C;IAC/C,IAAI;QACF,QAAQ,GAAG,CAAC,CAAC,eAAe,EAAE,WAAW,MAAM,CAAC,cAAc,CAAC;QAE/D,sFAAsF;QACtF,MAAM,gBAAgB,WAAW,GAAG,CAAC,CAAC,KAAO,gBAAgB,IAAI;QACjE,MAAM,UAAU,MAAM,QAAQ,GAAG,CAAC;QAElC,kBAAkB;QAClB,QAAQ,OAAO,CAAC,CAAC,QAAQ;YACvB,IAAI,OAAO,OAAO,IAAI,OAAO,IAAI,EAAE;gBACjC,MAAM,SAAS,UAAU,CAAC,MAAM;gBAChC,mBAAmB,CAAC,OAAO,GAAG,OAAO,IAAI;YAC3C;QACF;QAEA,OAAO;YAAE,SAAS;YAAM,eAAe;QAAoB;IAC7D,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mCAAmC;QACjD,OAAO;YACL,SAAS;YACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAChD,eAAe;QACjB;IACF;AACF;AAGO,eAAe,gBAAgB,QAAgB,EAAE,KAAc;IACpE,IAAI;QACF,yDAAyD;QACzD,MAAM,aAAa,CAAA,GAAA,iIAAA,CAAA,wBAAqB,AAAD,EAAE;QACzC,IAAI,YAAY;YACd,QAAQ,GAAG,CAAC,CAAC,4CAA4C,EAAE,UAAU;YACrE,OAAO;gBAAE,SAAS;gBAAM,MAAM;YAAW;QAC3C;QAEA,4CAA4C;QAC5C,MAAM,cAAc,CAAA,GAAA,mHAAA,CAAA,sBAAmB,AAAD,EAAE;QACxC,MAAM,WAAW,MAAM,YAAY,GAAG,CAAC,CAAC,sBAAsB,EAAE,UAAU;QAC1E,QAAQ,GAAG,CAAC,0BAA0B,SAAS,IAAI;QAEnD,mCAAmC;QACnC,CAAA,GAAA,iIAAA,CAAA,oBAAiB,AAAD,EAAE,UAAU,SAAS,IAAI;QAEzC,OAAO;YAAE,SAAS;YAAM,MAAM,SAAS,IAAI;QAAC;IAC9C,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAE1C,0BAA0B;QAC1B,IAAI,qIAAA,CAAA,UAAK,CAAC,YAAY,CAAC,UAAU,MAAM,QAAQ,EAAE;YAC/C,QAAQ,KAAK,CAAC,mBAAmB;gBAC/B,QAAQ,MAAM,QAAQ,CAAC,MAAM;gBAC7B,MAAM,MAAM,QAAQ,CAAC,IAAI;gBACzB,SAAS,MAAM,QAAQ,CAAC,OAAO;YACjC;YAEA,qDAAqD;YACrD,IAAI,MAAM,QAAQ,CAAC,MAAM,KAAK,KAAK;gBACjC,OAAO;oBACL,SAAS;oBACT,OAAO;gBACT;YACF;YAEA,IAAI,MAAM,QAAQ,CAAC,MAAM,KAAK,KAAK;gBACjC,OAAO;oBACL,SAAS;oBACT,OAAO;gBACT;YACF;YAEA,IAAI,MAAM,QAAQ,CAAC,MAAM,KAAK,KAAK;gBACjC,OAAO;oBACL,SAAS;oBACT,OAAO;gBACT;YACF;YAEA,IAAI,MAAM,QAAQ,CAAC,IAAI,EAAE,SAAS;gBAChC,OAAO;oBACL,SAAS;oBACT,OAAO,MAAM,QAAQ,CAAC,IAAI,CAAC,OAAO;gBACpC;YACF;QACF;QAEA,OAAO;YACL,SAAS;YACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAClD;IACF;AACF;;;IA1iBsB;IAuCA;IA+BA;IA6BA;IAsFA;IAwEA;IAKA;IAaA;IAkEA;IAmBA;IAmBA;IA8BA;IA8BA;IAmDA;;AA1eA,+OAAA;AAuCA,+OAAA;AA+BA,+OAAA;AA6BA,+OAAA;AAsFA,+OAAA;AAwEA,+OAAA;AAKA,+OAAA;AAaA,+OAAA;AAkEA,+OAAA;AAmBA,+OAAA;AAmBA,+OAAA;AA8BA,+OAAA;AA8BA,+OAAA;AAmDA,+OAAA", "debugId": null}}, {"offset": {"line": 2342, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/.next-internal/server/app/_not-found/page/actions.js%20%28server%20actions%20loader%29"], "sourcesContent": ["export {login as '786a5cddf431ffb57c7e3310435586f370d34355f2'} from 'ACTIONS_MODULE0'\nexport {logout as '0025527522b7882fd826ad1ca0dce0d0ce6726c702'} from 'ACTIONS_MODULE0'\nexport {getFriendsList as '40765b7ae1092c3a68fcf914347f0b297373fe950e'} from 'ACTIONS_MODULE1'\nexport {getReceivedFriendRequests as '406f88a7e8a3e3d4c6790948f974957b18aa302d3c'} from 'ACTIONS_MODULE1'\nexport {getSentFriendRequests as '40224cec15feb7e5af4da2d3672710db70fdde3b38'} from 'ACTIONS_MODULE1'\nexport {acceptFriendRequest as '608b8e3c5e2d1e07725287aa26bf15eff03cd8a719'} from 'ACTIONS_MODULE1'\nexport {rejectFriendRequest as '602666daee370c4b7cd541253a6c5b3eda846249eb'} from 'ACTIONS_MODULE1'\nexport {cancelFriendRequest as '60db05fee2171b5299bcb41d3928b3ff3198ff681b'} from 'ACTIONS_MODULE1'\nexport {blockUser as '60e3d50afefa1bcc6a6ce634bce273f4012a1585b6'} from 'ACTIONS_MODULE1'\nexport {unblockUser as '607be4a979876d1de147e5859be475d19de660820e'} from 'ACTIONS_MODULE1'\nexport {getBlockedUsers as '40f7015ada29a7b632b989c001759c1929ea1b50f7'} from 'ACTIONS_MODULE1'\nexport {initiateRegistration as '40e73854e278c980b5cec96ba96518bf359593fae1'} from 'ACTIONS_MODULE0'\nexport {verifyOtp as '606e93584058962effb204a2e915343a3ed5c58fb4'} from 'ACTIONS_MODULE0'\nexport {completeRegistration as '7cd3a5e00c5aa3d7589035e8aa10387789473d26e0'} from 'ACTIONS_MODULE0'\nexport {refreshToken as '00d41e3c118c2c91abbbf555473d2cc2477f0f3f28'} from 'ACTIONS_MODULE0'\nexport {initiateForgotPassword as '4067db1247ecf13e53e76ec48d1280e67e62705ece'} from 'ACTIONS_MODULE0'\nexport {verifyForgotPasswordOtp as '6010c31b664c9e4f4ed8e68a84ae00fb9d01e4af9b'} from 'ACTIONS_MODULE0'\nexport {resetPassword as '6077e13672133007b3a4e8c9c00b04c9dc9718a30c'} from 'ACTIONS_MODULE0'\nexport {changePassword as '70a829aa97efcb1ce5d25cc2ab07599c6328b4104f'} from 'ACTIONS_MODULE0'\nexport {confirmResetPassword as '60f5648f98cb3e7abcea16c8fb2dfdbed59b782834'} from 'ACTIONS_MODULE0'\n"], "names": [], "mappings": ";AAAA;AAEA", "debugId": null}}]}